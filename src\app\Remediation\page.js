import React from 'react' 
import Landing from './components/Landing';
import CybersecurityChallenges from './components/Challenges';
import AutomationCybersecurity from './components/Automation';
import ResultsOutcomes from './components/Result';
import FAQ from './components/FAQSection';
import Benefits from './components/Benefits';

export const metadata = {
  title: "Vulnerability Remediation Platform | Capture The Bug",
  description: "Streamline vulnerability remediation with our automated platform. Track fixes, validate patches, and collaborate with security experts in real-time.",
  keywords: "vulnerability remediation, patch management, security automation, penetration testing remediation, VAPT remediation, cybersecurity platform",
  openGraph: {
    title: "Vulnerability Remediation Platform | Capture The Bug",
    type: "website",
    url: "https://capturethebug.xyz/Remediation",
    description: "Streamline vulnerability remediation with our automated platform. Track fixes, validate patches, and collaborate with security experts in real-time.",
    images: "https://i.postimg.cc/hhQyBHkp/remediation.png",
  },
  twitter: {
    card: "summary_large_image",
    title: "Vulnerability Remediation Platform | Capture The Bug",
    description: "Streamline vulnerability remediation with our automated platform. Track fixes, validate patches, and collaborate with security experts in real-time.",
    images: "https://i.postimg.cc/hhQyBHkp/remediation.png",
  },
};

export default function page() {
  return (
    <div>
      <title>Vulnerability Remediation Platform | Capture The Bug</title>
      <Landing />
      <CybersecurityChallenges />
      <Benefits/>
      <ResultsOutcomes />
      <AutomationCybersecurity />
      <FAQ />
     </div>
  )
}
