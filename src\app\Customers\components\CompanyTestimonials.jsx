"use client"
import React, { useState } from 'react';
import Image from 'next/image';
import { Plus, Minus, ChevronRight } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

export default function CompanyShowcaseWithTestimonials() {
  const [expandedTestimonial, setExpandedTestimonial] = useState(0);

  const toggleTestimonial = (index) => {
    setExpandedTestimonial(expandedTestimonial === index ? null : index);
  };
 
  const testimonials = [
    {
      logo: <div className="bg-white rounded-xl p-3 shadow-sm flex items-center justify-center w-40 h-20"><Image src="/images/yabble_logo.png" alt="Company Logo" width={120} height={60} className="object-contain max-w-full max-h-full" /></div>,
      location: "Auckland, NZ",
      employees: "25-50",
      industry: "AI for Market Research",
      quote: "The team at Capture The Bug have been amazing and super easy to work with. In reality, security testing is ongoing, and needs to be effective yet cost efficient. I love the CTB platform format over traditional pen testing, not sure I could go back!",
      author: "<PERSON>uerin",
      position: "CPO"
    },
        {
      logo: <div className="bg-white rounded-xl p-3 shadow-sm flex items-center justify-center w-40 h-20"><Image src="/images/blackpearl_logo.png" alt="Blackpearl" width={120} height={60} className="object-contain max-w-full max-h-full" /></div>,
      location: "Wellington, NZ",
      employees: "80-100",
      industry: "Cloud services & software",
      quote: "Working with the team at Capture the Bug has been a hugely positive experience. The Penetration Test as a Service model suits us very well. Rather than an annual cadence, we can test in line with our major releases, which allows for more timely remediations and a more efficient use of development time. The Capture the Bug tool itself is very intuitive and lends itself very well to not only the identification of vulnerabilities but also clear paths to resolution. Direct support from the pen testers for each potential issue is provided throughout the re-testing phase, with clear explanations being provided directly to our developers. Well worth giving their tool a test run!",
      author: "Richard Doone",
      position: "VP of Security & Privacy"
    },
    {
      logo: <div className="bg-white rounded-xl p-3 shadow-sm flex items-center justify-center w-40 h-20"><Image src="/images/rafay_logo.png" alt="Rafay" width={120} height={60} className="object-contain max-w-full max-h-full" /></div>,
      location: "Sunnyvale, CA",
      employees: "51-200",
      industry: "Cloud-native / Kubernetes",
      quote: "As a leading Kubernetes company, we understand the importance of securing our data and systems. We engage Capture The Bug's pentesting as a service platform for black box penetration testing. Their ethical hackers provided a thorough security assessment, with clear and concise reporting that included actionable recommendations. We highly recommend their platform for any organization looking to conduct comprehensive penetration testing.",
      author: "Robbie Gill",
      position: "Sr. Director of Engineering at Rafay Systems"
    },
    {
      logo: <div className="bg-white rounded-xl p-3 shadow-sm flex items-center justify-center w-40 h-20"><Image src="/images/EROAD_Logo.webp" alt="EROAD" width={120} height={60} className="object-contain max-w-full max-h-full" /></div>,
      location: "Auckland, NZ",
      employees: "250-500",
      industry: "Transport Technology",
      quote: `Traditional pentesting from independent vendors just didn't scale for a business like ours. Waiting weeks for a final PDF report meant we couldn't act fast enough, and the process always felt disconnected from how our teams actually work. With Capture The Bug's PTaaS platform, that's changed for the better. Now, every time we launch a test - whether it's web, mobile, or infrastructure - we start getting actionable vulnerabilities much faster. It fits right into our existing workflows, so that we can react much more quickly.
The real-time visibility, continuous updates, and integration with our reporting cycles mean I'm no longer chasing static reports before board meetings. We have live insights into what's open. It's given us a much faster, more scalable, and far more transparent way to manage our independent vendor offensive security-without compromising on depth or quality`,
      author: "Jeremy Peaks",
      position: "Director of Engineering - Security"
    },
   
  ];

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
      className="w-full bg-[#F8F4F3] px-4 sm:px-6 py-8 md:py-16"
    >
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col lg:grid lg:grid-cols-12 gap-6 lg:gap-20">
           <motion.div 
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="lg:col-span-4"
          >
            <motion.div 
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="text-green-600 text-lg font-medium mb-3"
            >
              Customer snapshots
            </motion.div>
            <motion.h1 
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="text-3xl sm:text-4xl font-bold text-black mb-4 leading-tight"
            >
              Premium Pentesting {' '}
              <span className="text-blue-700">at a glance</span>
            </motion.h1> 
          </motion.div>

          {/* Right side testimonials */}
          <motion.div 
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="lg:col-span-8 space-y-3"
          >
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 + (index * 0.1) }}
                className="bg-white rounded-lg shadow-sm overflow-hidden"
              >
                <div className="p-4 sm:p-6">
                  {/* Top row - logo, details (on tablet+), and toggle */}
                  <div className="flex justify-between items-start mb-3">
                    <div className="hidden md:flex items-center flex-1 mr-4 gap-6">
  <motion.div whileHover={{ scale: 1.02 }} className="flex items-center gap-3">
    {testimonial.logo}
  </motion.div>
  <div className="grid grid-cols-3 gap-6 flex-1">
    <div className="flex flex-col">
      <span className="text-gray-500 font-medium text-xs">LOCATION</span>
      <span className="truncate">{testimonial.location}</span>
    </div>
    <div className="flex flex-col">
      <span className="text-gray-500 font-medium text-xs">EMPLOYEES</span>
      <span className="truncate">{testimonial.employees}</span>
    </div>
    <div className="flex flex-col -ml-8">
      <span className="text-gray-500 font-medium text-xs">INDUSTRY</span>
      <span className="truncate">{testimonial.industry}</span>
    </div>
  </div>
</div>


                    {/* Mobile layout */}
                    <div className="md:hidden flex flex-col gap-3 flex-1 mr-4">
                      <motion.div 
                        whileHover={{ scale: 1.02 }}
                        className="flex items-center gap-3"
                      >
                        {testimonial.logo}
                      </motion.div>
                    </div>
                    
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => toggleTestimonial(index)}
                      className="p-1 hover:bg-gray-100 rounded-full transition-colors flex-shrink-0"
                    >
                      {expandedTestimonial === index ? (
                        <Minus className="w-5 h-5 text-purple-600" />
                      ) : (
                        <Plus className="w-5 h-5 text-purple-600" />
                      )}
                    </motion.button>
                  </div>

                  {/* Company details - mobile only */}
                  <motion.div 
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                    className="md:hidden flex flex-col gap-y-2 text-sm mt-3"
                  >
                    <div className="flex flex-col">
                      <span className="text-gray-500 font-medium text-xs">LOCATION</span>
                      <span>{testimonial.location}</span>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-gray-500 font-medium text-xs">EMPLOYEES</span>
                      <span>{testimonial.employees}</span>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-gray-500 font-medium text-xs">INDUSTRY</span>
                      <span>{testimonial.industry}</span>
                    </div>
                  </motion.div>

                  {/* Expanded content */}
                  <AnimatePresence>
                    {expandedTestimonial === index && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="mt-4 pt-4 border-t border-gray-100"
                      >
                        <motion.blockquote 
                          initial={{ y: 20, opacity: 0 }}
                          animate={{ y: 0, opacity: 1 }}
                          transition={{ duration: 0.3, delay: 0.1 }}
                          className="text-gray-700 text-sm sm:text-base mb-3 italic"
                        >
                          &ldquo;{testimonial.quote}&rdquo;
                        </motion.blockquote>
                        <motion.div 
                          initial={{ y: 20, opacity: 0 }}
                          animate={{ y: 0, opacity: 1 }}
                          transition={{ duration: 0.3, delay: 0.2 }}
                          className="text-sm"
                        >
                          <div className="font-semibold">{testimonial.author}</div>
                          <div className="text-gray-600">{testimonial.position}</div>
                        </motion.div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
}