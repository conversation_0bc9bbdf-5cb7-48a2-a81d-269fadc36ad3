import { EmailClient, KnownEmailSendStatus } from "@azure/communication-email";

// Check if connection string is available
const connectionString = process.env.NEXT_PUBLIC_COMMUNICATION_SERVICES_CONNECTION_STRING;
let emailClient;

// Only initialize email client if connection string is available
if (connectionString) {
  try {
    emailClient = new EmailClient(connectionString);
  } catch (error) {
    console.error("Failed to initialize email client:", error);
  }
} else {
  console.warn("Communication Services connection string is not defined");
}

// Constants for better maintainability
const POLLER_WAIT_TIME = 10; // seconds
const MAX_POLL_TIME = 180; // 3 minutes maximum polling time
const MAX_RETRIES = 3; // Maximum number of retries for API calls

// Helper function to parse form data
const parseFormData = (body) => {
  try {
    const formData = {};
    const lines = body.split('\n');
    lines.forEach(line => {
      const [key, value] = line.split(':').map(item => item?.trim());
      if (key && value) {
        const formattedKey = key.toLowerCase()
          .replace(/\s+(\w)/g, (_, letter) => letter.toUpperCase())
          .replace(/\s+/g, '');
        formData[formattedKey] = value;
      }
    });
    return formData;
  } catch (error) {
    console.error('Error parsing form data:', error);
    return {};
  }
};

// Helper function to determine form type
const getFormType = (subject) => {
  const lowerSubject = subject.toLowerCase();
  if (lowerSubject.includes('lead')) return 'lead';
  if (lowerSubject.includes('demo')) return 'demo';
  if (lowerSubject.includes('contact')) return 'contact';
  return 'other';
};

// Helper function to store submission with retries
const storeSubmission = async (submissionData) => {
  // In server-side environment, we don't need to store submissions via API
  // as we're already in the server context
  if (typeof window === 'undefined') {
    return true; // Skip API call on server side
  }
  
  let retries = 0;
  while (retries < MAX_RETRIES) {
    try {
      // Use absolute URL with window.location.origin
      const baseUrl = window.location.origin;
      const response = await fetch(`${baseUrl}/api/submissions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return true;
    } catch (error) {
      retries++;
      if (retries === MAX_RETRIES) {
        console.error('Failed to store submission after retries:', error);
        return false;
      }
      // Wait before retrying (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, retries) * 1000));
    }
  }
  return false;
};

export async function sendEmail(subject, body, attachments = []) {
  try {
    // Parse form data and prepare submission
    const formData = parseFormData(body);
    const submissionData = {
      subject,
      body,
      attachments: attachments.length > 0 ? 'Has attachments' : 'No attachments',
      formType: getFormType(subject),
      formData,
      timestamp: new Date().toISOString()
    };

    // Store submission (non-blocking)
    const storePromise = storeSubmission(submissionData);

    // Check if email client is initialized
    if (!emailClient) {
      console.warn("Email client not initialized, skipping email send");
      await storePromise; // Still wait for storage to complete
      return true; // Return success for development environments
    }

    // Prepare email message
    const message = {
      senderAddress: process.env.NEXT_PUBLIC_EMAIL_USER,
      content: {
        subject,
        plainText: body,
        attachments,
      },
      recipients: {
        to: [
          {
            address: process.env.NEXT_PUBLIC_RECIEVER_EMAIL || '<EMAIL>',
            displayName: "Customer Name",
          },
        ],
      },
    };

    // Send email
    const poller = await emailClient.beginSend(message);

    if (!poller.getOperationState().isStarted) {
      throw new Error("Email sending failed to start");
    }

    // Poll for completion with timeout
    let timeElapsed = 0;
    while (!poller.isDone()) {
      await poller.poll();
      await new Promise(resolve => setTimeout(resolve, POLLER_WAIT_TIME * 1000));
      timeElapsed += POLLER_WAIT_TIME;

      if (timeElapsed > MAX_POLL_TIME) {
        throw new Error("Email sending timed out");
      }
    }

    const result = poller.getResult();
    if (result.status !== KnownEmailSendStatus.Succeeded) {
      throw new Error(result.error || "Email sending failed");
    }

    // Wait for storage to complete
    await storePromise;

    console.log(`Successfully sent the email (operation id: ${result.id})`);
    return true;
  } catch (error) {
    console.error("Failed to send email:", error);
    throw error;
  }
}