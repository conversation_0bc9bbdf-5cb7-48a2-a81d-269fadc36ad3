import React from 'react'
import Image from 'next/image';

export default function PartnersList() {
  const logos = [
    { src: '/images/kademi_logo.png', alt: "Kademi Logo" },
    { src: '/images/rafay_logo.png', alt: "Rafay Logo" },
    { src: '/images/partly_logo.png', alt: "Partly Logo" },
    { src: '/images/yabble_logo.png', alt: "Yabble Logo" },
    { src: '/images/parkable_logo.png', alt: "Parkable Logo" },
    { src: '/images/orbit_logo.png', alt: "Orbit Remit Logo" },
    { src: '/images/forsite_logo.png', alt: "Forsite Logo" },
    { src: '/images/cronberry_logo.png', alt: "Cronberry Logo" },
    { src: '/images/lawvu.jpg', alt: "Lawvu Logo" },
    { src: '/images/Bonnet_logo.jpeg', alt: "Bonnet Logo" },
    { src: '/images/Cotiss_Logo.svg', alt: "Cotiss Logo" },
    { src: '/images/Zebpay_logo.png', alt: "Zebpay Logo" },
  ];

  return (
    <section className="bg-gray-50 py-10">
      <div className="flex flex-col items-center md:p-2">
        <h2 className="text-center font-bold text-2xl md:text-3xl md:px-0 px-4 mb-10">
          Trusted by Fast-Growing SaaS Companies Dedicated to Top-Level Security
        </h2>
        <div className="w-full flex flex-wrap justify-center gap-6">
          {logos.map((logo, index) => (
            <div key={index} className="flex items-center justify-center p-4 w-1/3 md:w-1/4">
              <div className="relative w-40 h-16">
                <Image
                  src={logo.src}
                  alt={logo.alt}
                  layout="fill"
                  objectFit="contain"
                  className="rounded-md"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
