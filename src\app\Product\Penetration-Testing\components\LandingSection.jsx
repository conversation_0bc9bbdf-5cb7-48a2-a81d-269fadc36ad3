import Button from "@/app/common/buttons/Button";
import Image from "next/image";
import Link from "next/link";
import React from "react";

export default function LandingSection() {
  return (
    <div className="pt-24 pb-16 md:pt-32 md:pb-20 lg:pt-36 lg:pb-28 px-4 sm:px-6 md:px-8 lg:px-12 xl:px-28 bg-gradient-to-b from-ctb-bg-light to-white">
      <div className="max-w-8xl mx-auto">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-16">
          {/* Content Section */}
          <div className="flex flex-col lg:w-[45%] space-y-8 lg:space-y-10">
            <div className="space-y-6">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-[600] leading-tight">
                <span className="text-tertiary-blue">Continuous  </span>
                <span className="text-secondary-blue">
                  Pentesting
                </span>
                <span className="text-tertiary-blue">, Without  </span>
                <span className="text-secondary-blue">
                  the Wait
                </span>
                
              </h1>
              
              <p className="text-[#240642CC] text-base sm:text-lg md:text-xl leading-relaxed">
               Capture The Bug delivers continuous, developer-friendly penetration testing - built to scale with fast-moving teams.
              </p>
            </div>

            <div className="flex gap-4">
              <Button
                href="/Request-Demo"
                size="lg"
                variant="primary"
                rightIcon={
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                }
                aria-label="Request a demo"
              >
                  Request a demo
              </Button>
            </div>
          </div>

          {/* Image Section */}
          <div className="lg:w-[50%] flex justify-center lg:justify-end mt-6 lg:mt-0">
            <div className="relative rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-1">
              <div className="absolute inset-0 bg-gradient-to-br from-secondary-blue/5 to-primary-blue/10 rounded-xl"></div>
            <Image
              src="/images/ptaas_1.png"
              width={600}
              height={550}
              alt="Illustration of penetration testing service dashboard"
                className="w-full max-w-md sm:max-w-lg lg:max-w-none h-auto relative z-10"
              priority
            />
              <div className="absolute inset-0 bg-gradient-to-t from-tertiary-blue/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 z-20"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
