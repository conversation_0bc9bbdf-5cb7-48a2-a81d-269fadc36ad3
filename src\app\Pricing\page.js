import Pricing from './pricing';
 
export const metadata = {
  title: "Capture The Bug | Pentesting Pricing ",
  description: "Transparent PTaaS pricing tailored for fast-moving SaaS teams and enterprises. Choose a plan and start securing your applications with Capture The Bug.",
  keywords: "pentesting pricing, PTaaS plans, vulnerability assessment cost, application risk testing, offensive testing packages, Capture The Bug pricing, SaaS protection quote, cyber risk evaluation, penetration audit fees, secure development consulting",
  robots: "index, follow",
  icons: {
    icon: '/favicon.ico',
  },
  openGraph: {
    title: "Capture The Bug | Pentesting Pricing  ",
    type: "website",
    url: "https://capturethebug.xyz/Pricing",
    description: "Explore Capture The Bug’s flexible PTaaS pricing built for modern SaaS companies. No hidden fees-just scalable, expert-led testing.",
    images: "https://postimg.cc/sGv7VkYX",
  },
  twitter: {
    card: 'summary_large_image',
    title: "Capture The Bug | Pentesting Pricing",
    description: "View transparent pricing for Capture The Bug’s PTaaS offerings. Flexible packages for growing teams, startups, and enterprises.",
    images: "https://postimg.cc/sGv7VkYX",
  }
};

export default function Page() {
  return <Pricing />;
}