export const metadata = {
  title: "Capture The Bug | How It Works - Fast & Secure PTaaS",
  description: "Discover how Capture The Bug streamlines pentesting. Book in minutes, launch in days. Expert-led PTaaS built for speed, security, and compliance.",
  keywords: "how PTaaS works, Capture The Bug, pentesting platform, on-demand penetration testing, launch pentest fast, cybersecurity testing, web app security, secure PTaaS onboarding",
  robots: "index, follow",
  icons: {
    icon: '/favicon.ico',
  },
  openGraph: {
    title: "Capture The Bug | How It Works - Fast & Secure PTaaS",
    type: "website",
    url: "https://capturethebug.xyz/How-it-works",
    description: "Learn how Capture The Bug enables global tech teams to book on-demand pentests in minutes and go live in under 2 weeks with expert-driven PTaaS.",
    images: "https://i.ibb.co/v6fVfjyk/Screenshot-2025-06-19-130125.png",
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Capture The Bug | How It Works - Fast & Secure PTaaS',
    description: 'Streamline your security testing with Capture The Bug. Book a pentest in minutes and launch in under 2 weeks-fully expert-managed.',
    images: "https://i.ibb.co/v6fVfjyk/Screenshot-2025-06-19-130125.png",
  }
};

import HowItWorks from "./how-it-works";

export default function HomePage() {
  return <HowItWorks />;
}
