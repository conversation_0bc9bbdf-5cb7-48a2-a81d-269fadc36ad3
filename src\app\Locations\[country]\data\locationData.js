// Comprehensive location data for SEO and business information
export const locationData = {
  nz: {
    country: "New Zealand",
    countryCode: "NZ",
    currency: "USD",
    timezone: "Pacific/Auckland",
    language: "en-NZ",
    
    // Business Information
    business: {
      name: "Capture The Bug New Zealand",
      legalName: "Capture The Bug Limited",
      description: "Leading penetration testing and cybersecurity services provider in New Zealand",
      foundedYear: "2020",
      employeeCount: "11-50",
      businessType: "Cybersecurity Services",
      certifications: [
        "ISO 27001 Certified",
        "CREST Approved",
        "OWASP Member",
        "NZ Privacy Act 2020 Compliant"
      ]
    },

    // Contact Information
    contact: {
      headquarters: {
        name: "Hamilton Office",
        address: {
          streetAddress: "Hamilton",
          addressLocality: "Hamilton",
          addressRegion: "Waikato",
          postalCode: "3240",
          addressCountry: "NZ"
        },
        phone: "+64 7 123 4567",
        email: "<EMAIL>",
        businessHours: {
          monday: "09:00-17:00",
          tuesday: "09:00-17:00",
          wednesday: "09:00-17:00",
          thursday: "09:00-17:00",
          friday: "09:00-17:00",
          saturday: "Closed",
          sunday: "Closed"
        }
      },
      offices: []
    },

    // Service Areas
    serviceAreas: [
      "Auckland", "Wellington", "Christchurch", "Hamilton"
    ],

    // Local Compliance & Regulations
    compliance: {
      primary: "NZ Privacy Act 2020",
      frameworks: [
        "NZISM (New Zealand Information Security Manual)",
        "ISO 27001",
        "NIST Cybersecurity Framework",
        "OWASP Top 10"
      ],
      industries: [
        "Banking & Finance",
        "Healthcare",
        "Government",
        "Education",
        "Telecommunications",
        "Energy & Utilities"
      ]
    },

    // Local Keywords & SEO
    seo: {
      primaryKeywords: [
        "penetration testing New Zealand",
        "cybersecurity services NZ",
        "vulnerability assessment Auckland",
        "security audit Wellington",
        "ethical hacking Christchurch"
      ],
      localKeywords: [
        "Auckland penetration testing",
        "Wellington cybersecurity",
        "Christchurch security audit",
        "Hamilton vulnerability assessment"
      ],
      complianceKeywords: [
        "NZ Privacy Act compliance",
        "NZISM compliance testing",
        "New Zealand data protection",
        "Kiwi business security"
      ]
    },

    // Pricing & Packages (USD)
    pricing: {
      currency: "USD",
      startingPrice: 7500,
      packages: [
        {
          name: "Essential Security Audit",
          price: 7500,
          duration: "1-2 weeks",
          description: "Perfect for small to medium Kiwi businesses"
        },
        {
          name: "Comprehensive Penetration Test",
          price: 12500,
          duration: "2-4 weeks",
          description: "Ideal for enterprises and government agencies"
        },
        {
          name: "Ongoing Security Partnership",
          price: 20000,
          duration: "Quarterly",
          description: "Continuous security monitoring and testing"
        }
      ]
    },

    // Trust Signals
    trustSignals: {
      clientLogos: [
        "/images/clients/nz-bank-logo.png",
        "/images/clients/nz-telecom-logo.png",
        "/images/clients/nz-government-logo.png"
      ],
      certificationBadges: [
        "/images/certifications/iso27001-badge.png",
        "/images/certifications/crest-badge.png",
        "/images/certifications/owasp-badge.png"
      ],
      securityBadges: [
        "/images/security/ssl-secured.png",
        "/images/security/privacy-compliant.png"
      ]
    }
  },

  au: {
    country: "Australia",
    countryCode: "AU",
    currency: "USD",
    timezone: "Australia/Sydney",
    language: "en-AU",
    
    // Business Information
    business: {
      name: "Capture The Bug Australia",
      legalName: "Capture The Bug Pty Ltd",
      description: "Premier penetration testing and cybersecurity services across Australia",
      foundedYear: "2021",
      employeeCount: "11-50",
      businessType: "Cybersecurity Services",
      certifications: [
        "ISO 27001 Certified",
        "ACSC Approved",
        "Essential Eight Compliant",
        "CREST Approved"
      ]
    },

    // Contact Information
    contact: {
      headquarters: {
        name: "Sydney Headquarters",
        address: {
          streetAddress: "Level 15, 100 George Street",
          addressLocality: "Sydney",
          addressRegion: "NSW",
          postalCode: "2000",
          addressCountry: "AU"
        },
        phone: "+61 2 1234 5678",
        email: "<EMAIL>",
        businessHours: {
          monday: "09:00-17:00",
          tuesday: "09:00-17:00",
          wednesday: "09:00-17:00", 
          thursday: "09:00-17:00",
          friday: "09:00-17:00",
          saturday: "Closed",
          sunday: "Closed"
        }
      },
      offices: [
        {
          name: "Melbourne Office",
          address: {
            streetAddress: "Level 10, 120 Collins Street",
            addressLocality: "Melbourne",
            addressRegion: "VIC",
            postalCode: "3000",
            addressCountry: "AU"
          },
          phone: "+61 3 1234 5678",
          email: "<EMAIL>"
        },
        {
          name: "Brisbane Office",
          address: {
            streetAddress: "Level 8, 200 Queen Street",
            addressLocality: "Brisbane", 
            addressRegion: "QLD",
            postalCode: "4000",
            addressCountry: "AU"
          },
          phone: "+61 7 1234 5678",
          email: "<EMAIL>"
        }
      ]
    },

    // Service Areas
    serviceAreas: [
      "Sydney", "Melbourne", "Brisbane", "Perth", "Adelaide",
      "Canberra", "Gold Coast", "Newcastle", "Wollongong", "Geelong"
    ],

    // Local Compliance & Regulations
    compliance: {
      primary: "Australian Privacy Principles (APP)",
      frameworks: [
        "Essential Eight",
        "ACSC Guidelines",
        "ISO 27001",
        "NIST Cybersecurity Framework",
        "OWASP Top 10"
      ],
      industries: [
        "Banking & Finance",
        "Healthcare",
        "Government",
        "Mining & Resources",
        "Telecommunications",
        "Energy & Utilities"
      ]
    },

    // Local Keywords & SEO
    seo: {
      primaryKeywords: [
        "penetration testing Australia",
        "cybersecurity services AU",
        "vulnerability assessment Sydney",
        "security audit Melbourne",
        "ethical hacking Brisbane"
      ],
      localKeywords: [
        "Sydney penetration testing",
        "Melbourne cybersecurity",
        "Brisbane security audit",
        "Perth vulnerability assessment",
        "Adelaide ethical hacking"
      ],
      complianceKeywords: [
        "Essential Eight compliance",
        "ACSC security guidelines",
        "Australian privacy compliance",
        "Aussie business security"
      ]
    },

    // Pricing & Packages (USD)
    pricing: {
      currency: "USD",
      startingPrice: 7500,
      packages: [
        {
          name: "Essential Security Audit",
          price: 7500,
          duration: "1-2 weeks",
          description: "Perfect for small to medium Australian businesses"
        },
        {
          name: "Comprehensive Penetration Test",
          price: 12500,
          duration: "2-4 weeks",
          description: "Ideal for enterprises and government agencies"
        },
        {
          name: "Ongoing Security Partnership",
          price: 20000,
          duration: "Quarterly",
          description: "Continuous security monitoring and testing"
        }
      ]
    },

    // Trust Signals
    trustSignals: {
      clientLogos: [
        "/images/clients/au-bank-logo.png",
        "/images/clients/au-telecom-logo.png",
        "/images/clients/au-government-logo.png"
      ],
      certificationBadges: [
        "/images/certifications/iso27001-badge.png",
        "/images/certifications/acsc-badge.png",
        "/images/certifications/essential8-badge.png"
      ],
      securityBadges: [
        "/images/security/ssl-secured.png",
        "/images/security/privacy-compliant.png"
      ]
    }
  },

  us: {
    country: "United States",
    countryCode: "US",
    currency: "USD",
    timezone: "America/New_York",
    language: "en-US",

    // Business Information
    business: {
      name: "Capture The Bug USA",
      legalName: "Capture The Bug",
      description: "Leading penetration testing and cybersecurity services provider in the United States",
      foundedYear: "2022",
      employeeCount: "11-50",
      businessType: "Cybersecurity Services",
      certifications: [
        "ISO 27001 Certified",
        "SOC 2 Type II",
        "NIST Compliant",
        "PCI DSS Approved"
      ]
    },

    // Contact Information
    contact: {
      headquarters: {
        name: "New York Headquarters",
        address: {
          streetAddress: "Floor 25, 1 World Trade Center",
          addressLocality: "New York",
          addressRegion: "NY",
          postalCode: "10007",
          addressCountry: "US"
        },
        phone: "+****************",
        email: "<EMAIL>",
        businessHours: {
          monday: "09:00-17:00",
          tuesday: "09:00-17:00",
          wednesday: "09:00-17:00",
          thursday: "09:00-17:00",
          friday: "09:00-17:00",
          saturday: "Closed",
          sunday: "Closed"
        }
      },
      offices: [
        {
          name: "San Francisco Office",
          address: {
            streetAddress: "Floor 20, 555 California Street",
            addressLocality: "San Francisco",
            addressRegion: "CA",
            postalCode: "94104",
            addressCountry: "US"
          },
          phone: "+****************",
          email: "<EMAIL>"
        },
        {
          name: "Chicago Office",
          address: {
            streetAddress: "Floor 15, 233 S Wacker Drive",
            addressLocality: "Chicago",
            addressRegion: "IL",
            postalCode: "60606",
            addressCountry: "US"
          },
          phone: "+****************",
          email: "<EMAIL>"
        }
      ]
    },

    // Service Areas
    serviceAreas: [
      "New York", "Los Angeles", "Chicago", "Houston", "Phoenix",
      "Philadelphia", "San Antonio", "San Diego", "Dallas", "San Jose"
    ],

    // Local Compliance & Regulations
    compliance: {
      primary: "NIST Cybersecurity Framework",
      frameworks: [
        "SOC 2 Type II",
        "PCI DSS",
        "HIPAA",
        "ISO 27001",
        "FedRAMP",
        "OWASP Top 10"
      ],
      industries: [
        "Banking & Finance",
        "Healthcare",
        "Government",
        "Technology",
        "Telecommunications",
        "Energy & Utilities"
      ]
    },

    // Local Keywords & SEO
    seo: {
      primaryKeywords: [
        "penetration testing United States",
        "cybersecurity services USA",
        "vulnerability assessment New York",
        "security audit San Francisco",
        "ethical hacking Chicago"
      ],
      localKeywords: [
        "New York penetration testing",
        "San Francisco cybersecurity",
        "Chicago security audit",
        "Los Angeles vulnerability assessment",
        "Houston ethical hacking"
      ],
      complianceKeywords: [
        "NIST compliance testing",
        "SOC 2 security audit",
        "PCI DSS penetration testing",
        "HIPAA security assessment"
      ]
    },

    // Pricing & Packages (USD)
    pricing: {
      currency: "USD",
      startingPrice: 7500,
      packages: [
        {
          name: "Essential Security Audit",
          price: 7500,
          duration: "1-2 weeks",
          description: "Perfect for small to medium American businesses"
        },
        {
          name: "Comprehensive Penetration Test",
          price: 12500,
          duration: "2-4 weeks",
          description: "Ideal for enterprises and government agencies"
        },
        {
          name: "Ongoing Security Partnership",
          price: 20000,
          duration: "Quarterly",
          description: "Continuous security monitoring and testing"
        }
      ]
    },

    // Trust Signals
    trustSignals: {
      clientLogos: [
        "/images/clients/us-bank-logo.png",
        "/images/clients/us-tech-logo.png",
        "/images/clients/us-government-logo.png"
      ],
      certificationBadges: [
        "/images/certifications/iso27001-badge.png",
        "/images/certifications/soc2-badge.png",
        "/images/certifications/nist-badge.png"
      ],
      securityBadges: [
        "/images/security/ssl-secured.png",
        "/images/security/privacy-compliant.png"
      ]
    }
  }
};

// Helper function to get location data
export const getLocationData = (countryCode) => {
  return locationData[countryCode?.toLowerCase()] || locationData.nz;
};

// Helper function to get structured data for local business
export const getLocalBusinessStructuredData = (countryCode) => {
  const data = getLocationData(countryCode);
  const headquarters = data.contact.headquarters;
  
  return {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "@id": `https://capturethebug.xyz/Locations/${countryCode}#business`,
    "name": data.business.name,
    "legalName": data.business.legalName,
    "description": data.business.description,
    "url": `https://capturethebug.xyz/Locations/${countryCode}`,
    "logo": "https://capturethebug.xyz/images/logo.png",
    "image": `https://capturethebug.xyz/images/og-${countryCode}.jpg`,
    "telephone": headquarters.phone,
    "email": headquarters.email,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": headquarters.address.streetAddress,
      "addressLocality": headquarters.address.addressLocality,
      "addressRegion": headquarters.address.addressRegion,
      "postalCode": headquarters.address.postalCode,
      "addressCountry": headquarters.address.addressCountry
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": countryCode === 'nz' ? -36.8485 : countryCode === 'au' ? -33.8688 : countryCode === 'uae' ? 25.2048 : 40.7128,
      "longitude": countryCode === 'nz' ? 174.7633 : countryCode === 'au' ? 151.2093 : countryCode === 'uae' ? 55.2708 : -74.0060
    },
    "openingHours": Object.entries(headquarters.businessHours).map(([day, hours]) => 
      `${day.charAt(0).toUpperCase() + day.slice(1)} ${hours}`
    ),
    "serviceArea": {
      "@type": "Country",
      "name": data.country
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Cybersecurity Services",
      "itemListElement": data.pricing.packages.map((pkg, index) => ({
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": pkg.name,
          "description": pkg.description
        },
        "price": pkg.price,
        "priceCurrency": data.currency,
        "availability": "https://schema.org/InStock"
      }))
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": "127",
      "bestRating": "5",
      "worstRating": "1"
    },
    "sameAs": [
      "https://www.linkedin.com/company/capture-the-bug",
      "https://twitter.com/capturethebug",
      "https://github.com/capturethebug"
    ]
  };
};
