"use client"
import React from 'react';
import Button from '../../common/buttons/Button';
import { ShieldCheckIcon, CheckBadgeIcon, PuzzlePieceIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

const teamAdvantages = [
  {
    id: "COMPLIANCE",
    title: "COMPLIANCE",
    description: "Meet SOC 2, ISO 27001, HIPAA, and GDPR pentesting requirements with audit-ready reports.",
    icon: <ShieldCheckIcon className="w-8 h-8 text-primary-blue" />,
    gridClass: "col-span-1 row-span-1"
  },
  {
    id: "VALIDATION",
    title: "VALIDATION",
    description: "Get expert-verified vulnerability insights that go beyond scanner results - complete with reproducible PoCs.",
    icon: <CheckBadgeIcon className="w-8 h-8 text-primary-blue" />,
    gridClass: "col-span-1 row-span-1"
  },
  {
    id: "INTEGRATIONS",
    title: "INTEGRATIONS",
    description: "Streamline remediation by syncing findings with <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, or <PERSON>lack in real time.",
    icon: <PuzzlePieceIcon className="w-8 h-8 text-primary-blue" />,
    gridClass: "col-span-1 row-span-1"
  },
  {
    id: "RETESTING",
    title: "RETESTING",
    description: "Close the loop with retests and fix verification - all tracked in a continuous pentesting workflow.",
    icon: <ArrowPathIcon className="w-8 h-8 text-primary-blue" />,
    gridClass: "col-span-1 row-span-1"
  }
];

export default function CallToAction() {
  // Arrow icon for button
  const arrowIcon = (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      className="h-5 w-5" 
      fill="none" 
      viewBox="0 0 24 24" 
      stroke="currentColor"
    >
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
    </svg>
  );

  return (
    <section className="w-full bg-gradient-to-b from-ctb-bg-light to-white py-16 sm:py-20 md:py-24">
      <div className="max-w-screen-2xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
        {/* Header Section */}
        <div className="mb-12 sm:mb-16 md:mb-20">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-[500] text-center mb-4 leading-tight text-gray-900">
            Built for Modern Security Teams -<span className="text-primary-blue font-[600]"> More Than Just a Pentest</span>
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-secondary-blue max-w-4xl mx-auto leading-relaxed text-center mb-4 font-[500]">
            Capture The Bug delivers end-to-end Penetration Testing as a Service (PTaaS) to help you ship faster, stay secure, and satisfy compliance - without the overhead.
          </p>
        </div>

        {/* Responsive Grid */}
        <div className="md:px-4 lg:px-8 xl:px-16">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 md:gap-6 items-stretch w-full">
          {teamAdvantages.map((advantage, index) => (
            <div
              key={advantage.id}
                className="group relative flex flex-col justify-between bg-white rounded-2xl border border-gray-100 shadow-[0_10px_35px_-15px_rgba(8,53,167,0.08)] hover:shadow-[0_25px_65px_-12px_rgba(8,53,167,0.25)] transition-all duration-500 ease-in-out p-5 md:p-4 min-h-[220px] md:min-h-[200px] overflow-hidden transform hover:-translate-y-1 hover:border-primary-blue/20"
            >
                {/* Premium Background Effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary-blue/[0.03] via-white to-white opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                
                {/* Top right corner decorative element */}
                <div className="absolute -top-12 -right-12 w-20 h-20 rounded-full bg-gradient-to-br from-primary-blue/10 to-secondary-blue/5 group-hover:from-primary-blue/20 group-hover:to-secondary-blue/10 transition-colors duration-700" />
                
                {/* Animated glow effect */}
                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[120%] h-[120%] rounded-full bg-primary-blue/5 blur-3xl opacity-0 group-hover:opacity-30 scale-0 group-hover:scale-100 transition-all duration-700" />
                
                {/* Left border accent */}
                <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-primary-blue/20 via-primary-blue/40 to-secondary-blue/20 transform origin-bottom scale-y-0 group-hover:scale-y-100 transition-transform duration-500 ease-out rounded-l-xl" />
              
                {/* Content Container with relative positioning */}
              <div className="relative z-10 flex flex-col h-full">
                  {/* Icon and Title Container */}
                  <div className="flex items-start gap-4 mb-4">
                    {/* Premium Icon Container */}
                    <div className="p-2 bg-gradient-to-br from-primary-blue/5 to-primary-blue/10 rounded-xl group-hover:from-primary-blue/10 group-hover:to-primary-blue/20 transition-colors duration-500 shadow-sm group-hover:shadow-md relative overflow-hidden">
                      {/* Icon Background Animation */}
                      <div className="absolute inset-0 bg-gradient-to-tr from-primary-blue/0 to-primary-blue/20 opacity-0 group-hover:opacity-100 transition-opacity duration-700" />
                      
                      {/* Animated dots in icon background */}
                      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700 overflow-hidden">
                        {[...Array(3)].map((_, i) => (
                          <div 
                            key={i} 
                            className="absolute w-0.5 h-0.5 rounded-full bg-primary-blue/30"
                            style={{
                              top: `${10 + (i * 30)}%`,
                              left: `${10 + (i * 30)}%`,
                              animation: `float${i} 3s infinite ease-in-out ${i * 0.5}s`
                            }}
                          />
                        ))}
                      </div>
                      
                      {/* The actual icon with enhanced transitions */}
                      {React.cloneElement(advantage.icon, { 
                        className: 'w-6 h-6 text-primary-blue group-hover:text-secondary-blue transition-all duration-500 transform group-hover:scale-110 relative z-10' 
                      })}
                    </div>
                    
                    {/* Title with animated underline */}
                    <div className="overflow-hidden pt-1">
                      <h3 className="text-lg font-bold text-secondary-blue group-hover:text-primary-blue transition-colors duration-500 transform translate-y-0 group-hover:-translate-y-1">{advantage.title}</h3>
                      <div className="h-0.5 w-0 bg-gradient-to-r from-primary-blue to-secondary-blue group-hover:w-full transition-all duration-700 ease-out mt-1"></div>
                    </div>
                  </div>
                  
                  {/* Description with enhanced typography */}
                  <p className="text-gray-600 leading-relaxed text-sm md:text-base flex-1 group-hover:text-gray-700 transition-colors duration-500 relative z-10">{advantage.description}</p>
                  
                  {/* Subtle Bottom Pattern */}
                  <div className="absolute right-4 bottom-4 w-20 h-20 opacity-0 group-hover:opacity-10 transition-opacity duration-1000">
                    <div className="w-full h-full relative">
                      {[...Array(16)].map((_, i) => (
                        <div 
                          key={i} 
                          className="absolute w-0.5 h-0.5 rounded-full bg-primary-blue"
                          style={{
                            top: `${Math.floor(i / 4) * 28}%`,
                            left: `${(i % 4) * 28}%`,
                            opacity: ((i % 4) + (Math.floor(i / 4) % 3)) % 3 === 0 ? 0.8 : 0.4
                          }}
                        />
                      ))}
                    </div>
                  </div>
                </div>
                
                {/* Subtle bottom accent line */}
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-blue/80 to-secondary-blue/80 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-700 ease-out rounded-b-xl opacity-60"></div>
              </div>
            ))}
              </div>
            </div>
        
        {/* CTA Button */}
        <div className="mt-16 sm:mt-20 text-center">
          <Button 
            href="/Request-Demo" 
            variant="primary"
            size="lg"
            rightIcon={arrowIcon}
          >
            Schedule a Demo
          </Button>
        </div>
      </div>
      
      {/* Add global animation keyframes */}
      <style jsx global>{`
        @keyframes float0 {
          0%, 100% { transform: translateY(0) translateX(0); }
          50% { transform: translateY(-5px) translateX(3px); }
        }
        @keyframes float1 {
          0%, 100% { transform: translateY(0) translateX(0); }
          50% { transform: translateY(5px) translateX(-3px); }
        }
        @keyframes float2 {
          0%, 100% { transform: translateY(0) translateX(0); }
          50% { transform: translateY(-7px) translateX(-2px); }
        }
      `}</style>
    </section>
  );
}
