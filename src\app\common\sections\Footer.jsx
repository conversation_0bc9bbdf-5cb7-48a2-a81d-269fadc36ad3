import { <PERSON><PERSON>in , Phone, Mail, MapPin, ArrowRight } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { FaXTwitter } from 'react-icons/fa6';

const Footer = () => {
  return (
    <div className="relative bg-transparent z-10"> 
      <section className="relative z-10 w-[90%] sm:w-4/5 mx-auto overflow-hidden rounded-xl shadow-2xl transform translate-y-1/2 -mt-6 sm:-mt-16 md:-mt-24">
        <div className="bg-[#0835A7] relative">
          
          <div className="relative py-6 sm:py-8 lg:py-16 px-6 sm:px-8 lg:px-10">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 sm:gap-6 text-center sm:text-left">
              <div className="flex-1">
                <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-white mb-2 sm:mb-5 leading-tight">
Security that works like you do.
                </h2>
                <p className="text-sm lg:text-base text-white/80 max-w-xl mb-4 sm:mb-0">
Flexible, scalable PTaaS for modern product teams.
                </p>
              </div>

              <div>
                <Link
                  href="/Request-Demo"
                  className="inline-flex items-center justify-center gap-2 bg-white hover:bg-white/95 text-primary-blue font-medium px-6 py-3 sm:px-7 sm:py-3.5 rounded-lg transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg group relative overflow-hidden"
                >
                  <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-primary-blue/0 via-primary-blue/5 to-primary-blue/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></span>
                  <span className="relative">Request a demo</span>
                  <ArrowRight className="w-4 h-4 relative transition-transform group-hover:translate-x-1" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

       <footer className="w-full bg-[#010D2C] text-white pt-32 sm:pt-28 lg:pt-32 ">
        <div className="max-w-screen-2xl mx-auto px-3 sm:px-4 md:px-6 lg:px-12 xl:ml-10 ">
          <div className="py-8 sm:py-10 border-b border-white/10">
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 sm:gap-8">
              
               <div className="lg:col-span-4 text-center sm:text-left mx-5">
               <div className="mb-4 sm:mb-6">
  <Image 
    src="/images/white_logo.png" 
    alt="Logo" 
    width={160}              
    height={56}                 
    className="h-10 sm:h-12 lg:h-14 w-auto mx-auto sm:mx-0" 
  />
</div>

                <p className="text-white/70 text-sm leading-6 mb-4 sm:mb-6 max-w-md mx-auto sm:mx-0">
                  Our penetration testing platform provides fast-moving SaaS companies with a cost-efficient solution to achieve cybersecurity standards and ensure compliance.
                </p>
                
                {/* Contact Information */}
                <div className="mb-4 sm:mb-6">
                   <div className="space-y-3 sm:space-y-4">
                    <div className="flex items-center gap-3 text-white/70 text-sm">
                      <MapPin size={15} className="text-white/50 flex-shrink-0" />
                      <span>New Zealand - Waikato, Hamilton</span>
                    </div>
                    <div className="flex items-center gap-3 text-white/70 text-sm">
                      <Mail size={15} className="text-white/50 flex-shrink-0" />
                      <a href="mailto:<EMAIL>" className="hover:text-white transition-colors break-all sm:break-normal"><EMAIL></a>
                    </div>
                    <div className="flex items-center gap-3 text-white/70 text-sm">
                      <Phone size={15} className="text-white/50 flex-shrink-0" />
                      <a href="tel:+64221994320" className="hover:text-white transition-colors">+64 22 199 4320</a>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-center sm:justify-start space-x-4">
                  <a href="https://www.linkedin.com/company/capture-the-bug/" className="text-white/70 hover:text-white transition-colors">
                    <Linkedin size={20}/>
                  </a>
                  <a href="https://x.com/Capturethebugs" className="text-white/70 hover:text-white text-sm transition-colors">
                    <FaXTwitter  size={20}/>
                  </a>
                </div>
              </div>

              {/* Right Side - Navigation Links in Two Rows */}
              <div className="lg:col-span-8">
                
                {/* First Row */}
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 sm:gap-8 mb-8">

                  {/* Product */}
                  <div>
                    <h3 className="text-white font-semibold text-sm mb-3 sm:mb-4">Product</h3>
                    <ul className="space-y-2 sm:space-y-3">                      
                      <li><a href="/How-it-works" className="text-white/70 hover:text-white text-sm transition-colors">How it works</a></li>
                      <li><a href="/Remediation" className="text-white/70 hover:text-white text-sm transition-colors">Remediation</a></li>
                      <li><a href="/Blogs" className="text-white/70 hover:text-white text-sm transition-colors">Blogs</a></li>
                      <li><a href="/Customers" className="text-white/70 hover:text-white text-sm transition-colors">Customer</a></li>
                      <li><a href="/Pricing" className="text-white/70 hover:text-white text-sm transition-colors">Pricing</a></li>
                      <li><a href="/Request-Demo" className="text-white/70 hover:text-white text-sm transition-colors">Request demo</a></li>
                    </ul>
                  </div>

                  {/* Company */}
                  <div>
                    <h3 className="text-white font-semibold text-sm mb-3 sm:mb-4">Company</h3>
                    <ul className="space-y-2 sm:space-y-3">
                      <li><a href="/Company/About-Us" className="text-white/70 hover:text-white text-sm transition-colors">About Us</a></li>
                      <li><a href="/Company/Press" className="text-white/70 hover:text-white text-sm transition-colors">Press</a></li>
                      <li><a href="/Company/Partner-With-Us" className="text-white/70 hover:text-white text-sm transition-colors">Partner With Us</a></li>
                      <li><a href="https://businessdesk.co.nz/article/technology/the-business-of-tech-podcast-startups-the-budget-and-bounties-for-bugs" className="text-white/70 hover:text-white text-sm transition-colors">Tech Podcast</a></li>
                      <li><a href="/Company/Contact-Us" className="text-white/70 hover:text-white text-sm transition-colors">Contact Us</a></li>
                      <li><a href="https://capturethebug.safebase.us/" className="text-white/70 hover:text-white text-sm transition-colors">Trust Center</a></li>
                     </ul>
                  </div>

                  {/* Services */}
                 <div>
                    <h3 className="text-white font-semibold text-sm mb-3 sm:mb-4">Services</h3>
                    <ul className="space-y-2 sm:space-y-3">
                      <li><a href="/Services/Web-app" className="text-white/70 hover:text-white text-sm transition-colors">Web Application</a></li>
                      <li><a href="/Services/Mobile-app" className="text-white/70 hover:text-white text-sm transition-colors">Mobile Application</a></li>
                      <li><a href="/Services/Network-pentest" className="text-white/70 hover:text-white text-sm transition-colors">Network Infrastructure</a></li>
                      <li><a href="/Services/API-pentest" className="text-white/70 hover:text-white text-sm transition-colors">API Testing</a></li>
                    </ul>
                  </div>

                  {/* Locations */}
                  <div>
                    <h3 className="text-white font-semibold text-sm mb-3 sm:mb-4">Locations</h3>
                    <ul className="space-y-2 sm:space-y-3">
                      <li><a href="/Locations" className="text-white/70 hover:text-white text-sm transition-colors">All Locations</a></li>
                      <li><a href="/Locations/nz" className="text-white/70 hover:text-white text-sm transition-colors">New Zealand</a></li>
                      <li><a href="/Locations/au" className="text-white/70 hover:text-white text-sm transition-colors">Australia</a></li>
                      <li><a href="/Locations/us" className="text-white/70 hover:text-white text-sm transition-colors">United States</a></li>
                    </ul>
                  </div>

                  {/* Industries */}
                  <div>
                    <h3 className="text-white font-semibold text-sm mb-3 sm:mb-4">Industries</h3>
                    <ul className="space-y-2 sm:space-y-3">
                      <li><a href="/Industries/Automotive&Transportation" className="text-white/70 hover:text-white text-sm transition-colors">Automotive & Transportation</a></li>
                      <li><a href="/Industries/Banking&FinancialServices" className="text-white/70 hover:text-white text-sm transition-colors">Banking & Financial Services</a></li>
                      <li><a href="/Industries/Ecommerce&Retail" className="text-white/70 hover:text-white text-sm transition-colors">E-commerce & Retail</a></li>
                      <li><a href="/Industries/Education&EdTech" className="text-white/70 hover:text-white text-sm transition-colors">Education & EdTech</a></li>
                      <li><a href="/Industries/AISecurity&Infrastructure" className="text-white/70 hover:text-white text-sm transition-colors">AI Security & Infrastructure</a></li>
                      <li><a href="/Industries/Health&SafetyTech" className="text-white/70 hover:text-white text-sm transition-colors">Health & Safety Tech</a></li>
                      <li><a href="/Industries/Legal&Compliance" className="text-white/70 hover:text-white text-sm transition-colors">Legal & Compliance</a></li>
                      <li><a href="/Industries/SaaS&CloudPlatforms" className="text-white/70 hover:text-white text-sm transition-colors">SaaS & Cloud Platforms</a></li>
                      <li><a href="/Industries/Telecom&Media" className="text-white/70 hover:text-white text-sm transition-colors">Telecom & Media</a></li>
                    </ul>
                  </div>
                </div>

                {/* Second Row */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8">
                  {/* Company Size */}
                  <div>
                    <h3 className="text-white font-semibold text-sm mb-3 sm:mb-4">Company Size</h3>
                    <ul className="space-y-2 sm:space-y-3">
                      <li><a href="/Company-size/Start-Up" className="text-white/70 hover:text-white text-sm transition-colors">Start Up</a></li>
                      <li><a href="/Company-size/Growing-Team" className="text-white/70 hover:text-white text-sm transition-colors">Growing Team</a></li>
                      <li><a href="/Company-size/Enterprise" className="text-white/70 hover:text-white text-sm transition-colors">Enterprise</a></li>
                    </ul>
                  </div>

                  {/* Resources */}
                  <div>
                    <h3 className="text-white font-semibold text-sm mb-3 sm:mb-4">Resources</h3>
                    <ul className="space-y-2 sm:space-y-3">
                       <li><a href="/Useful-Links/Customer-Terms&Conditions" className="text-white/70 hover:text-white text-sm transition-colors">Customer Terms & Conditions</a></li>
                      <li><a href="/Useful-Links/Disclosure-Policy" className="text-white/70 hover:text-white text-sm transition-colors">Disclosure Policy</a></li>
                      <li><a href="/Useful-Links/Privacy-Policy" className="text-white/70 hover:text-white text-sm transition-colors">Privacy Policy</a></li>
                      <li><a href="/Useful-Links/CTB-T&C" className="text-white/70 hover:text-white text-sm transition-colors">CTB Terms & Conditions</a></li>
                    </ul>
                  </div>

                  {/* Empty columns for consistent spacing */}
                  <div></div>
                  <div></div>

                </div>
              </div>

            </div>
      

 
            <div className="mt-6 sm:mt-8 text-center">
               
              <p className="text-white/70 text-sm mt-3 sm:mt-4">
                © {new Date().getFullYear()} Capture The Bug. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Footer;