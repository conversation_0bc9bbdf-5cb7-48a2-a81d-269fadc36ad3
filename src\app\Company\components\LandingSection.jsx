import Button from "@/app/common/buttons/Button";
import Image from "next/image";
import Link from "next/link";
import React from "react";

export default function LandingSection() {
  return (
    <section className="md:p-12 p-8 flex md:flex-row flex-col justify-between">
      <article className="Content md:w-[50%] md:gap-6 gap-4 flex flex-col">
        <header className="Title md:text-5xl text-2xl font-bold bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent md:py-4">
          Advanced Penetration Testing for Agile SaaS Companies
        </header>

        <p className="subTitle2 leading-8 md:pr-10 text-slate-600 text-sm md:text-lg">
          Secure your SaaS with our PTaaS platform: test thoroughly, collaborate closely, and gain insights.
        </p>

        <div className="button flex gap-2">
          <Button
            href="/signup"
            variant="primary"
            className="px-8 py-2 text-[10px] md:text-sm"
          >
            Get Started
          </Button>
          <Button
            href="/Request-Demo"
            variant="secondary"
            className="px-8 py-2 text-[10px] md:text-sm"
          >
            Request a demo
          </Button>
        </div>
      </article>

      <div className="Image md:mt-0 mt-10">
        <Image 
          src="/images/Company1.png" 
          width={550} 
          height={500} 
          alt="Image of SaaS company concept"
        />
      </div>
    </section>
  );
}
