

"use client";
import { motion } from 'framer-motion';
import React from 'react' 
import Features from './components/Features'
import Introduction from './components/Introduction' 
import AttackSurface from './components/AttackSurface' 
import FAQSection from './components/FAQSection' 
import PartnersList from './components/PartnersList'
import Testimonials from '@/app/Home/components/Testimonials'
import PTaaSSection from './components/PTaaSSection' 


export default function page() {
  return (
    <div className="min-h-screen bg-white">
      <title>Capture The Bug | Penetration Testing</title>
      
      <main>
      {/* <LandingSection/> */}
       <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
      <PTaaSSection/>
       <PartnersList/>
       </motion.div>
      {/* <PTaaSSectionCopy/> */}
      <Introduction/> 
      <Features/> 
      <AttackSurface/>
      <Testimonials/>
      <FAQSection/> 
      </motion.div>
      </main>
    </div>
  )
}
