"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import Button from "../../../common/buttons/Button";
import { motion, useReducedMotion } from "framer-motion";
import { ArrowRight } from "lucide-react";

export default function LandingSection() {
  // State to track if component has mounted
  const [isMounted, setIsMounted] = useState(false);
  
  // Hook to detect if user prefers reduced motion
  const prefersReducedMotion = useReducedMotion();
  
  // Animation controls
  const shouldAnimate = isMounted && !prefersReducedMotion;
  
  // Ensure component is mounted before animations
  useEffect(() => {
    setIsMounted(true);
    
    // Safety timeout to force content visibility if animations get stuck
    const safetyTimeout = setTimeout(() => {
      const heroContent = document.getElementById('startup-hero-content');
      if (heroContent) {
        heroContent.style.opacity = '1';
      }
    }, 1500); // Force visibility after 1.5s
    
    return () => clearTimeout(safetyTimeout);
  }, []);
  
  // Animation variants
  const containerAnimation = shouldAnimate 
    ? { 
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        transition: { duration: 0.3 }
      } 
    : { initial: { opacity: 1 } };
    
  const textAnimation = shouldAnimate
    ? {
        initial: { opacity: 0, y: -15 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.4 }
      }
    : { initial: { opacity: 1 } };
    
  const paragraphAnimation = shouldAnimate
    ? {
        initial: { opacity: 0, y: 10 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.3 }
      }
    : { initial: { opacity: 1 } };
    
  const imageAnimation = shouldAnimate
    ? {
        initial: { opacity: 0, x: 20 },
        animate: { opacity: 1, x: 0 },
        transition: { duration: 0.3 }
      }
    : { initial: { opacity: 1 } };

  const arrowIcon = (
    <motion.div
      animate={shouldAnimate ? { x: [0, 4, 0] } : {}}
      transition={shouldAnimate ? { duration: 1.5, repeat: Infinity } : {}}
    >
      <ArrowRight className="h-4 w-4" />
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-[#F8F5F7] flex items-center">
      <motion.div 
        id="startup-hero-content"
        {...containerAnimation}
        className="w-full"
      >
        <div className="w-full py-16 sm:py-24 lg:py-26 grid grid-cols-1 lg:grid-cols-2 gap-y-8 gap-x-4 items-center">
          {/* Left Hero Section */}
          <div className="px-4 sm:px-6 md:px-10 lg:pl-14 xl:pl-24 space-y-6 sm:space-y-8 z-10 max-w-full lg:max-w-[800px] w-full mx-auto">
            <div className="space-y-4 sm:space-y-6 md:space-y-8">
              <h1 className="font-bold leading-tight text-ctb-blue-150 text-left tracking-tight">
                <motion.span 
                  {...textAnimation}
                  className="block mb-1 sm:mb-2 text-3xl xs:text-4xl sm:text-4xl md:text-5xl lg:text-5xl"
                >
                  Capture The Bug for
                </motion.span>
                <motion.span 
                  {...textAnimation}
                  transition={shouldAnimate ? { duration: 0.4, delay: 0.1 } : {}}
                  className="block mb-1 sm:mb-2 text-3xl xs:text-4xl sm:text-4xl md:text-5xl lg:text-5xl text-primary-blue"
                >
                  Startups
                </motion.span>
              </h1>

              <motion.p 
                {...paragraphAnimation}
                transition={shouldAnimate ? { duration: 0.3, delay: 0.3 } : {}}
                className="text-[#7B6B9C] text-base sm:text-lg md:text-xl leading-relaxed tracking-normal max-w-full md:max-w-[600px] text-left mt-2 sm:mt-4"
              >
                Whether you&apos;re raising your first round or closing enterprise deals, Capture The Bug helps you meet security requirements with real human-led pentesting-on your schedule, and inside your dev workflow.
              </motion.p>
              
              <motion.p 
                {...paragraphAnimation}
                transition={shouldAnimate ? { duration: 0.3, delay: 0.4 } : {}}
                className="text-[#7B6B9C] text-base sm:text-lg leading-relaxed mt-0 sm:mt-1"
              >
                Our PTaaS platform gives you continuous access to security experts, real-time findings, and compliance-ready reports that help you win SOC 2, ISO 27001, HIPAA, and customer trust-faster.
              </motion.p>

              <motion.div 
                {...paragraphAnimation}
                transition={shouldAnimate ? { duration: 0.3, delay: 0.5 } : {}}
                className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center sm:justify-start pt-2 sm:pt-4"
              >
                <Button
                  href="/Request-Demo"
                  variant="success"
                  size="lg"
                  rightIcon={arrowIcon}
                >
                  Request a demo
                </Button>
              </motion.div>
            </div>
          </div>

          {/* Right Dashboard Image Section - Responsive */}
          <motion.div 
            {...imageAnimation}
            className="flex justify-center xl:justify-end items-center w-full h-full px-4 lg:px-0 xl:pr-0"
          >
            <div className="relative w-full max-w-[640px] h-[220px] sm:h-[300px] md:h-[380px] lg:h-[440px] xl:h-[520px] rounded-xl overflow-hidden  ">
              <Image
                src="/images/startup_dash.svg"
                alt="Startup Security Dashboard"
                fill
                priority
                className="object-cover object-top rounded-xl shadow-2xl"
                sizes="(max-width: 640px) 100vw, (max-width: 768px) 80vw, (max-width: 1024px) 50vw, 640px"
                quality={80}
              />
            </div>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
}