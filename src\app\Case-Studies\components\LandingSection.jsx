import DarkButton from "@/app/common/buttons/DarkButton";
import LightButton from "@/app/common/buttons/LightButton";
import Image from "next/image";
import Link from "next/link";
import React from "react";

export default function LandingSection() {
  return (
    <section className="md:p-12 p-8 flex md:flex-row flex-col justify-between mb-10">
      <article className="Content md:w-[50%] md:gap-6 gap-4 flex flex-col">
        <header className="Title md:text-6xl text-4xl font-bold bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent md:py-4">
        Case Studies
        </header>

        <p className="subTitle2 leading-8 md:pr-10 text-slate-600 text-30 md:text-lg">
        See how Capture The Bug&apos;s agile pentesting platform has enabled organizations across various industries to manage cyber risk, enhance security, and scale with confidence through continuous testing and proactive vulnerability management.
        </p>

        <div className="Button pt-6 ">
          <a href="https://outlook.office.com/bookwithme/user/<EMAIL>?anonymous&ep=pcard">
            <DarkButton className="rounded-md font-semibold px-8 py-2">
              Talk to an Expert
            </DarkButton>
          </a>
        </div>
      </article>

      <div className="Image md:mt-4 mt-10">
        <Image src="/images/CaseStudy-img.jpg" width={600} height={500} alt="" />
      </div>
    </section>
  );
}
