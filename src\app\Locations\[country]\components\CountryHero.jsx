'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { MapPin, ArrowRight } from 'lucide-react';
import Button from '@/app/common/buttons/Button';
import Image from 'next/image';
import { getLocationData } from '../data/locationData';
import BreadcrumbNavigation from '@/app/common/components/BreadcrumbNavigation';
import { generateBreadcrumbs } from '@/app/common/components/BreadcrumbNavigation';

const CountryHero = ({ country }) => {
  // Get comprehensive location data
  const locationData = getLocationData(country);

  // Generate breadcrumbs for location pages
  const breadcrumbs = generateBreadcrumbs('location', {
    countryName: locationData.country
  })(country);

  // Enhanced hero data with SEO optimization
  const countryData = {
    nz: {
      title: "New Zealand Penetration Testing Services",
      subtitle: "Comprehensive Cybersecurity Solutions for Kiwi Businesses",
      description: `Capture The Bug delivers specialized penetration testing services tailored for New Zealand businesses. Our certified security professionals provide comprehensive cybersecurity assessments using industry-leading methodologies and ISO 27001 standards.`,
      imageSrc: "/images/nz-map.png",
      imageAlt: "New Zealand cybersecurity services coverage map showing Hamilton-based penetration testing services",
      locations: locationData.serviceAreas.slice(0, 4),
      highlights: []
    },
    au: {
      title: "Australian Penetration Testing Services",
      subtitle: "Enterprise-Grade Security for Australian Organizations",
      description: `Capture The Bug offers comprehensive penetration testing solutions for Australian enterprises, government entities, and growing businesses. Our security experts focus on Australian Privacy Principles compliance and Essential Eight implementation, delivering thorough vulnerability assessments and detailed remediation guidance.`,
      imageSrc: "/images/AU-map.png",
      imageAlt: "Australia cybersecurity services coverage map showing comprehensive penetration testing solutions",
      locations: locationData.serviceAreas.slice(0, 4),
      highlights: []
    },
    us: {
      title: "United States Penetration Testing Services",
      subtitle: "Advanced Security Testing for American Enterprises",
      description: `Capture The Bug provides advanced penetration testing services for American businesses, from emerging startups to established corporations. Our security assessments emphasize federal compliance standards, NIST framework alignment, and comprehensive vulnerability management tailored to US regulatory environments.`,
      imageSrc: "/images/usa-map-loc.png",
      imageAlt: "United States cybersecurity services coverage map showing advanced penetration testing solutions",
      locations: locationData.serviceAreas.slice(0, 4),
      highlights: []
    }
  };

  const data = countryData[country.toLowerCase()];

  return (
    <section className="bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] text-white min-h-screen px-4 relative overflow-hidden flex items-center">
      {/* Breadcrumb Navigation - positioned absolutely at the top */}
      <div className="absolute top-0 left-0 right-0 pt-2 sm:pt-2 md:pt-5 xl:pt-14 z-10">
        <div className="max-w-7xl px-2 sm:px-2 md:px-16">
          <BreadcrumbNavigation items={breadcrumbs} className="text-white" />
        </div>
      </div>

      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.1)_1px,transparent_0)] bg-[length:24px_24px] opacity-20"></div>
      </div>

      <div className="max-w-7xl mx-auto relative z-10 w-full">
        <div className="flex flex-col md:flex-row items-center justify-center gap-8 md:gap-16">
          {/* Content Section */}
          <div className="w-full md:w-1/2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="space-y-8 py-8"
            >
              {/* Main Heading - H1 for SEO */}
              <h1 className="text-3xl md:text-5xl font-bold leading-tight">
                {data.title}
              </h1>

              {/* Subtitle - H2 for SEO hierarchy */}
              <h2 className="text-xl md:text-2xl font-medium text-white/80">
                {data.subtitle}
              </h2>

              {/* Description with location-specific keywords */}
              <p className="text-base text-white/90 max-w-2xl">
                {data.description}
              </p>



              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <Button
                  href="/Request-Demo"
                  variant="success"
                  size="lg"
                  rightIcon={<ArrowRight className="w-3 h-4" />}
                >
                  Request Free Security Assessment
                </Button>
                <Button
                  href="/Pricing"
                  variant="secondary"
                  size="lg"
                  className="bg-white/10 border-white/30 text-white hover:bg-white/20"
                >
                  Calculate Price
                </Button>
              </div>

              {/* Service Highlights - Compact single line layout */}
              <div className="pt-6 grid grid-cols-1 lg:grid-cols-3 gap-3">
                {data.highlights.map((highlight, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center group bg-white/5 rounded-lg p-3 hover:bg-white/10 transition-colors duration-300"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                  >
                    <div className="bg-white/10 p-1.5 rounded-md mr-3 group-hover:bg-white/20 transition-colors duration-300">
                      {highlight.icon}
                    </div>
                    <div>
                      <h3 className="text-white font-medium text-sm">{highlight.title}</h3>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Visual Section */}
          <div className="w-full md:w-1/2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white/10 backdrop-blur-sm rounded-xl p-8 border border-white/20 overflow-hidden min-h-[500px] flex flex-col justify-center"
            >
              {/* Map Image with SEO-optimized alt text */}
              <div className="flex justify-center mb-8">
                <div className="relative w-full h-48 md:h-56">
                  <Image
                    src={data.imageSrc}
                    alt={data.imageAlt}
                    fill
                    style={{ objectFit: 'contain' }}
                    priority
                    className="transform hover:scale-105 transition-transform duration-500"
                  />
                </div>
              </div>

              {/* Service Locations - H3 for SEO hierarchy */}
              <h3 className="text-base font-semibold text-white mb-4">
                Our {locationData.country} Service Locations
              </h3>
              <div className="flex flex-wrap gap-2 mb-8">
                {data.locations.map((location, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center bg-white/10 px-3 py-2 rounded-md hover:bg-white/20 transition-colors duration-300"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                  >
                    <MapPin className="w-4 h-4 text-white/80 mr-2" />
                    <span className="font-medium text-sm">{location}</span>
                  </motion.div>
                ))}
              </div>

              {/* Pricing Starting From */}
              <div className="pt-6 border-t border-white/20 mt-auto">
                <p className="text-white/80 text-sm mb-2">Starting from</p>
                <p className="text-2xl font-bold text-white mb-2">
                  {locationData.currency} {locationData.pricing.startingPrice.toLocaleString()}
                </p>
                <p className="text-white/70 text-sm">Professional security assessment</p>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CountryHero; 