import PropTypes from "prop-types";
import clsx from "clsx";

const LightButton = ({ children, onClick, icon, className }) => {
  return (
    <button
      className={clsx(
        "bg-blue-200 text-blue-700 py-1 border border-blue-700 px-4 rounded-lg font-semibold text-md flex items-center justify-center transition-all duration-300 ease-in-out group relative overflow-hidden",
        className
      )}
      onClick={onClick}
      aria-label="light button"
    >
      <span className="absolute inset-0 w-full h-full bg-blue-300/50 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 ease-out"></span>
      <span className="relative z-10">{children}</span>
      {icon && <span className="ml-2 relative z-10 transition-transform group-hover:translate-x-1 duration-300">{icon}</span>}
    </button>
  );
};

LightButton.propTypes = {
  children: PropTypes.node.isRequired,
  onClick: PropTypes.func,
  icon: PropTypes.node,
  className: PropTypes.string,
};

export default LightButton;
