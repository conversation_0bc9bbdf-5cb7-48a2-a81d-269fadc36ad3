import UsefulLinksPage from '@/app/common/pages/UsefulLinksPage'
import React from 'react'

export const metadata = {
  openGraph: {
    title: "Capture The Bug | Code Of Ethics",
    type: "website",
    url: "https://capturethebug.xyz/Useful-Links/Code-Of-Ethics",
    description:
      "The Capture The Bug (“CTB”) Code of Conduct lays out the necessary conduct for all CTB community members that participate in crowdsourced security programs, online community offerings, and other CTB programs. ",
    images:  "https://i.postimg.cc/RVDkS5zQ/Code-Of-Ethics.png"
  },
};
export default function page() {
  return (
    <div>
      <title>Capture The Bug | Code Of Ethics</title>
      <UsefulLinksPage title="Code of Ethics">
        <div className='font-bold'>Code of Ethics</div>        
        <div className='font-bold'>CTB-COE-V2.0</div>        
        <div className='font-bold'>Last update 16-03-2023</div>      

        <p className='text-slate-600'>The Capture The Bug (“CTB”) Code of Conduct lays out the necessary conduct for all CTB community members that participate in crowdsourced security programs, online community offerings, and other CTB programs. This Code of Conduct applies to all interactions between CTB team members, customers, researchers and ethical hackers. The CTB community is open to all people from different backgrounds, and following this Code of Conduct will help make sure we have a safe and welcoming environment for everyone. Please read through this information to understand the necessary behaviour for all CTB participants.</p>  

        <div className='font-bold'>What is expected?</div>

        
        <p className='text-slate-600'>
            <strong>Professionalism:</strong> Researchers are expected to behave professionally when interacting with other researchers and CTB team members. This includes being respectful, courteous, and avoiding any aggressive language or behaviour.   
        </p> 

        <p className='text-slate-600'>
            <strong>Honesty and Integrity:</strong> Researchers should maintain a high level of honesty and integrity at all times. This includes not intentionally misleading CTB or customers, and reporting vulnerabilities promptly.
        </p> 

        <p className='text-slate-600'>
            <strong>Responsible Disclosure:</strong> Researchers should adhere to responsible disclosure practices when reporting vulnerabilities. This includes not sharing confidential vulnerability or customer information without explicit permission. Follow proper Disclosure Guidelines. Sharing confidential vulnerability or customer information is prohibited. No submitted vulnerability may be disclosed without explicit customer permission. Please read each Program Brief for specific program disclosure policies. Disclose or communicate about vulnerability submissions via the proper channels. If you have any questions about disclosure, contact CTB Support.
        </p> 

        <p className='text-slate-600'>
            <strong>Prompt Reporting:</strong> Researchers are expected to promptly report any vulnerabilities they find, so they can be addressed and fixed as quickly as possible.
        </p> 

        <p className='text-slate-600'>
            <strong>Limited Data Access:</strong> When a vulnerability provides unintended access to data, researchers must follow certain guidelines. First, they should limit the amount of data accessed to only what is necessary to demonstrate the vulnerability. Second, they must stop testing and report the vulnerability immediately upon encountering any user data, such as Personally Identifiable Information (PII), Personal Healthcare Information (PHI), credit card data, or proprietary information. In addition, researchers must adhere to all applicable laws and regulations related to accessing and processing such sensitive data. If they access PII or other sensitive data, they must comply with these laws and regulations.
        </p> 

        <p className='text-slate-600'>
            <strong>Maintain ethical behaviour :</strong> by refraining from intentionally misleading CTB or customers. Your job is to attempt to identify technology and business logic flaws, and to report them for fixing instead of exploiting them.

            Read and follow CTB’s Researcher Terms and each program’s Program Brief. Follow any guidelines and rules a particular crowdsourced security program or company may have regarding the scope of testing and disclosure.   
        </p> 

        <p className='text-slate-600'>
            <strong>Report bad behaviours:</strong> As a member of this community, you can impact the quality and reputation of CTB. If you see anything that violates our guidelines, please notify our operations team immediately at CTB Support. 
        </p> 

        <p className='text-slate-600'>
            <strong>Professionalism:</strong> Researchers are expected to behave professionally when interacting with other researchers and CTB team members. This includes being respectful, courteous, and avoiding any aggressive language or behaviour.   
        </p> 
      </UsefulLinksPage>
    </div>
  )
}
