import React from 'react';
import Image from 'next/image';

const HeaderSection = ({ title, description, imageUrl, breadcrumbs }) => {
  return (
    <div className="bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] text-white relative p-6 md:p-16 flex flex-col md:flex-row justify-between items-center gap-6">
      {/* Breadcrumbs positioned absolutely at the top */}
      {breadcrumbs && (
        <div className="absolute top-0 left-0 right-0 pt-2 sm:pt-2 md:pt-5 xl:pt-14 z-10">
          <div className="max-w-7xl px-2 sm:px-2 md:px-16">
            {breadcrumbs}
          </div>
        </div>
      )}
      
      {/* Left Side - Text */}
      <div className="w-full md:max-w-2xl mt-5 sm:mt-0 sm:ml-0 md:ml-8">
        <p className="text-md">{description}</p>
      </div>

      {/* Right Side - Image */}
      <div className="w-full md:w-auto h-64 sm:mr-0 md:mr-8 rounded-lg overflow-hidden">
        <Image
          src={imageUrl}
          alt={title || "Blog post featured image"}
          className="w-full h-full object-contain"
          width={400}
          height={256}
        />
      </div>
    </div>
  );
};

export default HeaderSection;
