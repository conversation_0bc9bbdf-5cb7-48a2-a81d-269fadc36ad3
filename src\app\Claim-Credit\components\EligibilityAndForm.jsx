// EligibilityAndForm.jsx

"use client";
import React, { useState } from 'react';
import { sendEmail } from "../../utils/send-email";

const EligibilityAndForm = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState('');
  const [apiError, setApiError] = useState('');

  // Form State
  const [formData, setFormData] = useState({
    fullName: '',
    companyName: '',
    workEmail: '',
    productUrl: '',
    buildingWhat: '',
    needsCompliance: '',
    kickoffDate: ''
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (isSubmitting) return;

    setIsSubmitting(true);
    setApiError('');
    setSuccess('');
    console.log('Form Submitted:', formData);

    const emailContent = `
      Full Name: ${formData.fullName}
      Company Name: ${formData.companyName}
      Work Email: ${formData.workEmail}
      Product URL: ${formData.productUrl}
      Building: ${formData.buildingWhat}
      Compliance Needs: ${formData.needsCompliance}
      Kickoff Date: ${formData.kickoffDate}
    `;

    try {
      await sendEmail("Startup Credit Application", emailContent);

      // Clear form fields on success
      setFormData({
        fullName: '',
        companyName: '',
        workEmail: '',
        productUrl: '',
        buildingWhat: '',
        needsCompliance: '',
        kickoffDate: ''
      });

      setSuccess("Thank you! Your startup credit application has been submitted successfully. We'll review your eligibility within 24 hours and get back to you shortly.");
    } catch (error) {
      console.error("Error sending email:", error);
      setApiError("Failed to submit application. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Eligibility Criteria
  const eligibilityCriteria = [
    "A pre-seed to Series A startup",
    "Have less than 100 employees",
    "Building a digital product (web, mobile, or API)",
    "Willing to start your first pentest within 30 days"
  ];

  return (
    <section id="apply-form" className="min-h-screen bg-gradient-to-b from-white to-gray-50 py-16 sm:py-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl sm:text-5xl font-bold text-[#027bfc] mb-4">
            Get Your $1,000 Startup Credit
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Accelerate your security journey with our exclusive startup program
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-20 items-start">
          {/* Left Column: Eligibility */}
          <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
            <div className="flex items-center gap-3 mb-8">
              <div className="w-12 h-12 bg-indigo-100 rounded-xl flex items-center justify-center">
                {/* SVG icon for Eligibility Check [5][8] */}
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-ctb-light-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h2 className="text-2xl sm:text-3xl font-bold text-ctb-blue-350">
                Who&apos;s Eligible?
              </h2>
            </div>
            
            <p className="text-lg text-gray-600 mb-8">
              You may qualify if you are:
            </p>
            
            <ul className="space-y-6">
              {eligibilityCriteria.map((item, index) => (
                <li key={index} className="flex items-start gap-4">
                  <div className="w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    {/* SVG icon for list item checkmark [5] */}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-ctb-light-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="3">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <span className="text-gray-700 text-lg">{item}</span>
                </li>
              ))}
            </ul>

            <div className="mt-10 bg-ctb-blue-0/50 rounded-xl p-6 border border-indigo-100">
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  {/* SVG icon for Priority/Target [4][6] */}
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-ctb-light-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <path d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" />
                    <path d="M12 12m-5 0a5 5 0 1 0 10 0a5 5 0 1 0 -10 0" />
                    <path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-ctb-blue-350 mb-2">Priority Access</h3>
                  <p className="text-ctb-blue-350 text-sm sm:text-base leading-relaxed">
                    We prioritize <span className="whitespace-nowrap">YC</span>, <span className="whitespace-nowrap">Techstars</span>, <span className="whitespace-nowrap">Antler</span>, <span className="whitespace-nowrap">Blackbird</span>, <span className="whitespace-nowrap">Icehouse</span>, <span className="whitespace-nowrap">Startmate</span>, and other accelerator-backed teams.
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-4 text-center">
              <a 
                href="/CTB-Terms-and-Conditions-Startup-Program.pdf" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-ctb-light-blue hover:text-ctb-blue-350 underline text-sm font-medium transition-colors"
              >
                Terms and Conditions Apply
              </a>
            </div>
          </div>

          {/* Right Column: Application Form */}
          <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
            <div className="mb-8">
              <h2 className="text-2xl sm:text-3xl font-bold text-ctb-blue-350 mb-3">
                Apply Now
              </h2>
              <p className="text-gray-600">
                Fill out the form and we&apos;ll review your eligibility within 24 hours.
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name
                  </label>
                  <input 
                    type="text" 
                    name="fullName" 
                    id="fullName" 
                    value={formData.fullName}
                    onChange={handleChange} 
                    required 
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-ctb-light-blue focus:border-transparent transition-all"
                    placeholder="John Doe"
                  />
                </div>
                <div>
                  <label htmlFor="companyName" className="block text-sm font-medium text-gray-700 mb-2">
                    Company Name
                  </label>
                  <input 
                    type="text" 
                    name="companyName" 
                    id="companyName" 
                    value={formData.companyName}
                    onChange={handleChange} 
                    required 
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-ctb-light-blue focus:border-transparent transition-all"
                    placeholder="Acme Inc."
                  />
                </div>
              </div>

              <div>
                <label htmlFor="workEmail" className="block text-sm font-medium text-gray-700 mb-2">
                  Work Email
                </label>
                <input 
                  type="email" 
                  name="workEmail" 
                  id="workEmail" 
                  value={formData.workEmail}
                  onChange={handleChange} 
                  required 
                  className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-ctb-light-blue focus:border-transparent transition-all"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label htmlFor="productUrl" className="block text-sm font-medium text-gray-700 mb-2">
                  Website or Product URL
                </label>
                <input 
                  type="url" 
                  name="productUrl" 
                  id="productUrl" 
                  value={formData.productUrl}
                  onChange={handleChange} 
                  required 
                  className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-ctb-light-blue focus:border-transparent transition-all"
                  placeholder="https://your-product.com"
                />
              </div>

              <div>
                <label htmlFor="buildingWhat" className="block text-sm font-medium text-gray-700 mb-2">
                  What are you building?
                </label>
                <textarea 
                  name="buildingWhat" 
                  id="buildingWhat" 
                  value={formData.buildingWhat}
                  onChange={handleChange} 
                  rows="3" 
                  required 
                  className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-ctb-light-blue focus:border-transparent transition-all resize-none"
                  placeholder="Tell us about your product..."
                ></textarea>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="needsCompliance" className="block text-sm font-medium text-gray-700 mb-2">
                    Compliance Needs
                  </label>
                  <input 
                    type="text" 
                    name="needsCompliance" 
                    id="needsCompliance" 
                    value={formData.needsCompliance}
                    onChange={handleChange} 
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-ctb-light-blue focus:border-transparent transition-all"
                    placeholder="SOC 2, HIPAA, etc."
                  />
                </div>
                <div>
                  <label htmlFor="kickoffDate" className="block text-sm font-medium text-gray-700 mb-2">
                    Target Kickoff Date
                  </label>
                  <input 
                    type="date" 
                    name="kickoffDate" 
                    id="kickoffDate" 
                    value={formData.kickoffDate}
                    onChange={handleChange} 
                    required 
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-ctb-light-blue focus:border-transparent transition-all"
                  />
                </div>
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-[#062575] text-white font-semibold py-4 px-6 rounded-lg hover:bg-[#041a4f] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-lg"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Application'}
              </button>

              {/* Success and Error Messages */}
              {success && (
                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-green-800">{success}</p>
                    </div>
                  </div>
                </div>
              )}

              {apiError && (
                <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-red-800">{apiError}</p>
                    </div>
                  </div>
                </div>
              )}

              <p className="text-sm text-gray-500 text-center mt-4">
                By submitting, you agree to our Terms of Service and Privacy Policy
              </p>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EligibilityAndForm;