import FullBlogView from "@/app/common/pages/BlogView/FullBlogView";
import BookACall from "@/app/Home/components/BookACall";
import React from "react";

export const metadata = {
  openGraph: {
    title: "Capture The Bug | New Zealand became the latest nation",
    type: "website",
    url: "https://capturethebug.xyz/Blogs/New-Zealand-became-the-latest-nation",
    description:
      "New Zealand’s Government Communications Security Bureau (GCSB) has advised government agencies to introduce vulnerability disclosure policies (VDPs).",
    images: "https://i.postimg.cc/nLPspS0f/Blog5.png",
  },
};

function page() {
  const headerSection = {
    description:
      "New Zealand’s Government Communications Security Bureau (GCSB) has advised government agencies to introduce vulnerability disclosure policies (VDPs).",
    imageUrl: "/images/Blog5.png",
  };
  return (
    <div>
      <title>Capture The Bug | New Zealand became the latest nation</title>
      <FullBlogView headerSection={headerSection}>
      <div className="md:text-lg text-gray-600">
      The GCSB said agencies should establish a process that would allow members of the public to report potential software vulnerabilities or other security problems.
        </div>

        <div className="md:text-lg text-gray-600">
        Under the new mandate, each agency will be responsible for creating its own policy, based on the sensitivity of the information it holds, the security measures already in place, and its ability to segment its network or otherwise segregate sensitive information. Vulnerabilities should be patched, mitigated, or managed within 90 days.
        </div>
        <div className="md:text-lg text-gray-600">
        This move by the New Zealand government is in line with global best practices for cybersecurity and helps standardize the policies to create clear pathways for ethical hackers to submit and communicate about potential vulnerabilities.
        </div>

        <div className="md:text-3xl font-semibold text-blue-600">
       <b> But, if you’re charged with securing technology at a federal agency, what does this mean?</b>
        </div>


        <div className="md:text-lg text-gray-600">
        Government Communications Security Bureau (GCSB) offers guidelines on how to develop and publish VDPs, and how vulnerabilities should be disclosed and mitigated.
        </div>

        <div className="md:text-lg text-gray-600">
        So what do you do now? Here are some steps you should start today to comply with this directive while effectively reducing the risk to your internet-facing technologies.
        </div>

        <div className="md:text-3xl font-semibold text-blue-600">
       <b> What’s Required?</b>
        </div>

        <div className="md:text-lg text-gray-600">
        The GCSB has some requirements, ranging from adding a security contact email address to developing vulnerability handling, disclosure, and reporting processes. It’s a lot to do, especially for those starting from scratch.
        </div>

        <div className="md:text-lg text-gray-600">
       <b> An agency’s Vulnerability disclosure policy (VDP) MUST contain at least the following core content:</b>
        </div>

        <div className="md:text-lg text-gray-600">
        A VDP will typically include:
        </div>


        <div className="md:text-lg text-gray-600">
          <ol className="list-decimal space-y-4">
            <li className="text-gray-700">
             A <b>scoping statement</b>   listing the systems the policy applies to (e.g. the agency’s website and other public-facing systems); Contact details
            </li>
            <li className="text-gray-700">
            Details of how finders can contact the agency’s security team (including any public keys for encrypting reports)
            </li>
            <li className="text-gray-700">
            Permitted activities; Acknowledgement of reports and a response time (typically 60 or 90 days) for corrections, adjustments, or other “fixes”
            </li>
            <li className="text-gray-700">
            Reporters/finders agree to not share information about the vulnerability until the end of the disclosure period, to let the organization fix the issues before it becomes public
            </li>
            <li className="text-gray-700">
            Illegal activities are not permitted (specifying any relevant legislation, such as the Crimes Act, the Privacy Act etc.); and Either a statement that bug bounties will not be paid for any discoveries, or information about the agency’s bug bounty programme.
            </li>


          </ol>
        </div>

        

        <div className="md:text-3xl font-semibold text-blue-600">
  <b>Building an Effective VDP</b>
</div>

<div className="md:text-lg text-gray-600">
  A good Vulnerability Disclosure Program (VDP) makes it easy for researchers and ethical hackers to report any vulnerabilities they find.
</div>

<div className="md:text-lg text-gray-600">
  However, implementing a good VDP can be challenging. If policies are poorly written or processes are cumbersome, it can increase your agency’s risk or waste resources.
</div>

<div className="md:text-lg text-gray-600">
  Your security team needs to be ready to handle many reports and quickly sort, route, and communicate with reporters and development teams. Any issues along the way could make security researchers unhappy or put your security at risk.
</div>

<div className="md:text-lg text-gray-600">
  It is important to involve all stakeholders from the beginning, not just security, IT, and web development teams. Other teams such as Legal, Communications, and Operations may be affected by a security gap or want to know about security efforts.
</div>

<div className="md:text-lg text-gray-600">
  At Capture The Bug, we provide many tools to help you create an effective VDP. We offer a complete platform for deploying a VDP and managing the entire process, including reporting, communication, and mitigation. Our platform can be integrated with your existing security and collaboration tools.
</div>

<div className="md:text-3xl font-semibold text-blue-600">
  <b>Opening Statement</b>
</div>

<div className="md:text-lg text-gray-600">
  Responsible disclosure is a process based on trust. In the past, ethical hackers have been hesitant to notify organizations of potential vulnerabilities due to threats of legal actions. However, more organizations are now recognizing the power of collective intelligence and public VDPs.
</div>

<div className="md:text-lg text-gray-600">
  Nonetheless, researchers still fear the potential legal repercussions unless the VDP clearly commits to not penalizing those who report potential vulnerabilities in good faith.
</div>

<div className="md:text-lg text-gray-600">
  This statement is typically the opening section of a well-written VDP. It is simply a statement that explains the agency’s commitment to security and invites researchers to submit vulnerabilities.
</div>

<div className="md:text-lg text-gray-600">
  The opening statement for the Ministry of Social Development is as follows: “The Ministry of Social Development (the Ministry) takes the security and privacy of our information seriously. If you identify a security issue with our systems, please let us know so we can address it.”
</div>

<div className="md:text-3xl font-semibold text-blue-600">
  <b>Identify Your Scope</b>
</div>

<div className="md:text-lg text-gray-600">
  This section provides information on vulnerability disclosure for all externally-facing agency systems, including public-facing systems.
</div>

<div className="md:text-lg text-gray-600">
  When determining which systems, applications, and data fall within the scope of a vulnerability disclosure program (VDP), agencies may consider the following:
</div>

<div className="md:text-lg text-gray-600">
  <ol className="list-decimal space-y-4">
    <li>The sensitivity of the information on the agency’s systems, including financial data, medical information, proprietary information, customer data, or other personally identifiable information (PII).</li>
    <li>Existing security safeguards on the system, such as data encryption at rest.</li>
    <li>The agency’s ability to segment its network or otherwise segregate sensitive information stored on its systems.</li>
    <li>Regulatory, contractual, privacy, or other restrictions on the disclosure of protected classes of information (such as within the New Zealand Classification System).</li>
  </ol>
</div>

<div className="md:text-lg text-gray-600">
  Capture The Bug can assist agencies and organizations with creating and articulating their VDP, including defining and publishing what is in and out of scope.
</div>

<div className="md:text-3xl font-semibold text-blue-600">
  <b>Establish a Clear Process</b>
</div>

<div className="md:text-lg text-gray-600">
  While the scope defines the properties covered by your VDP, it also defines the rules of the game for ethical hackers.
</div>

<div className="md:text-lg text-gray-600">
  Once a potential vulnerability is found, the real disclosure and mitigation process begins. When security risks in agency services are discovered and reported to the agency, it is vital that a robust communication channel is available to receive the report.
</div>

<div className="md:text-lg text-gray-600">
  To facilitate a clear process, GCSB requires that agencies provide a description of how reports are to be sent, detail the information to be included with the report, and allow for a statement that reporters may submit.
</div>

<div className="md:text-lg text-gray-600">
  But this is just the first step in a long process of triage and remediation. You’ll need to assess, prioritize, mitigate, and address incoming vulnerability reports.
</div>

<div className="md:text-lg text-gray-600">
  Capture The Bug helps agencies of any size manage the publishing and facilitation of a VDP effectively. This includes articulating a policy to meet your agency’s unique needs and building a streamlined process to comply with the new GCSP mandate.
</div>

<div className="md:text-3xl font-semibold text-blue-600">
  <b>Why Capture The Bug</b>
</div>

<div className="md:text-lg text-gray-600">
  Capture The Bug can help you establish a Vulnerability Disclosure Program (VDP) that allows you to achieve compliance with minimal operational disruption. But more importantly, we can guide you on your end-to-end strategy. With Capture The Bug, you can craft a VDP, report on program statistics and specifics, and create a strategy that’s right for your organization.
</div>

<div className="md:text-lg text-gray-600">
  Capture The Bug delivers community-powered security solutions with efficiency and effectiveness.
</div>

<div className="md:text-lg text-gray-600">
  The GCSB mandate is an effort to improve overall security. Don’t waste the expense and opportunity to maximize the security benefits of this new program. Capture The Bug offers testing that conforms to your agency’s needs, helps integrate vulnerability reports with your existing processes, and facilitates access to the world’s largest community of security researchers.
</div>

<div className="md:text-lg text-gray-600">
  With Capture The Bug, you can comply with GCSB, improve your security, and do it all with minimal operational disruption.  To learn more, visit capturethebug.xyz or send a <NAME_EMAIL> to speak with one of our expert.
</div>












       
      </FullBlogView>
      <BookACall />
    </div>
  );
}

export default page;
