"use client";
import DarkButton from "@/app/common/buttons/DarkButton";
import Link from "next/link";
import { useState, useCallback, memo } from "react";
import Image from 'next/image';

const navItems = [
  "Cloud-Based Pentesting",
  "Integrations",
  "Flexible Pricing",
  "Scalable and Agile Pentesting",
  "Real-Time Bug Reports",
];

const FeatureContent = memo(function FeatureContent({ title, image, description, buttonText, url }) {
  return (
    <div className="flex flex-col lg:flex-row items-start justify-between px-4 lg:px-20">
      <div className="w-full lg:w-1/2 lg:pr-10 text-left mb-4 lg:mb-0 flex flex-col">
        <p className="mb-4 text-lg text-slate-600 leading-8">{description}</p>
        <Link href={url} passHref>
          <DarkButton>{buttonText}</DarkButton>
        </Link>
      </div>
      <div className="w-full lg:w-1/2">
        <Image
          src={image}
          alt={title}
          width={800}
          height={600}
          layout="responsive"
          objectFit="cover"
        />
      </div>
    </div>
  );
});

export default function Benefits() {
  const [activeTab, setActiveTab] = useState("Cloud-Based Pentesting");
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = useCallback(() => setIsMenuOpen(prev => !prev), []);
  
  const handleTabChange = useCallback((item) => {
    setActiveTab(item);
    setIsMenuOpen(false);
  }, []);

  const tabContent = {
    "Cloud-Based Pentesting": {
      image: "/images/PTaaS.jpg",
      description:
        "Capture The Bug's PTaaS SaaS model delivers flexible, on-demand security assessments, eliminating the need for traditional, resource-intensive setups. Conduct multiple, simultaneous tests with ease, and get real-time results and insights, all through our intuitive platform.",
      buttonText: "Launch a Pentest",
      url: "/Request-Demo"
    },
    Integrations: {
      image: "/images/Integration.jpg",
      description:
        "Easily integrate with tools like Jira and Slack to relay manual pentest findings directly to your development teams. Benefit from comprehensive insights and tailored solutions to intelligently address vulnerabilities and enhance your security posture.",
      buttonText: "Launch a Pentest",
      url: "/Request-Demo"
    },
    "Flexible Pricing": {
      image: "/images/pricing_dashboard.jpg",
      description:
        "Capture The Bug offers a monthly subscription that combines cost-effectiveness with top-tier human expertise. Our subscription model overcomes the limitations of traditional pentesting and automated tools, providing comprehensive detection of complex vulnerabilities and business logic flaws.",
      buttonText: "Calculate Cost",
      url: "/Pricing"
    },
    "Scalable and Agile Pentesting": {
      image: "/images/scalable_dashboard.jpg",
      description:
        "Quickly initiate pentests and gain access to a global network of expert testers, ready to begin assessments within 24 hours. Our flexible SaaS model adapts to your evolving testing needs, ensuring comprehensive coverage and protection for your digital assets.",
      buttonText: "Read Continuous Pentesting for SaaS guide",
      url: "/PtaaS for Saas- A deep dive.pdf"
    },
    "Real-Time Bug Reports": {
      image: "/images/program_dashboard.jpg",
      description:
        "Capture The Bug's PTaaS platform delivers real-time bug reports, empowering your team to address vulnerabilities as soon as they are identified. Collaborate directly with expert pentesters to understand threats and develop effective patches swiftly.",
      buttonText: "Launch a Pentest",
      url: "/Request-Demo"
    },
  };

  return (
    <div className="bg-white">
      <div className="container py-16 flex flex-col items-left text-center md:text-left">
        <div className="px-20 md:text-left text-slate-600 text-lg mb-4">
          Capture The Bug PTaaS Platform Benefits
        </div>
        <h1 className="md:text-5xl text-2xl font-semibold md:px-20 px-8 mb-12 bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent py-2">
          Benefits of Using Our PTaaS
        </h1>

        {/* Mobile Dropdown Navbar */}
        <div className="lg:hidden w-full mb-8 relative px-10">
          <button
            onClick={toggleMenu}
            className="w-full py-2 px-4 text-left bg-white border border-gray-300 rounded-md shadow-sm"
          >
            {activeTab} ▼
          </button>
          {isMenuOpen && (
            <ul className="w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-10">
              {navItems.map((item) => (
                <li key={item}>
                  <button
                    className={`w-full py-2 px-4 text-left ${
                      activeTab === item
                        ? "bg-blue-100 text-blue-700"
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                    onClick={() => handleTabChange(item)}
                  >
                    {item}
                  </button>
                </li>
              ))}
            </ul>
          )}
        </div>

        {/* Desktop Navigation */}
        <div className="hidden lg:flex mb-8 justify-center space-x-10 border-b border-gray-200 px-10">
          {navItems.map((item) => (
            <button
              key={item}
              className={`py-2 px-1 text-[18px] font-bold transition-colors duration-300 relative ${
                activeTab === item
                  ? "text-blue-700"
                  : "text-gray-500 hover:text-blue-700"
              }`}
              onClick={() => setActiveTab(item)}
            >
              {item}
              {activeTab === item && (
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-blue-700"></span>
              )}
            </button>
          ))}
        </div>

        <main className="w-full">
          <FeatureContent {...tabContent[activeTab]} />
        </main>
      </div>
    </div>
  );
}
