import Image from "next/image";

export default function CustomerTestimonials() {
  return (
    <div className="min-h-screen w-full relative overflow-hidden bg-ctb-blue-350 flex flex-col items-center justify-center py-6 sm:py-8 lg:py-12 lg:px-auto pt-16 lg:pt-20">
      {/* Background image - hidden on screens smaller than lg (1024px) */}
      <div className="absolute bottom-0 left-0 w-full h-auto hidden lg:block">
        <Image 
          src="/images/customer-bg.png" 
          alt="" 
          width={1920}
          height={600}
          className="w-full h-3/4 object-cover object-bottom" 
          priority={false}
        />
      </div>

      <div className="relative z-20 max-w-7xl mx-auto px-4 lg:mx-14">
        <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white text-center mb-8 sm:mb-12 md:mb-16">
          Hear from leaders who trust Capture The Bug
        </h1>

        <div className="flex flex-col lg:flex-row items-center justify-center gap-8 sm:gap-12 lg:gap-20 py-8 sm:py-10 lg:py-14 mb-12 sm:mb-16 lg:mb-20">
          
          <div className="relative w-full max-w-lg lg:max-w-none">
            <div className="bg-white p-1 rounded-2xl">
              <iframe
                className="w-full aspect-video sm:w-[500px] sm:h-[300px] lg:w-[700px] lg:h-[400px] rounded-xl"
                src="https://www.youtube.com/embed/1fTR_KJRCeU"
                title=""
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowFullScreen
              />
            </div>
            <div className="absolute bottom-1 left-1 bg-white rounded-lg px-3 py-1.5 sm:px-4 sm:py-2 shadow-lg">
              <p className="font-semibold text-gray-900 text-sm sm:text-base">Nathan Taylor</p>
              <p className="text-xs sm:text-sm text-gray-600">COO PARTLY</p>
            </div>
          </div>
   
          <div className="max-w-md lg:max-w-lg text-center lg:text-left">
            <div className="flex items-center justify-center lg:justify-start gap-2 mb-4 sm:mb-6">
              <span className="text-white text-xl sm:text-2xl font-bold">PARTLY</span>
            </div>
            <div className="relative">
              <svg className="w-6 h-6 sm:w-8 sm:h-8 text-gray-400 mb-3 sm:mb-4 mx-auto lg:mx-0" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
              </svg>
              <p className="text-white text-base sm:text-lg leading-relaxed">
                Thanks to their continuous monitoring and clear communication, we&apos;ve built a more resilient and security-aware development process.
              </p>
            </div>
          </div> 
           
        </div>
 
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 sm:gap-12 lg:gap-20"> 
          <div className="text-white text-center lg:text-left">
            <div className="relative mb-4 sm:mb-6">
              <div className="relative bg-white p-2 sm:p-1 rounded-2xl">
                <Image 
                  src="/images/Shai_WhipAround.jpg" 
                  alt="Shai Bhula - Chief Technology Officer at Whip Around" 
                  width={400}
                  height={300}
                  className="w-full object-cover rounded-xl"
                />
              </div>
              <div className="absolute bottom-2 sm:bottom-4 left-2 sm:left-4 bg-white rounded-lg px-2 py-1 sm:px-4 sm:py-2 shadow-lg">
                <p className="font-semibold text-gray-900 text-xs sm:text-sm">Shai Bhula</p>
                <p className="text-xs text-gray-600">Chief Technology Officer, Whip Around</p>
              </div>
            </div>
            
            <div className="flex items-center justify-center lg:justify-start gap-2 mb-3 sm:mb-4"> 
              <span className="text-white text-lg sm:text-2xl font-bold">Whip Around</span>
            </div>
            <div className="relative">
              <svg className="w-6 h-6 sm:w-8 sm:h-8 text-gray-400 mb-3 sm:mb-4 mx-auto lg:mx-0" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
              </svg>
              <p className="text-white text-sm sm:text-base leading-relaxed">
                The platform made it easy to scope, schedule, and track the test in real time-no long email chains or delays.
              </p>
            </div>
          </div>

          <div className="text-white text-center lg:text-left">
            <div className="relative mb-4 sm:mb-6">
              <div className="relative bg-white p-2 sm:p-1 rounded-2xl">
                <Image 
                  src="/images/Sarah-Webb.png" 
                  alt="Sarah Webb - Chief Operating Officer at LawVu" 
                  width={400}
                  height={300}
                  className="w-full object-cover rounded-xl"
                />
              </div>
              <div className="absolute bottom-2 sm:bottom-4 left-2 sm:left-4 bg-white rounded-lg px-2 py-1 sm:px-4 sm:py-2 shadow-lg">
                <p className="font-semibold text-gray-900 text-xs sm:text-sm">Sarah Webb</p>
                <p className="text-xs text-gray-600">Chief Operating Officer, LawVu</p>
              </div>
            </div>
            
            <div className="flex items-center justify-center lg:justify-start gap-2 mb-3 sm:mb-4"> 
              <span className="text-white text-lg sm:text-2xl font-bold">LawVu</span>
            </div>
            <div className="relative">
              <svg className="w-6 h-6 sm:w-8 sm:h-8 text-gray-400 mb-3 sm:mb-4 mx-auto lg:mx-0" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
              </svg>
              <p className="text-white text-sm sm:text-base leading-relaxed">
                Capture The Bug&apos;s continuous pentesting approach has been a game-changer for us at LawVu.
              </p>
            </div>
          </div>
 
          <div className="text-white text-center lg:text-left sm:col-span-2 lg:col-span-1">
            <div className="relative mb-4 sm:mb-6">
              <div className="relative bg-white p-2 sm:p-1 rounded-2xl">
                <Image 
                  src="/images/Labuschagne_Paysauce.jpg" 
                  alt="Jacques Labuschagne - CTO at PaySauce" 
                  width={400}
                  height={300}
                  className="w-full object-cover rounded-xl"
                />
              </div>
              <div className="absolute bottom-2 sm:bottom-4 left-2 sm:left-4 bg-white rounded-lg px-2 py-1 sm:px-4 sm:py-2 shadow-lg">
                <p className="font-semibold text-gray-900 text-xs sm:text-sm">Jacques Labuschagne</p>
                <p className="text-xs text-gray-600">CTO, PaySauce</p>
              </div>
            </div>
            
            <div className="flex items-center justify-center lg:justify-start gap-2 mb-3 sm:mb-4">
              <span className="text-white font-bold text-lg sm:text-xl">PaySauce</span>
            </div>
            <div className="relative">
              <svg className="w-6 h-6 sm:w-8 sm:h-8 text-gray-400 mb-3 sm:mb-4 mx-auto lg:mx-0" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
              </svg>
              <p className="text-white text-sm sm:text-base leading-relaxed">
                We would highly recommend Capture The Bug to anyone who needs continuous assurance and speed without compromising depth.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}