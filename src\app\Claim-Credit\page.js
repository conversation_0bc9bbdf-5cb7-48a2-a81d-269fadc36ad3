import ClaimCredits from './claimcredits';
 
export const metadata = {
  title: "Capture The Bug | Claim Your Pentest Credits",
  description: "Claim your pentesting credits and ship faster with real-time vulnerability testing, compliance-ready reports, and expert-led security validation.",
keywords: "claim pentest credits, free pentesting trial, PTaaS credits, real-time vulnerability testing, Capture The Bug credit, secure product launches, security testing offer",  robots: "index, follow",
  icons: {
    icon: '/favicon.ico',
  },
  openGraph: {
    title: "Capture The Bug | Claim Your Pentest Credits",
    type: "website",
    url: "https://capturethebug.xyz/Claim-Credit",
    description: "Ship fast and earn trust. Claim your pentesting credits with Capture The Bug and secure your app with real-time vulnerability testing.",
    images: "https://postimg.cc/V5YtsCft",
  },
  twitter: {
    card: 'summary_large_image',
    title: "Capture The Bug | Claim Your Pentest Credits",
    description: "Claim free pentest credits and test your product before launch. Get real-time results, expert validation, and compliance-ready reports.",
    images: "https://postimg.cc/V5YtsCft",
  }
};


export default function Page() {
  return <ClaimCredits />;
}