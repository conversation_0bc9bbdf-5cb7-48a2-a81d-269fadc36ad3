import React from "react";
import PartnersList from "@/app/Product/Penetration-Testing/components/PartnersList";
import BlogSection from "@/app/Home/components/Blogs";
import Landing from "./components/Landing";
import AutomotiveSecurity from "./components/AutomotiveSecurity";
import Testimonial from "../Testimonial";

export const metadata = {
  title: "Capture The Bug | Automotive & Fleet Cybersecurity Security",
  description:
    "Secure connected vehicles, smart mobility systems, and fleet infrastructure from cyber threats with Capture The Bug’s advanced penetration testing platform.",
  keywords:
    "automotive cybersecurity, connected vehicle security, fleet penetration testing, vehicle API testing, smart mobility infrastructure, OEM cyber defense, Capture The Bug",
  robots: "index, follow",
  icons: {
    icon: "/favicon.ico",
  },
  openGraph: {
    title: "Capture The Bug | Automotive & Fleet Cybersecurity Security",
    type: "website",
    url: "https://capturethebug.xyz/Industries/Automotive&Transportation",
    description:
      "Protect vehicle APIs, mobility infrastructure, and smart transportation platforms with Capture The Bug. Cybersecurity tailored for OEMs and mobility innovators.",
    images: "https://ibb.co/fYh65pgX",  
  },
  twitter: {
    card: "summary_large_image",
    title: "Capture The Bug | Automotive & Fleet Cybersecurity Security",
    description:
      "Capture The Bug secures connected fleets, smart mobility systems, and OEM platforms from cyber threats before attackers strike.",
    images: "https://ibb.co/fYh65pgX",
  },
};

const testimonial = {
  company: "EROAD",
  logo: "/images/EROAD_Logo.webp",
  quote: "Traditional pentesting from independent vendors just didn't scale for a business like ours. Waiting weeks for a final PDF report meant we couldn't act fast enough, and the process always felt disconnected from how our teams actually work. With Capture The Bug's PTaaS platform, that's changed for the better. Now, every time we launch a test - whether it's web, mobile, or infrastructure - we start getting actionable vulnerabilities much faster. It fits right into our existing workflows, so that we can react much more quickly. The real-time visibility, continuous updates, and integration with our reporting cycles mean I'm no longer chasing static reports before board meetings. We have live insights into what's open. It's given us a much faster, more scalable, and far more transparent way to manage our independent vendor offensive security-without compromising on depth or quality",
  author: "Jeremy Peaks",
  position: "Director of Engineering - Security "
};
export default function AutomotiveSecurityLanding() {
  return (
    <>
    <Landing/>
   <AutomotiveSecurity/>
   <PartnersList/>
   <Testimonial
   company={testimonial.company}
   logo={testimonial.logo}
   quote={testimonial.quote}
   author={testimonial.author}
   position={testimonial.position}
   logoSize={{ width: 140, height: 100 }}
   logoStyle={{ marginLeft: 0 }}
      />
     <BlogSection/> 
</>
  );
}