"use client";

import React from 'react';
import Link from "next/link";
import { Search, GitBranch, BarChart3, <PERSON><PERSON>, ArrowRight } from 'lucide-react';
import { motion } from 'framer-motion';
import Button from "@/app/common/buttons/Button";

const ToolsForGrowth = () => {
  const mainFeatures = [
    {
      icon: Search,
      title: "Complete Visibility",
      description: "Get a unified dashboard to track testing progress, vulnerabilities, and remediation across multiple apps, teams, and business units-so nothing falls through the cracks.",
      iconBg: "bg-[#F8F5F7]",
      iconColor: "text-ctb-green-50"
    },
    {
      icon: GitBranch,
      title: "Role-Based Access Control (RBAC)",
      description: "Assign granular permissions for engineers, project managers, and security teams. Collaborate securely without bottlenecks or overexposure.",
      iconBg: "bg-[#F8F5F7]",
      iconColor: "text-ctb-green-50"
    },
    {
      icon: BarChart3,
      title: "Compliance-Ready Reports",
      description: "Get clear, audit-friendly reports mapped to frameworks like SOC 2, ISO 27001, and PCI DSS-ready to share with auditors, clients, or partners.",
      iconBg: "bg-[#F8F5F7]",
      iconColor: "text-ctb-green-50"
    }
  ];

  const additionalFeatures = [
    {
      icon: Search,
      title: "Multi-project Support",
      description: "Run concurrent or recurring tests across web, mobile, APIs, and internal assets. Ideal for product portfolios, subsidiaries, and multi-tenant environments.",
      iconBg: "bg-[#F8F5F7]",
      iconColor: "text-ctb-green-50"
    },
    {
      icon: GitBranch,
      title: "Integrations that Scale",
      description: "Connect with tools like Jira, Slack to sync issues, manage users, and automate remediation workflows.",
      iconBg: "bg-[#F8F5F7]",
      iconColor: "text-ctb-green-50"
    },
    {
      icon: BarChart3,
      title: "SLA-driven Testing & Support",
      description: "Set SLAs for vulnerability response, test scheduling, and reporting. Enterprise support ensures we're responsive when you need us most.",
      iconBg: "bg-[#F8F5F7]",
      iconColor: "text-ctb-green-50"
    }
  ];

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="bg-[#F8F5F7] p-8 sm:p-20 w-full m-0"
    >
      <div className="w-full md:px-12 lg:px-16">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <motion.div 
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.1 }}
            className="text-center mb-12 md:mb-16"
          >
            <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-secondary-blue leading-tight">
              Enterprise-grade Features for Security at Scale
            </h1>
            <p className="text-base md:text-lg text-gray-600 mt-4 max-w-3xl mx-auto">
              Give your security and compliance teams what they need to move fast-without cutting corners.
            </p>
          </motion.div>

          {/* Main Features Grid - Fixed to 3 columns */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 mb-12 md:mb-16">
            {mainFeatures.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <motion.div 
                  key={index}
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.4, delay: 0.1 + (index * 0.05) }}
                  whileHover={{ 
                    y: -8, 
                    transition: { duration: 0.2 } 
                  }}
                  className="bg-white rounded-2xl p-6 lg:p-7 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-200 group cursor-pointer"
                >
                  <div className="flex flex-col items-center lg:items-start text-center lg:text-left h-full">
                    {/* Icon */}
                    <motion.div 
                      className={`w-14 h-14 ${feature.iconBg} rounded-xl flex items-center justify-center mb-5 group-hover:scale-110 transition-transform duration-200`}
                      whileHover={{ rotate: 5 }}
                    >
                      <IconComponent className={`w-6 h-6 ${feature.iconColor}`} />
                    </motion.div>
                    
                    {/* Title */}
                    <h3 className="text-lg font-bold text-secondary-blue mb-4 group-hover:text-primary-blue transition-colors duration-200">
                      {feature.title}
                    </h3>
                    
                    {/* Description */}
                    <p className="text-sm text-gray-600 leading-relaxed flex-grow">
                      {feature.description}
                    </p>
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* Additional Features Grid - Already has 3 columns */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 mb-12 md:mb-16">
            {additionalFeatures.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <motion.div 
                  key={index + 4}
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.4, delay: 0.3 + (index * 0.05) }}
                  whileHover={{ 
                    y: -8, 
                    transition: { duration: 0.2 } 
                  }}
                  className="bg-white rounded-2xl p-6 lg:p-7 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-200 group cursor-pointer"
                >
                  <div className="flex flex-col items-center lg:items-start text-center lg:text-left h-full">
                    {/* Icon */}
                    <motion.div 
                      className={`w-14 h-14 ${feature.iconBg} rounded-xl flex items-center justify-center mb-5 group-hover:scale-110 transition-transform duration-200`}
                      whileHover={{ rotate: 5 }}
                    >
                      <IconComponent className={`w-6 h-6 ${feature.iconColor}`} />
                    </motion.div>
                    
                    {/* Title */}
                    <h3 className="text-lg font-bold text-secondary-blue mb-4 group-hover:text-primary-blue transition-colors duration-200">
                      {feature.title}
                    </h3>
                    
                    {/* Description */}
                    <p className="text-sm text-gray-600 leading-relaxed flex-grow">
                      {feature.description}
                    </p>
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* CTA Button */}
          <motion.div 
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.7 }}
            className="text-center"
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                href="/Request-Demo"
                variant="primary"
                size="lg"
                rightIcon={
                  <motion.div
                    animate={{ x: [0, 4, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    <ArrowRight className="w-4 h-4" />
                  </motion.div>
                }
              >
                Request a demo
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default ToolsForGrowth;