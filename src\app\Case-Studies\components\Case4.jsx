import DarkButton from "@/app/common/buttons/DarkButton";
import Image from "next/image";
import React from "react";

export default function Case1() {
  return (
    <div className="md:px-4 md:py-12 p-8 flex md:flex-row flex-col justify-between items-start">
      <div className="Content md:w-[62%] w-full flex flex-col md:gap-8 gap-6 md:pl-10">
        <div className="Title text-lg md:text-xl lg:text-2xl text-black py-2 flex flex-col md:flex-row md:items-center md:gap-2">
          <div className="font-bold">Industry:</div>
          <span className="text-lg md:text-xl lg:text-2xl">
            Parking Management Software
          </span>
        </div>

        <div className="Title text-lg md:text-xl lg:text-2xl text-black py-2 flex flex-col md:flex-row md:items-center md:gap-2">
          <div className="font-bold">Services Provided:</div>
          <span className="text-lg md:text-xl lg:text-2xl">
            Vulnerability Assessment & Penetration Testing
          </span>
        </div>

        <div className="description leading-8 md:pr-10 text-slate-800 text-base md:text-lg lg:text-xl text-justify">
          Our client is a prominent provider of parking management solutions
          that enable businesses and property owners to efficiently manage
          parking spaces through a user-friendly app. As the platform expanded
          its user base, securing sensitive data and ensuring platform
          resilience became essential to maintaining customer trust.
        </div>

        <div className="button flex gap-2 mt-4">
          <a
            href="/Parking Management Software Case Study.pdf"
            download="Parking Management Software Case Study.pdf"
          >
            <DarkButton className="px-8 py-4 text-sm md:text-lg">
              Download Success Story
            </DarkButton>
          </a>
        </div>
      </div>
      <div className="Image md:w-[35%] w-full md:pr-10 md:mt-0 mt-10 mb-6 md:mb-0">
        <Image
          src="/images/Case 4-img.png"
          width={750}
          height={550}
          alt="Security Testing"
        />
      </div>
    </div>
  );
}
