import PropTypes from 'prop-types';
import clsx from 'clsx';

const DarkButton = ({ children, onClick, icon, className }) => {
  return (
    <button 
      className={clsx(
        "bg-secondary-blue hover:bg-primary-blue text-white py-1 px-4 rounded-lg text-md flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg group relative overflow-hidden",
        className
      )}
      onClick={onClick}
      aria-label="gradient button"
    >
      <span className="absolute inset-0 w-full h-full bg-white/10 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></span>
      <span className="relative z-10">{children}</span>
      {icon && <span className="ml-2 relative z-10">{icon}</span>}
    </button>
  );
};

DarkButton.propTypes = {
  children: PropTypes.node.isRequired,
  onClick: PropTypes.func,
  icon: PropTypes.node,
  className: PropTypes.string,
};

export default DarkButton;
