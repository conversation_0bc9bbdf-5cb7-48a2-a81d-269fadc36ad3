"use client"
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

export default function WiredSkeletonLoader() {
  const [loading, setLoading] = useState(true);
  const [hovering, setHovering] = useState(false);
  const [pulseEffect, setPulseEffect] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [energyPulse, setEnergyPulse] = useState(false);
  
  // Simulate loading and verification
  useEffect(() => {
    // Loading animation
    const timer = setTimeout(() => {
      setLoading(false);
    }, 2000);
    
    // Periodic pulse animations
    const pulseTimer = setInterval(() => {
      setPulseEffect(true);
      setTimeout(() => setPulseEffect(false), 800);
    }, 5000);
    
    // Energy flow animations
    const energyTimer = setInterval(() => {
      setEnergyPulse(true);
      setTimeout(() => setEnergyPulse(false), 1500);
    }, 3000);
    
    return () => {
      clearTimeout(timer);
      clearInterval(pulseTimer);
      clearInterval(energyTimer);
    };
  }, []);
  
  // Toggle verification state on click
  const toggleVerification = () => {
    setIsVerified(!isVerified);
    setPulseEffect(true);
    setEnergyPulse(true);
    setTimeout(() => setPulseEffect(false), 800);
    setTimeout(() => setEnergyPulse(false), 1500);
  };

  return (
    <div className="w-full h-full flex items-center justify-center">
      <div 
        className="relative w-full max-w-xs"
        onMouseEnter={() => setHovering(true)}
        onMouseLeave={() => setHovering(false)}
      >
        {/* Skeleton loading card with enhanced appearance */}
        <motion.div 
          className="bg-white rounded-lg border border-gray-200 p-4 flex flex-col gap-3 relative overflow-hidden"
          whileHover={{ boxShadow: "0 8px 24px rgba(0,0,0,0.08)", y: -3 }}
          animate={pulseEffect ? { scale: [1, 1.02, 1] } : {}}
          transition={{ duration: 0.5 }}
        >
          {/* Circuit pattern background */}
          <div className="absolute inset-0 opacity-5 pointer-events-none">
            <svg width="100%" height="100%" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10,10 L90,10 L90,90 L10,90 Z" stroke="#0835A7" strokeWidth="0.5" fill="none" />
              <path d="M20,10 L20,30 L40,30 L40,50 L60,50 L60,70 L80,70 L80,90" stroke="#0835A7" strokeWidth="0.5" fill="none" />
              <path d="M10,30 L30,30 L30,70 L50,70 L50,90" stroke="#0835A7" strokeWidth="0.5" fill="none" />
              <path d="M70,10 L70,40 L90,40" stroke="#0835A7" strokeWidth="0.5" fill="none" />
              <circle cx="20" cy="30" r="2" fill="#0835A7" opacity="0.5" />
              <circle cx="40" cy="50" r="2" fill="#0835A7" opacity="0.5" />
              <circle cx="60" cy="50" r="2" fill="#0835A7" opacity="0.5" />
              <circle cx="30" cy="70" r="2" fill="#0835A7" opacity="0.5" />
              <circle cx="70" cy="40" r="2" fill="#0835A7" opacity="0.5" />
            </svg>
          </div>
          
          {/* Energy glow effect when pulsing */}
          <AnimatePresence>
            {energyPulse && (
              <motion.div 
                className="absolute inset-0 bg-blue-500 pointer-events-none"
                initial={{ opacity: 0 }}
                animate={{ opacity: 0.05 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.8 }}
              />
            )}
          </AnimatePresence>

          {/* Connection points - top */}
          <div className="absolute -top-1 left-1/4 w-1 h-1 bg-blue-500 rounded-full">
            <motion.div 
              className="absolute inset-0 rounded-full bg-blue-400"
              animate={{ 
                opacity: [0.8, 0.2, 0.8],
                scale: [1, 1.5, 1]
              }}
              transition={{ duration: 2, repeat: Infinity }}
            />
          </div>
          
          <div className="absolute -top-1 right-1/4 w-1 h-1 bg-blue-500 rounded-full">
            <motion.div 
              className="absolute inset-0 rounded-full bg-blue-400"
              animate={{ 
                opacity: [0.8, 0.2, 0.8],
                scale: [1, 1.5, 1]
              }}
              transition={{ duration: 2, repeat: Infinity, delay: 0.7 }}
            />
          </div>

          {/* Enhanced skeleton loading bars */}
          <motion.div 
            className="h-3 bg-gradient-to-r from-gray-100 to-gray-200 rounded-full w-full relative overflow-hidden"
            animate={{ 
              opacity: loading ? [0.6, 0.8, 0.6] : 0.7,
              width: pulseEffect && !loading ? ["100%", "95%", "100%"] : "100%"
            }}
            transition={{ 
              opacity: { duration: 1.5, repeat: Infinity },
              width: { duration: 0.8 }
            }}
          >
            {/* Power flow animation */}
            <motion.div 
              className="absolute top-0 h-full bg-blue-300 opacity-30"
              style={{ width: '30%' }}
              animate={{ 
                x: ['-100%', '400%'],
              }}
              transition={{ 
                duration: 2.5, 
                repeat: Infinity,
                ease: "linear"
              }}
            />
          </motion.div>
          
          <motion.div 
            className="h-3 bg-gradient-to-r from-gray-100 to-gray-200 rounded-full relative overflow-hidden"
            animate={{ 
              opacity: loading ? [0.6, 0.8, 0.6] : 0.7,
              width: pulseEffect && !loading ? ["100%", "85%", "100%"] : "100%"
            }}
            transition={{ 
              opacity: { duration: 1.5, repeat: Infinity, delay: 0.2 },
              width: { duration: 0.8, delay: 0.1 }
            }}
          >
            {/* Power flow animation */}
            <motion.div 
              className="absolute top-0 h-full bg-blue-300 opacity-30"
              style={{ width: '30%' }}
              animate={{ 
                x: ['-100%', '400%'],
              }}
              transition={{ 
                duration: 2.5, 
                repeat: Infinity,
                delay: 0.8,
                ease: "linear"
              }}
            />
          </motion.div>
          
          <motion.div 
            className="h-3 bg-gradient-to-r from-gray-100 to-gray-200 rounded-full relative overflow-hidden"
            animate={{ 
              opacity: loading ? [0.6, 0.8, 0.6] : 0.7,
              width: pulseEffect && !loading ? ["100%", "90%", "100%"] : "100%"
            }}
            transition={{ 
              opacity: { duration: 1.5, repeat: Infinity, delay: 0.4 },
              width: { duration: 0.8, delay: 0.2 }
            }}
          >
            {/* Power flow animation */}
            <motion.div 
              className="absolute top-0 h-full bg-blue-300 opacity-30"
              style={{ width: '30%' }}
              animate={{ 
                x: ['-100%', '400%'],
              }}
              transition={{ 
                duration: 2.5, 
                repeat: Infinity,
                delay: 0.4,
                ease: "linear"
              }}
            />
          </motion.div>
          
          {/* Connection points - bottom */}
          <div className="absolute -bottom-1 left-1/3 w-1 h-1 bg-blue-500 rounded-full">
            <motion.div 
              className="absolute inset-0 rounded-full bg-blue-400"
              animate={{ 
                opacity: [0.8, 0.2, 0.8],
                scale: [1, 1.5, 1]
              }}
              transition={{ duration: 2, repeat: Infinity, delay: 0.3 }}
            />
          </div>
          
          {/* Enhanced internal connection lines */}
          <div className="absolute left-0 top-1/2 -translate-y-1/2 w-0 h-16">
            <motion.svg 
              width="40" 
              height="64" 
              viewBox="0 0 40 64" 
              fill="none" 
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="-ml-8"
            >
              {/* Main connection path */}
              <motion.path
                d="M1,32 L20,32 L20,10 L38,10"
                stroke="#0835A7"
                strokeWidth="1.5"
                strokeDasharray="3 2"
                strokeLinecap="round"
                fill="none"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ duration: 1.2, delay: 0.2 }}
              />
              
              <motion.path
                d="M1,32 L20,32 L20,54 L38,54"
                stroke="#0835A7"
                strokeWidth="1.5"
                strokeDasharray="3 2"
                strokeLinecap="round"
                fill="none"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ duration: 1.2, delay: 0.5 }}
              />
              
              {/* Glowing effect for paths */}
              <motion.path
                d="M1,32 L20,32 L20,10 L38,10"
                stroke="#0835A7"
                strokeWidth="1"
                strokeLinecap="round"
                fill="none"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: energyPulse ? 1 : 0 }}
                transition={{ duration: 1 }}
                style={{ filter: "blur(1px)" }}
              />
              
              <motion.path
                d="M1,32 L20,32 L20,54 L38,54"
                stroke="#0835A7"
                strokeWidth="1"
                strokeLinecap="round"
                fill="none"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: energyPulse ? 1 : 0 }}
                transition={{ duration: 1, delay: 0.3 }}
                style={{ filter: "blur(1px)" }}
              />
              
              {/* Connection nodes */}
              <motion.circle cx="20" cy="32" r="2" fill="#0835A7" 
                animate={{ 
                  r: energyPulse ? [2, 3, 2] : 2,
                  opacity: [0.7, 1, 0.7]
                }}
                transition={{ duration: 1.5, repeat: Infinity }}
              />
              
              <motion.circle cx="20" cy="10" r="2" fill="#0835A7" 
                animate={{ 
                  r: energyPulse ? [2, 2.5, 2] : 2,
                  opacity: [0.7, 1, 0.7]
                }}
                transition={{ duration: 1.5, repeat: Infinity, delay: 0.3 }}
              />
              
              <motion.circle cx="20" cy="54" r="2" fill="#0835A7" 
                animate={{ 
                  r: energyPulse ? [2, 2.5, 2] : 2,
                  opacity: [0.7, 1, 0.7]
                }}
                transition={{ duration: 1.5, repeat: Infinity, delay: 0.6 }}
              />
              
              {/* Animated energy particles */}
              <motion.circle
                r="1.8"
                fill="#0835A7"
                animate={{
                  opacity: [0, 0.9, 0],
                  offsetDistance: ['0%', '100%']
                }}
                style={{
                  offsetPath: "path('M1,32 L20,32 L20,10 L38,10')"
                }}
                transition={{
                  duration: 1.8,
                  repeat: Infinity,
                  repeatType: "loop",
                  ease: "easeInOut",
                  delay: 0.2
                }}
              />
              
              <motion.circle
                r="1.8"
                fill="#0835A7"
                animate={{
                  opacity: [0, 0.9, 0],
                  offsetDistance: ['0%', '100%']
                }}
                style={{
                  offsetPath: "path('M1,32 L20,32 L20,54 L38,54')"
                }}
                transition={{
                  duration: 1.8,
                  repeat: Infinity,
                  repeatType: "loop",
                  ease: "easeInOut",
                  delay: 0.7
                }}
              />
              
              {/* Secondary particles (smaller) */}
              <motion.circle
                r="1.2"
                fill="#0835A7"
                animate={{
                  opacity: [0, 0.7, 0],
                  offsetDistance: ['0%', '100%']
                }}
                style={{
                  offsetPath: "path('M1,32 L20,32 L20,10 L38,10')"
                }}
                transition={{
                  duration: 2.2,
                  repeat: Infinity,
                  repeatType: "loop",
                  ease: "easeInOut",
                  delay: 1.2
                }}
              />
              
              <motion.circle
                r="1.2"
                fill="#0835A7"
                animate={{
                  opacity: [0, 0.7, 0],
                  offsetDistance: ['0%', '100%']
                }}
                style={{
                  offsetPath: "path('M1,32 L20,32 L20,54 L38,54')"
                }}
                transition={{
                  duration: 2.2,
                  repeat: Infinity,
                  repeatType: "loop",
                  ease: "easeInOut",
                  delay: 1.7
                }}
              />
            </motion.svg>
          </div>
        </motion.div>
        
        {/* Enhanced dotted line connector with power flow */}
        <motion.div 
          className="absolute -bottom-10 left-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.5 }}
        >
          <svg width="36" height="40" className="absolute -top-2 -left-2">
            {/* Main path */}
            <motion.path
              d="M2,0 L2,30 L36,30"
              stroke="#0835A7"
              strokeWidth="1.8"
              strokeDasharray="3 3"
              strokeLinecap="round"
              fill="none"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 1, delay: 0.3 }}
            />
            
            {/* Power flow path - solid underneath for glow effect */}
            <motion.path
              d="M2,0 L2,30 L36,30"
              stroke="#0835A7"
              strokeWidth="1"
              strokeLinecap="round"
              fill="none"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: energyPulse ? 1 : 0 }}
              transition={{ duration: 1.2 }}
              style={{ filter: "blur(2px)" }}
            />
            
            {/* Connection points */}
            <motion.circle
              cx="2" cy="0" r="2"
              fill="#0835A7"
              animate={{ 
                opacity: [0.7, 1, 0.7],
                r: energyPulse ? [2, 2.5, 2] : 2
              }}
              transition={{ duration: 1.5, repeat: Infinity }}
            />
            
            <motion.circle
              cx="2" cy="30" r="2"
              fill="#0835A7"
              animate={{ 
                opacity: [0.7, 1, 0.7],
                r: energyPulse ? [2, 2.5, 2] : 2
              }}
              transition={{ duration: 1.5, repeat: Infinity, delay: 0.5 }}
            />
            
            <motion.circle
              cx="36" cy="30" r="2"
              fill="#0835A7"
              animate={{ 
                opacity: [0.7, 1, 0.7],
                r: energyPulse ? [2, 2.5, 2] : 2
              }}
              transition={{ duration: 1.5, repeat: Infinity, delay: 1 }}
            />
            
            {/* Animated dots that travel along the path */}
            <motion.circle
              r="2.5"
              fill="#0835A7"
              animate={{
                opacity: [0, 0.8, 0],
                offsetDistance: ['0%', '100%']
              }}
              style={{
                offsetPath: "path('M2,0 L2,30 L36,30')"
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatType: "loop",
                ease: "easeInOut",
                delay: 0.5
              }}
            />
            
            {hovering && (
              <>
              <motion.circle
                  r="2"
                fill="#0835A7"
                animate={{
                    opacity: [0, 0.9, 0],
                  offsetDistance: ['0%', '100%']
                }}
                style={{
                  offsetPath: "path('M2,0 L2,30 L36,30')"
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  repeatType: "loop",
                  ease: "easeInOut"
                }}
              />
                
                <motion.circle
                  r="1.5"
                  fill="#0835A7"
                  animate={{
                    opacity: [0, 0.7, 0],
                    offsetDistance: ['0%', '100%']
                  }}
                  style={{
                    offsetPath: "path('M2,0 L2,30 L36,30')"
                  }}
                  transition={{
                    duration: 1.8,
                    repeat: Infinity,
                    repeatType: "loop",
                    ease: "easeInOut",
                    delay: 1
                  }}
                />
              </>
            )}
          </svg>
          
          {/* Enhanced verification button */}
          <motion.div 
            className={`${isVerified ? 'bg-emerald-500' : 'bg-primary-blue'} text-white text-xs py-2 px-4 rounded-full flex items-center cursor-pointer relative overflow-hidden`}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 1.3 }}
            whileHover={{ scale: 1.05, boxShadow: "0 3px 10px rgba(0,0,0,0.15)" }}
            whileTap={{ scale: 0.95 }}
            onClick={toggleVerification}
          >
            {/* Background energy effect */}
            <motion.div
              className="absolute inset-0 bg-white"
              animate={{
                opacity: energyPulse ? [0, 0.2, 0] : 0
              }}
              transition={{ duration: 1 }}
            />
            
            {/* Moving particles in button */}
            {[...Array(5)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 rounded-full bg-white"
                style={{
                  left: `${10 + (i * 20)}%`,
                  top: `${30 + (i % 3) * 20}%`
                }}
                animate={{
                  opacity: [0, 0.6, 0],
                  y: [0, -10, 0],
                  x: [0, 5, 0]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.3,
                  repeatType: "reverse"
                }}
              />
            ))}
            
            <AnimatePresence mode="wait">
              {isVerified ? (
                <motion.svg 
                  className="w-5 h-5 mr-2 flex-shrink-0"
                  key="check-icon"
                  viewBox="0 0 24 24" 
                  fill="none"
                  initial={{ scale: 0, rotate: -90 }}
                  animate={{ scale: 1, rotate: 0 }}
                  exit={{ scale: 0, rotate: 90 }}
                  transition={{ type: "spring", stiffness: 300, damping: 15 }}
                >
                  <path 
                    d="M20 6L9 17L4 12"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </motion.svg>
              ) : (
                <motion.svg 
                  className="w-5 h-5 mr-2 flex-shrink-0" 
                  key="shield-icon"
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                  initial={{ scale: 0, rotate: 90 }}
                  animate={{ scale: 1, rotate: 0 }}
                  exit={{ scale: 0, rotate: -90 }}
                  transition={{ type: "spring", stiffness: 300, damping: 15 }}
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth="2" 
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" 
                  />
                </motion.svg>
              )}
            </AnimatePresence>
            <span className="font-medium whitespace-nowrap relative z-10">
              {isVerified ? "Vulnerability verified" : "Every vulnerability is verified"}
            </span>
            
            {/* Enhanced pulse effect around button when state changes */}
            <AnimatePresence>
              {pulseEffect && (
                <>
                <motion.div
                  className={`absolute inset-0 rounded-full ${isVerified ? 'border-emerald-500' : 'border-primary-blue'}`}
                  initial={{ scale: 1, opacity: 0.7 }}
                    animate={{ scale: 1.6, opacity: 0 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.8 }}
                />
                  <motion.div
                    className={`absolute inset-0 rounded-full ${isVerified ? 'border-emerald-500' : 'border-primary-blue'}`}
                    initial={{ scale: 1, opacity: 0.5 }}
                    animate={{ scale: 1.3, opacity: 0 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                  />
                </>
              )}
            </AnimatePresence>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
} 