import BlogSection from './Blogs';
 
export const metadata = {
  title: "Capture The Bug | Cybersecurity Blog & Insights",
  description: "Explore expert insights, PTaaS updates, threat analysis, and modern security frameworks. Stay ahead of cyber risks with Capture The Bug's latest articles.",
  keywords:"cybersecurity blog, offensive security insights, PTaaS trends, penetration testing updates, threat intelligence blog, Capture The Bug articles, security frameworks news",
  robots: "index, follow",
  icons: {
    icon: '/favicon.ico',
  },
  openGraph: {
    title: "Capture The Bug | Cybersecurity Blog & Insights",
    type: "website",
    url: "https://capturethebug.xyz/Blogs",
    description: "Curated cybersecurity content for pros. Read about the latest threats, frameworks, and PTaaS innovations from Capture The Bug.",
    images: "https://i.ibb.co/sJK8gTnG/Screenshot-2025-06-18-221405.png",
  },
  twitter: {
    card: 'summary_large_image',
    title: "Capture The Bug | Cybersecurity Blog & Insights",
    description: "Stay informed with Capture The Bug’s expert-written blogs. Discover updates on pentesting, offensive security, and threat landscape shifts.",
    images: "https://i.ibb.co/sJK8gTnG/Screenshot-2025-06-18-221405.png",
  }
};

export default function Page() {
  return <BlogSection />;
}
