'use client';

import React, { useState } from 'react';
import { Plus, Minus } from 'lucide-react';

const FAQItem = ({ question, answer, isOpen, onToggle, uniqueId }) => (
  <div className="mb-4">
    <button
      onClick={onToggle}
      aria-expanded={isOpen}
      className="flex w-full justify-between items-center text-left bg-gray-100 p-4 rounded-lg transition-all"
    >
      <span className="text-sm md:text-lg font-medium text-gray-900">
        {question}
      </span>
      <span className="ml-4 shrink-0">
        {isOpen ? (
          <Minus className="h-5 w-5 text-[#1e83fb]" />
        ) : (
          <Plus className="h-5 w-5 text-[#1e83fb]" />
        )}
      </span>
    </button>

    <div
      className={`transition-all duration-300 ease-in-out overflow-hidden ${
        isOpen ? 'max-h-[1000px] opacity-100 py-4' : 'max-h-0 opacity-0 py-0'
      } bg-gray-50`}
    >
      <div className="px-4">
        <p className="text-sm md:text-base text-gray-700 leading-relaxed">{answer}</p>
      </div>
    </div>
  </div>
);

const FAQ = () => {
  const [openItem, setOpenItem] = useState(null);

  const handleToggle = (uniqueId) => {
    setOpenItem((prev) => (prev === uniqueId ? null : uniqueId));
  };

  const faqs = [
    {
      id: 'api-pentest-what',
      question: 'What is API Penetration Testing?',
      answer:
        'API Penetration Testing involves simulating attacks on your APIs to identify vulnerabilities such as broken authentication, injection flaws, insecure endpoints, and improper rate limiting. It helps secure REST, GraphQL, and other web APIs from real-world threats.',
    },
    {
      id: 'api-types-tested',
      question: 'What types of APIs do you test?',
      answer:
        'We test RESTful APIs, GraphQL APIs, internal microservice APIs, and third-party integrations. Each test is tailored to the specific protocol, architecture, and use case of the API in question.',
    },
    {
      id: 'api-doc-needed',
      question: 'Do you require documentation for testing our APIs?',
      answer:
        'Providing Postman collections, Swagger/OpenAPI specs, or other API documentation helps speed up testing and ensures full coverage-but we can also work without them by performing endpoint discovery and dynamic analysis.',
    },
    {
      id: 'api-auth-testing',
      question: 'Do you test for authentication and authorization issues?',
      answer:
        'Yes, we simulate broken authentication, token misuse, privilege escalation, and horizontal/vertical authorization bypass scenarios to validate access controls and session management mechanisms.',
    },
    {
      id: 'api-business-logic',
      question: 'Do you test for business logic flaws in APIs?',
      answer:
        'Absolutely. Our testers go beyond technical flaws and analyze business workflows to find logical flaws such as price manipulation, order tampering, privilege misuse, or excessive data exposure.',
    },
    {
      id: 'api-graphql-support',
      question: 'Do you support GraphQL API penetration testing?',
      answer:
        'Yes, we perform advanced GraphQL-specific tests including introspection abuse, injection attacks, batching misuse, and query depth/complexity attacks to ensure complete security.',
    },
    {
      id: 'api-realtime-results',
      question: 'Do we get real-time findings and dashboards?',
      answer:
        'Yes. Our platform offers real-time updates on vulnerabilities, with risk ratings, remediation guidance, and collaboration features to triage findings as they’re discovered.',
    },
    {
      id: 'api-pentest-standards',
      question: 'What standards do you follow in API testing?',
      answer:
        'We follow OWASP API Security Top 10, OWASP Web Security Testing Guide, and industry best practices to ensure your APIs are tested thoroughly and in compliance with regulatory needs.',
    },
    {
      id: 'api-test-frequency',
      question: 'How often should we test our APIs?',
      answer:
        'We recommend testing APIs during major updates, before deployments, and regularly during development sprints. Our PTaaS model supports scheduled and on-demand testing to align with your CI/CD workflow.',
    },
    {
      id: 'api-remediation-help',
      question: 'Do you assist with remediation and retesting of API issues?',
      answer:
        'Yes. We provide detailed reports, mitigation steps, and offer follow-up retesting to ensure that issues have been fixed correctly and your APIs remain secure.',
    },
  ];

  return (
    <section className="w-full py-16 bg-gray-50">
      <div className="max-w-6xl mx-auto px-4">
        <div className="flex flex-col md:flex-row md:gap-20 gap-10">
          <div className="md:w-1/3 w-full flex items-center justify-center">
            <h2 className="text-4xl md:text-6xl font-bold text-[#1e83fb] text-center">
              FAQ
            </h2>
          </div>
          <div className="md:w-2/3 w-full flex flex-col justify-center">
            {faqs.map((faq) => (
              <FAQItem
                key={faq.id}
                uniqueId={faq.id}
                question={faq.question}
                answer={faq.answer}
                isOpen={openItem === faq.id}
                onToggle={() => handleToggle(faq.id)}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
