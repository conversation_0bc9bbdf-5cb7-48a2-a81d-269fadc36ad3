"use client"
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

export default function LazyLoadWrapper({ 
  children,
  height = '200px',
  width = '100%',
  className = ''
}) {
  const [isClient, setIsClient] = useState(false);
  const [hasRevealedContent, setHasRevealedContent] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
    
    // Add slight delay before marking content as revealed for animation
    setTimeout(() => setHasRevealedContent(true), 100);
  }, []);
  
  // Skip the component on server side rendering
  if (!isClient) {
    return <div style={{ minHeight: height, width }}></div>;
  }
  
  return (
    <div className={`relative ${className}`} style={{ minHeight: height, width }}>
      <motion.div 
        className="h-full w-full"
        initial={{ opacity: 0, y: 10 }}
        animate={{ 
          opacity: 1, 
          y: 0,
          transition: { 
            type: 'spring', 
            stiffness: 260, 
            damping: 20,
            duration: 0.6 
          }
        }}
      >
        {/* Premium glow effect that fades in when content is revealed */}
        {hasRevealedContent && (
          <motion.div
            className="absolute inset-0 rounded-lg pointer-events-none"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 1.2 }}
            style={{ 
              boxShadow: '0 8px 32px -4px rgba(8, 53, 167, 0.06)',
              border: '1px solid rgba(8, 53, 167, 0.03)'
            }}
          />
        )}
        
        {children}
      </motion.div>
    </div>
  );
} 