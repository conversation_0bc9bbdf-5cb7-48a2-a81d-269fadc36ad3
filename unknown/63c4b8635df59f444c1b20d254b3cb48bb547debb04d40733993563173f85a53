"use client";

import React from 'react';

const additionalServices = [
  {
    id: "rapid-sizing",
    title: "Rapid Sizing Sprint",
    description: "Fixed 2-day assessment to size complex apps and determine optimal testing approach.",
    price: "$1,200",
    currency: "USD",
    features: [
      "Comprehensive app analysis",
      "Testing strategy recommendation",
      "Detailed sizing report",
      "Expert consultation"
    ],
    icon: (
      <svg className="w-8 h-8 text-[#027bfd]" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9Z"/>
      </svg>
    )
  },
  {
    id: "baseline-blocks",
    title: "Additional Baseline Blocks",
    description: "Extend your initial penetration test with additional testing days for comprehensive coverage.",
    options: [
      { days: "+5 days", price: "$3,600", highlight: false },
      { days: "+10 days", price: "$6,600", highlight: true }
    ],
    note: "Unused blocks roll into your monthly day bank (Year 1).",
    icon: (
      <svg className="w-8 h-8 text-[#027bfd]" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9Z"/>
      </svg>
    )
  }
];

const addOns = [
  {
    id: "extra-day",
    title: "Add Extra Pentest Day",
    description: "Scale up for new features or urgent testing needs with additional testing capacity.",
    price: "$1,080",
    period: "/ Day",
    currency: "USD",
    popular: false,
    icon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M13,7H11V11H7V13H11V17H13V13H17V11H13V7Z"/>
      </svg>
    )
  },
  {
    id: "emergency-retest",
    title: "24-48h Emergency Retest",
    description: "Expedited validation of critical security fixes with priority testing queue.",
    price: "$600",
    period: "/ Case",
    currency: "USD",
    popular: true,
    icon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
        <path d="M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z"/>
      </svg>
    )
  },
  {
    id: "compliance-pack",
    title: "Compliance Report Pack",
    description: "Industry-specific reports tailored for ISO, SOC2, PCI compliance requirements.",
    price: "$300",
    period: "/ Year",
    currency: "USD",
    popular: false,
    icon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
      </svg>
    )
  },
  {
    id: "jira-github",
    title: "Jira / GitHub Sync",
    description: "Seamless workflow integration with your existing development tools and processes.",
    price: "Custom",
    period: "",
    currency: "",
    popular: false,
    icon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
        <path d="M18,16.08C17.24,16.08 16.56,16.38 16.04,16.85L8.91,12.7C8.96,12.47 9,12.24 9,12C9,11.76 8.96,11.53 8.91,11.3L15.96,7.19C16.5,7.69 17.21,8 18,8A3,3 0 0,0 21,5A3,3 0 0,0 18,2A3,3 0 0,0 15,5C15,5.24 15.04,5.47 15.09,5.7L8.04,9.81C7.5,9.31 6.79,9 6,9A3,3 0 0,0 3,12A3,3 0 0,0 6,15C6.79,15 7.5,14.69 8.04,14.19L15.16,18.34C15.11,18.55 15.08,18.77 15.08,19C15.08,20.61 16.39,21.91 18,21.91C19.61,21.91 20.92,20.61 20.92,19A2.92,2.92 0 0,0 18,16.08Z"/>
      </svg>
    )
  }
];

export default function AdditionalServices() {
  return (
    <section className="w-full py-16 sm:py-20 md:py-24 bg-slate-100 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-[#027bfd]/5 to-[#032391]/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-[#032391]/5 to-[#027bfd]/5 rounded-full blur-3xl"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* For Larger or More Complex Applications */}
        <div className="mb-20 sm:mb-24">
          <div className="text-center mb-12 sm:mb-16">
            <h2 className="text-3xl sm:text-4xl md:text-4xl font-bold text-[#06258d] mb-4">
              For Larger or More Complex Applications
            </h2>
            <p className="text-lg sm:text-lg text-[#06258d] max-w-3xl mx-auto">
              Enhanced testing solutions for enterprise-scale applications requiring comprehensive security assessment.
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {additionalServices.map((service) => (
              <div
                key={service.id}
                className="bg-white rounded-3xl shadow-xl border border-gray-200 p-8 sm:p-10 hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 group flex flex-col justify-between min-h-[400px]"
              >
                <div>
                  <div className="flex items-start gap-4 mb-8">
                    <div className="p-3 bg-gradient-to-br from-[#027bfd]/10 to-[#032391]/10 rounded-2xl group-hover:scale-110 transition-transform duration-300">
                      {service.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-gray-900 mb-4">
                        {service.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed text-lg">
                        {service.description}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="mt-auto">
                  {service.options ? (
                    <div className="space-y-4">
                      {service.options.map((option, index) => (
                        <div
                          key={index}
                          className={`flex items-center justify-between p-4 rounded-xl border-2 transition-all duration-200 ${
                            option.highlight 
                              ? 'border-[#027bfd] bg-gradient-to-r from-[#027bfd]/5 to-[#032391]/5' 
                              : 'border-gray-200 hover:border-[#027bfd]/50'
                          }`}
                        >
                          <span className="font-semibold text-gray-900">{option.days}</span>
                          <span className="text-xl font-bold text-[#032391]">{option.price} USD</span>
                        </div>
                      ))}
                      <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
                        <p className="text-sm text-blue-700 font-medium">
                          💡 {service.note}
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      <div className="flex items-center justify-center p-4 rounded-xl border-2 border-[#027bfd] bg-gradient-to-r from-[#027bfd]/5 to-[#032391]/5">
                        <span className="text-3xl font-bold text-[#032391]">{service.price} {service.currency}</span>
                      </div>
                      {service.features && (
                        <div className="space-y-3">
                          {service.features.map((feature, index) => (
                            <div key={index} className="flex items-center gap-2 text-gray-700">
                              <svg className="w-5 h-5 text-[#027bfd]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                              </svg>
                              <span>{feature}</span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Optional Add-Ons */}
        <div>
          <div className="text-center mb-12 sm:mb-16">
            <h2 className="text-3xl sm:text-4xl md:text-4xl font-semibold text-[#06258d] mb-4">
              Optional Add-Ons
            </h2>
            <p className="text-lg sm:text-lg text-[#06258d] max-w-3xl mx-auto mt-6">
              Enhance your security testing with specialized services and integrations.
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-6">
            {addOns.map((addon) => (
              <div
                key={addon.id}
                className={`relative bg-white rounded-3xl shadow-lg border-2 p-6 sm:p-8 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group ${
                  addon.popular 
                    ? 'border-[#027bfd] ring-4 ring-[#027bfd]/10' 
                    : 'border-gray-200 hover:border-[#027bfd]/50'
                }`}
              >
                {addon.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-[#027bfd] text-white px-4 py-1 rounded-full text-sm font-semibold shadow-lg">
                      Popular
                    </span>
                  </div>
                )}

                <div className={`p-3 rounded-2xl w-fit mb-6 group-hover:scale-110 transition-transform duration-300 ${
                  addon.popular 
                    ? 'bg-[#027bfd] text-white' 
                    : 'bg-gradient-to-br from-[#027bfd]/10 to-[#032391]/10 text-[#027bfd]'
                }`}>
                  {addon.icon}
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  {addon.title}
                </h3>
                <p className="text-gray-600 mb-6 leading-relaxed text-sm">
                  {addon.description}
                </p>
                <div className="mt-auto">
                  <div className="text-2xl font-bold text-[#027bfd]">
                    {addon.price === "Custom" ? (
                      <span className="text-[#032391]">Custom Pricing</span>
                    ) : (
                      <>
                        {addon.price} {addon.currency}
                        {addon.period && (
                          <span className="text-base text-gray-600 font-normal ml-1">
                            {addon.period}
                          </span>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Enhanced Footer with static circle */}
          <div className="mt-12 text-center">
            <div className="inline-flex items-center gap-3 bg-white border-2 border-[#027bfd] text-gray-700 px-8 py-4 rounded-full shadow-lg">
              <div className="w-3 h-3 bg-gradient-to-r from-[#027bfd] to-[#032391] rounded-full"></div>
              <span className="font-semibold">
                Up to 2 unused monthly days roll over for 60 days
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
