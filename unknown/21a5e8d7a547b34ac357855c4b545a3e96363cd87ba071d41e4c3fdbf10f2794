import Image from "next/image";
import Link from "next/link";
import React from "react";

export const metadata = {
  openGraph: {
    title: "Capture The Bug | Sign Up",
    type: "website",
    url: "https://capturethebug.xyz/signup",
    description:
      "Join Capture The Bug, the ultimate platform for bug hunters and developers. Sign up to start tracking, reporting, and fixing bugs efficiently. Collaborate with experts, earn rewards, and improve software quality.",
    images: "https://i.postimg.cc/TPKHTXSc/Sign-up.png",
  },
};

export default function page() {
  return (
    <div>
       <title>Capture The Bug | Sign Up</title>
      <div className="flex flex-col items-center p-10 gap-8">
        <div className="Logo">
          <Image
            src="/images/dark_logo.png"
            alt="logo"
            width={300}
            height={500}
          />
        </div>
        <div className="Title md:text-3xl font-bold text-center">
          Are you a Business or a Security Researcher?
        </div>

        <div className="CardsContainer flex gap-10 md:flex-row flex-col">
          <Link
            href="https://app.capturethebug.xyz/register/business"
            target="_blank"
          >
            <div className="card flex flex-col items-center border border-blue-800 p-8 rounded-lg">
              <div className="Image mb-4">
                <Image
                  src="/images/business_signup.png"
                  width={150}
                  height={150}
                  alt="business"
                />
              </div>
              <div className="Title bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent md:text-2xl font-semibold">
                Business
              </div>
              <div className="subTitle text-slate-700 text-center">
                Strengthen the security of your digital assets
              </div>
            </div>
          </Link>

          <Link
            href="https://app.capturethebug.xyz/register/researcher"
            target="_blank"
          >
            <div className="card flex flex-col items-center border border-blue-800 p-8 md:px-20 rounded-lg">
              <div className="Image mb-4">
                <Image
                  src="/images/researcher_signup.png"
                  width={150}
                  height={150}
                  alt="business"
                />
              </div>
              <div className="Title bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent md:text-2xl font-semibold">
                Researcher
              </div>
              <div className="subTitle text-slate-700 text-center">
                Are you ready to Capture Bugs?
              </div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
}