const freeEmailDomains = [
    "gmail.com", "yahoo.com", "hotmail.com", "outlook.com",
    "aol.com", "icloud.com", "zoho.com", "protonmail.com",
    "gmx.com", "yandex.com", "mail.com", "live.com", "me.com",
    "msn.com", "inbox.com"
  ];
  
  const disposableEmailDomains = [
    "temp-mail.org", "1secmail.com", "1secmail.org", "1secmail.net",
    "disposableemailaddresses.com", "mailinator.com", "guerrillamail.com",
    "10minutemail.com", "getnada.com", "throwawaymail.com", "yopmail.com",
    "trashmail.com", "maildrop.cc", "fakeinbox.com", "moakt.com", 
    "meltmail.com", "spambog.com", "mailcatch.com", "spamex.com", 
    "spammotel.com", "spamgourmet.com", "discard.email", "airmail.cc",
    "sharklasers.com", "mytemp.email", "dispostable.com", "tempmailo.com", 
    "emailondeck.com", "mintemail.com", "eyepaste.com", "33mail.com", 
    "tmail.com", "instant-mail.com", "opayq.com", "tmpbox.net", 
    "kasmail.com", "mohmal.com", "anonbox.net", "disbox.org", 
    "easytrashmail.com", "fakemailgenerator.com", "getairmail.com",
    "guerillamail.org", "guerillamailblock.com", "hot-mail.gq", 
    "inboxkitten.com", "mailsac.com", "mail.tm", "mytrashmail.com", 
    "tutanota.com", "privatemail.com", "jetable.org", "temporary-mail.net",
    "crazymailing.com", "heweek.com", "example.com", "test.com"
  ];
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  // Known spam traps
  const spamTraps = [
    "<EMAIL>", "<EMAIL>", "<EMAIL>"
  ];
  
  // Function to check if the email is a business email
  export const isBusinessEmail = (email) => {
    // Check if email is undefined or null
    if (!email) {
      console.log("Email is undefined or null");
      return false;
    }
  
    if (!emailRegex.test(email)) {
      console.log("Invalid email format");
      return false;  
    }
  
    const domain = email.split("@")[1];
  
    if (freeEmailDomains.includes(domain) || disposableEmailDomains.includes(domain)) {
      console.log("Free or disposable domain detected");
      return false;  
    }
  
    if (spamTraps.includes(email.toLowerCase())) {
      console.log("Spam trap detected");
      return false; 
    }
  
    return true;
  };
  