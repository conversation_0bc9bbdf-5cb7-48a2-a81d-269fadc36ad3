
"use client";
import { motion } from 'framer-motion';
import React from 'react'
import CompletePage from './components/CompletePage'
import DemoFeatures from './components/DemoFeatures'; 
import Stats from './components/Stats';
import Testimonials from '../Home/components/Testimonials';
import VendorSecurityReview from './components/Feature';


export default function page() {
  return (
    <div>
      <title>Capture The Bug | Request Demo</title>
        <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
      <CompletePage/>
      </motion.div>
      <DemoFeatures/> 
       <Testimonials/> 
       </motion.div>
    </div>
  )
}
