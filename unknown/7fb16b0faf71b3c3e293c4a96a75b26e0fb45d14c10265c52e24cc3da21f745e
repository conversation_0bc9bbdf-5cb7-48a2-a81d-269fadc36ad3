'use client';
import { motion } from 'framer-motion';
import React from 'react';
import LocationsContent from './components/LocationsContent';

export default function LocationsPage() {
  return (
    <div>
      <title>Capture The Bug | Global Penetration Testing Services</title>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <LocationsContent />
        </motion.div>
      </motion.div>
    </div>
  );
}