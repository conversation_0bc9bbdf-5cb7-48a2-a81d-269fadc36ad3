"use client";

import React, { useState } from 'react';

const FAQItem = ({ question, answer, isOpen, onToggle, uniqueId }) => {
  return (
    <div className="border border-gray-200 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden bg-white">
      <button
        onClick={onToggle}
        aria-expanded={isOpen}
        className="w-full flex justify-between items-center p-6 sm:p-8 text-left focus:outline-none focus:ring-2 focus:ring-[#027bfd] focus:ring-offset-2 group"
      >
        <span className="text-lg sm:text-xl font-semibold text-gray-900 pr-4 group-hover:text-[#027bfd] transition-colors duration-200">
          {question}
        </span>
        <div className={`flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full transition-all duration-300 ${
          isOpen ? 'bg-[#027bfd] text-white rotate-180' : 'bg-gray-100 text-gray-600 group-hover:bg-[#027bfd] group-hover:text-white'
        }`}>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </button>
      
      <div className={`transition-all duration-500 ease-in-out ${
        isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
      } overflow-hidden`}>
        <div className="px-6 sm:px-8 pb-6 sm:pb-8">
          <div className="pt-2 border-t border-gray-100">
            <p className="text-gray-700 leading-relaxed mt-4">
              {answer}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

const FAQ = () => {
  // Only one open at a time
  const [openItem, setOpenItem] = useState(null);

  const handleToggle = (uniqueId) => {
    setOpenItem(prev => (prev === uniqueId ? null : uniqueId));
  };

  const faqs = [
    {
      id: "what-is-vapt",
      question: "What is VAPT?",
      answer: "VAPT stands for Vulnerability Assessment and Penetration Testing. It is a security testing methodology that combines two approaches to identify and address vulnerabilities in an organization's IT systems. The vulnerability assessment phase focuses on identifying potential weaknesses, while penetration testing attempts to exploit these vulnerabilities to assess their impact and exploitability."
    },
    {
      id: "vapt-assessment-includes",
      question: "What does a VAPT assessment include?",
      answer: "A VAPT assessment includes a comprehensive evaluation of your organization's network, applications, and systems. It involves identifying vulnerabilities through automated and manual testing, assessing the risks associated with these vulnerabilities, and providing detailed reports with remediation recommendations."
    },
    {
      id: "upfront-payment",
      question: "Do I need to make an upfront payment?",
      answer: "Payment terms vary depending on the service provider. Capture The Bug typically offers flexible payment options and will discuss terms with you before starting the assessment."
    },
    {
      id: "assessment-duration",
      question: "How long does a VAPT assessment take?",
      answer: "The duration of a VAPT assessment depends on the complexity and size of your network and applications. Typically, it can take anywhere from a few days to several weeks. The exact timeline will be discussed during the initial scoping phase."
    },
    {
      id: "rescan-request",
      question: "Can I request a re-scan to check if the vulnerability is patched?",
      answer: "Yes, Capture The Bug offers retesting services to verify whether identified vulnerabilities have been successfully patched. This ensures that your security measures are effective and up-to-date."
    },
    {
      id: "technology-compatibility",
      question: "Does VAPT work only on a certain technology?",
      answer: "VAPT can be applied to a wide range of technologies, including web applications, mobile apps, cloud environments, IoT devices, and APIs. Our approach is tailored to your specific technology stack to ensure comprehensive coverage."
    },
    {
      id: "developer-collaboration",
      question: "Do you work with our developers in patching the vulnerabilities?",
      answer: "Yes, Capture The Bug provides post-assessment support, including consultation and collaboration with your development team to help implement effective patches and security measures."
    },
    {
      id: "choosing-vapt-company",
      question: "How should I choose a VAPT company?",
      answer: "When selecting a VAPT provider, consider factors such as their expertise, reputation, the comprehensiveness of their testing methodologies, client testimonials, and post-assessment support. Capture The Bug excels in all these areas, offering tailored solutions to meet your specific security needs."
    }
  ];

  return (
    <section className="w-full py-16 sm:py-20 md:py-24 bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#06258d]  mb-4">
            Frequently Asked Questions
          </h2>
          <p className="text-lg sm:text-xl text-[#06258d] max-w-3xl mx-auto">
            Get answers to common questions about our penetration testing services and security assessments.
          </p>
        </div>

        {/* FAQ List - single column vertical accordion */}
        <div className="flex flex-col gap-4">
          {faqs.map((faq) => (
            <FAQItem
              key={faq.id}
              uniqueId={faq.id}
              question={faq.question}
              answer={faq.answer}
              isOpen={openItem === faq.id}
              onToggle={() => handleToggle(faq.id)}
            />
          ))}
        </div>

        {/* Contact CTA */}
        <div className="text-center mt-12 sm:mt-16">
          <div className="inline-flex items-center gap-3 bg-white border-2 border-[#027bfd] text-gray-700 px-6 py-3 rounded-full shadow-lg">
            <div className="w-3 h-3 bg-gradient-to-r from-[#027bfd] to-[#032391] rounded-full"></div>
            <span className="font-medium">
              Still have questions? <a href="/Company/Contact-Us" className="text-[#027bfd] hover:text-[#032391] font-semibold">Contact our team</a>
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
