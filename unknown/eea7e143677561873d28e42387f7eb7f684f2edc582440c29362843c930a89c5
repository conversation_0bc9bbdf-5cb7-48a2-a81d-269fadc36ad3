/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
        "btn-gradient": "linear-gradient(90deg, #0835A7 0%, #062575 50%, #010D2C 100%)",
        "btn-gradient-hover": "linear-gradient(90deg, #0B45DB 0%, #0835A7 50%, #062575 100%)",
      },
      colors: {
        // Capture the Bug brand colours
        "ctb-blue-0": "#D4E2FF",
        "ctb-blue-50": "#58CC02",
        "ctb-blue-150": "#010D2C",
        "ctb-blue-250": "#010D2C",
        "ctb-blue-350": "#011B70",
        "tertiary-blue": "#010D2C",
        "secondary-blue": "#062575", //used for buttons
        "primary-blue": "#0835A7",
        "ctb-green-50": "#58CC02",
        "ctb-bg-light": "#fbf7ff", // Light purple background for hero section
        "ctb-light-blue": "#027bfb",
      },
    },
  },
  plugins: [],
};
