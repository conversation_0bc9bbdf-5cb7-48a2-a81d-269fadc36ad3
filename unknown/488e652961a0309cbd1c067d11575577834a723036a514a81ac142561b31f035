import React from "react";
import Image from 'next/image';

export default function SecurityAuditBanner() {
  return (
    <div className="bg-green-100 rounded-lg shadow-md p-6 flex flex-col md:flex-row items-center justify-between mx-auto max-w-4xl mt-6">
      {/* Text Content */}
      <div className="text-left">
        <h2 className="text-xl md:text-2xl font-semibold text-gray-900">
          It is one small security loophole v/s{" "}
          <span className="text-blue-600">
           your entire application.
          </span>
        </h2>
        <p className="text-gray-700 mt-2">
          Get your web app audited with &quot;Capture The Bug&quot;.
        </p>
        {/* Buttons */}
        <div className="mt-4 flex gap-4">
          <a
            href="https://capturethebug.xyz/"
            target="_blank"
            rel="noopener noreferrer"
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition text-center"
          >
            Explore Features
          </a>
          <a
            href="https://capturethebug.xyz/Request-Demo"
            target="_blank"
            rel="noopener noreferrer"
            className="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 transition text-center"
          >
            Request a Demo
          </a>
        </div>
      </div>

      {/* Illustration/Image */}
      <div className="mt-4 md:mt-0">
        <Image
          src="/images/Home_Dashboard.png"
          alt="Security Audit"
          className="w-52 md:w-64"
          width={256}
          height={160}
        />
      </div>
    </div>
  );
}
