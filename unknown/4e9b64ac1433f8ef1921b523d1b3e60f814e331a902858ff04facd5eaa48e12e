"use client";
import React from 'react';
import Link from 'next/link';
import { ChevronRight, Home, MapPin } from 'lucide-react';
import { motion } from 'framer-motion';
import { getLocationData } from '../data/locationData';

/**
 * Breadcrumb Navigation Component with SEO structured data
 * Provides clear navigation hierarchy and improves SEO
 */
const BreadcrumbNavigation = ({ country }) => {
  const locationData = getLocationData(country);
  
  const breadcrumbItems = [
    {
      name: "Home",
      url: "/",
      icon: <Home className="w-4 h-4" />,
      description: "Return to homepage"
    },
    {
      name: "Locations",
      url: "/Locations",
      icon: <MapPin className="w-4 h-4" />,
      description: "View all locations"
    },
    {
      name: locationData.country,
      url: `/Locations/${country}`,
      current: true,
      description: `${locationData.country} cybersecurity services`
    }
  ];

  // Generate structured data for breadcrumbs
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbItems.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.current ? undefined : `https://capturethebug.xyz${item.url}`
    }))
  };

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      {/* Enhanced Breadcrumb Navigation */}
      <nav
        className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 py-6"
        aria-label="Breadcrumb navigation"
      >
        <div className="max-w-7xl mx-auto px-4">
          <motion.ol
            className="flex items-center space-x-1 text-sm"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {breadcrumbItems.map((item, index) => (
              <motion.li
                key={index}
                className="flex items-center"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                {index > 0 && (
                  <ChevronRight
                    className="w-4 h-4 text-gray-400 mx-3"
                    aria-hidden="true"
                  />
                )}

                {item.current ? (
                  <span
                    className="text-gray-900 font-semibold flex items-center gap-2 px-3 py-2 bg-[#0835A7]/5 rounded-lg"
                    aria-current="page"
                    title={item.description}
                  >
                    {item.icon && (
                      <span className="text-[#0835A7]">{item.icon}</span>
                    )}
                    {item.name}
                  </span>
                ) : (
                  <Link
                    href={item.url}
                    className="text-gray-600 hover:text-[#0835A7] transition-all duration-300 flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-[#0835A7]/5 group"
                    title={item.description}
                  >
                    {item.icon && (
                      <span className="text-gray-500 group-hover:text-[#0835A7] transition-colors duration-300">
                        {item.icon}
                      </span>
                    )}
                    <span className="font-medium">{item.name}</span>
                  </Link>
                )}
              </motion.li>
            ))}
          </motion.ol>

          {/* Optional: Current page description */}
          <motion.div
            className="mt-2 text-xs text-gray-500"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            {breadcrumbItems.find(item => item.current)?.description}
          </motion.div>
        </div>
      </nav>
    </>
  );
};

export default BreadcrumbNavigation;
