"use client";
import React from "react";
import { Boxes } from "./ui/background-boxes";
import { cn } from "@/app/utils/cn";

export function BackgroundBoxesDemo() {
  return (
    <div
      className="min-h-screen w-full relative overflow-hidden bg-slate-900 flex flex-col items-center justify-center rounded-none">
      <div
        className="absolute inset-0 w-full h-full bg-slate-900 z-20 [mask-image:radial-gradient(transparent,white)] pointer-events-none" />
      <Boxes />
      <p className="text-xl md:text-2xl font-semibold mb-4 text-center relative z-20 text-neutral-300 ">About us</p>
      <h1 className="text-4xl md:text-7xl font-bold text-white mb-4 text-center relative z-20">
        On a mission to
      </h1>
      <h2 className="text-3xl md:text-6xl font-semibold text-[#1e83fb] text-center relative z-20">
        secure the internet and protect <br /> consumer data
      </h2>
      
    </div>
  );
}
