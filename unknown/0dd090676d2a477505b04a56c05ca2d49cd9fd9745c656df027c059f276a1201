"use client";
import Image from "next/image";
import React, { useState } from "react";

const Introduction = () => {
  const [openDropdown, setOpenDropdown] = useState("Cloud-Based Pentesting");

  const benefits = [
    {
      title: "Cloud-Based Pentesting",
      content: "Capture The Bug's cloud-native PTaaS platform gives your team the ability to launch and manage manual pentests-without the delays of traditional consulting models. Whether you're running one test or ten, you can spin them up instantly, collaborate with pentesters in real time, and get continuous visibility into your findings. ",
      image: "/images/bene1.png"
    },
    {
      title: "Integrations",
      content: "Capture The Bug integrates seamlessly with the tools your security and engineering teams already use-like Jira, Slack, GitHub, and CI/CD pipelines. Sync vulnerability data, assign tasks automatically, and keep everyone in the loop without switching context. ",
      image: "/images/bene2.png"
    },
    {
      title: "Flexible Pricing",
      content: " Whether you're running one pentest or ten, our flat, subscription-based model gives you unlimited access to the platform-no surprise fees, no per-test costs. ",
      image: "/images/bene3.png"
    },
    {
      title: "Scalable and Agile Pentesting",
      content: "From product teams to platform security, Capture The Bug scales with your organization. Launch tests on demand, track findings across business units, and maintain full visibility-all from one centralized dashboard. ",
      image: "/images/bene4.png"
    },
   {
  title: "Real-Time Bug Reports",
  content: (
    <>
      <span className="text-gray-900 mb-5 font-semibold">See findings as they&apos;re discovered-not weeks later</span>
      <br />
Stop waiting for final PDFs. Our live findings feed delivers real-time insights as vulnerabilities are uncovered, so your team can start fixing sooner and reduce exposure instantly.     </>
  ),
  image: "/images/bene5.png"
}
  ];

  const toggleDropdown = (title) => {
    setOpenDropdown(openDropdown === title ? "" : title);
  };

  // Find the image for the currently open dropdown, or default to the first
  const currentBenefit = benefits.find(b => b.title === openDropdown) || benefits[0];

  return (
    <div className="py-20 md:py-24 lg:py-32 px-4 sm:px-6 md:px-8 lg:px-12 xl:px-28 bg-ctb-bg-light">
      <div className="max-w-8xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16 lg:mb-20">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-[600] mb-6 text-tertiary-blue">
            Why Teams Choose <span className="text-primary-blue"> Our PTaaS Platform</span>
          </h2>
          <p className="text-tertiary-blue text-lg md:text-xl max-w-3xl mx-auto">
 Built for speed, scale, and seamless security testing.          </p>
        </div>

        <div className="flex flex-col lg:flex-row items-stretch justify-between gap-12 lg:gap-16">
          {/* Image Section */}
          <div className="lg:w-[45%] flex justify-center lg:justify-start">
            <div className="relative w-full h-[400px]">
              <Image
                src={currentBenefit.image}
                fill={true}
                alt="PTaaS Dashboard Interface"
                className={`object-cover transition-transform duration-700 hover:scale-105 ${
                  currentBenefit.image === "/images/bene1.png" ? " " : "border-[7px] border-[#D4D4D5] rounded-xl"
                }`}
                style={{ objectPosition: "center" }}
                priority
              />
            </div>
          </div>

          {/* Dropdowns Section */}
          <div className="lg:w-[50%] space-y-3">
            {benefits.map((benefit, index) => (
              <div key={index} className="mb-2">
                <div className={`
                  rounded-lg transition-all duration-300 border
                  ${openDropdown === benefit.title 
                    ? 'bg-white shadow-lg border-primary-blue' 
                    : 'bg-white/90 hover:bg-white border-gray-200 hover:border-primary-blue/50'}
                `}>
                  <button
                    onClick={() => toggleDropdown(benefit.title)}
                    className="w-full px-6 py-5 text-left flex justify-between items-center transition-colors duration-300"
                  >
                    <span className={`text-lg font-medium transition-all duration-300 ${
                      openDropdown === benefit.title 
                        ? 'text-primary-blue' 
                        : 'text-tertiary-blue hover:text-primary-blue'
                    }`}>
                      {benefit.title}
                    </span>
                    <div className={`
                      w-8 h-8 rounded-full flex items-center justify-center 
                      ${openDropdown === benefit.title 
                        ? 'bg-primary-blue text-white' 
                        : 'bg-gray-100 text-gray-500'}
                      transition-all duration-300
                    `}>
                    <svg
                        className={`w-4 h-4 transition-transform duration-300 ${
                        openDropdown === benefit.title ? "rotate-180" : ""
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                    </div>
                  </button>
                  {openDropdown === benefit.title && (
                    <div className="px-6 pb-6 pt-2">
                      <p className="text-tertiary-blue/80 text-base leading-relaxed pl-0">
                        {benefit.content}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Introduction;