"use client"
import Link from "next/link";
import ThreatIntelligenceRing from "./ThreatIntelligenceRing";
import VulnerabilityTrendsChart from "./VulnerabilityTrendsChart";
import ScannerAnimation from "./ScannerAnimation";
import WiredSkeletonLoader from "./WiredSkeletonLoader";

const features = [
  {
    title: "Continuous Pentesting",
    desc: "Move beyond once-a-year testing. Identify and fix vulnerabilities continuously across web apps, APIs, and infrastructure - without slowing development.",
    link: { href: "/Product/Penetration-Testing", label: "Explore continuous testing", icon: "→" },
    component: <ThreatIntelligenceRing />
  },
  {
    title: "Compliance-Ready Reports",
    desc: "Generate clean, actionable pentest reports mapped to SOC 2, ISO 27001, GDPR, CIS, HIPAA, and more. Perfect for auditors, investors, and customers.",
    link: { href: "/Download-Sample-Report", label:"Check our sample reports", icon: "→" },
    component: <VulnerabilityTrendsChart />
  },
  {
    title: "Verified by Humans ",
    desc: "All findings are manually validated by top-tier pentesters. That means no false positives-just real, actionable vulnerabilities. ",
    link: { href: "/How-it-works", label: "See how we do our testing", icon: "→" },
    component: <ScannerAnimation />
  },
  {
    title: "Developer-Centric Remediation",
    desc: "With clear reproduction steps, risk context, and GitHub/Jira-ready tickets, your developers will love our pentest reports.",
    link: { href: "/Remediation", label: "See how remediation works", icon: "→" },
    component: <WiredSkeletonLoader />
  }
];

export default function Features() {
  return (
    <section className="w-full py-12 sm:py-16 md:py-20 bg-white">
      <div className="max-w-screen-2xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
        <h2 className="text-2xl sm:text-3xl md:text-4xl font-[400] text-center mb-8 sm:mb-12">
          Your continuous, compliance-ready <span className="text-primary-blue font-[500]">pentesting solution</span>
        </h2>
        <div className="md:px-4 lg:px-8 xl:px-16">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-5 lg:gap-6 w-full items-stretch">
            {features.map((f, i) => (
              <div
                key={i}
                className="flex flex-col justify-between bg-white rounded-xl border border-gray-100 shadow-sm p-6 sm:p-7 min-h-[380px] sm:min-h-[400px] w-full hover:shadow-md transition-all duration-300"
              >
                <div className="flex-1 flex flex-col">
                  <h3 className="text-lg sm:text-xl font-[500] mb-2 sm:mb-3">{f.title}</h3>
                  <p className="text-gray-700 text-sm sm:text-base mb-4 sm:mb-6 font-[400]">{f.desc}</p>
                  <div className="mt-auto pt-2">
                    <Link href={f.link.href} className="text-primary-blue text-sm sm:text-base font-medium flex items-center gap-1 hover:text-secondary-blue transition-colors group relative overflow-hidden">
                      <span className="relative z-10 flex items-center">
                        {f.link.label}
                        <span className="ml-1 transform group-hover:translate-x-1 transition-transform duration-300 relative z-10">{f.link.icon}</span>
                      </span>
                      <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-secondary-blue transform origin-left transition-all duration-300 ease-out group-hover:w-full"></span>
                    </Link>
                  </div>
                </div>
                <div className="mt-6 h-32 sm:h-36 md:h-40">{f.component}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
