"use client";
import React from "react";
import Image from 'next/image';

export default function VendorSecurityReview() {
  const images = [
    "/images/webapp1.svg",
    "/images/webapp2.png",
    "/images/webapp3.svg",
    "/images/Integration.svg",
    "/images/Remediation.svg"
  ];

  const sections = [
    {
         id: "real-time-insights",
      title: "Real-Time Vulnerability Insights",
      description: "Stay ahead of threats with live reporting and dashboards.",
      features: [
        {
          title: "Live findings feed",
          description: "Watch vulnerabilities appear in real-time-no more waiting for static PDF reports. Prioritize fixes the moment they're discovered."
        },
        {
          title: "Instant risk visibility",
          description: "Track issue severity, affected assets, and fix progress across teams-all from one centralized view."
        }
      ]
    },
    {
      id: "ptaas-methodology",
      title: "PTaaS Methodology",
      description: "Unlike traditional pentesting our ongoing pentesting approach ensures:",
      features: [
        {
          title: "Real-Time Bug Reports",
          description: "You receive immediate alerts when vulns are discovered, allowing for rapid action."
        },
        {
          title: "Adaptability to Changes",
          description: "New feature added? We test it immediately to ensure it's secure, no waiting for the next annual pentest."
        }
      ]
    },
    {
          id: "trusted-slas",
      title: "SLAs You Can Trust",
      description: "Move fast with guaranteed timelines and transparent delivery.",
      features: [
        {
          title: "Kick off in days, not weeks",
          description: "Start your pentest fast with committed kickoff windows and predefined delivery dates."
        },
        {
          title: "On-time, every time",
          description: "Our SLA-backed delivery ensures your roadmap and compliance timelines stay on track-no bottlenecks."
        }
      ]
    },
    {
       id: "workflow-integration",
      title: "Integrates Into Your Workflow",
      description: "Connect continuous security testing with how your teams already work.",
      features: [
        {
          title: "Dev-friendly tooling",
          description: "Push findings directly into Jira, Slack, or GitHub-no extra logins or friction. Developers stay in flow."
        },
        {
          title: "Built for DevSecOps",
          description: "Bridge the gap between security and engineering with alerts, tickets, and fixes all in sync."
        }
      ]
    },
    {
       id: "guided-remediation",
      title: "Guided Remediation & Support",
      description: "We don't just report problems-we help you solve them.",
      features: [
        {
          title: "Fix it with confidence",
          description: "Every finding comes with clear remediation steps, CVSS ratings, and one-click guidance from your assigned pentester."
        },
        {
          title: "On-demand security support",
          description: "Got a question? Chat directly with the pentester to validate fixes or dig deeper-no middle layers."
        }
      ]
    }
  ];

  const handleDemoRequest = () => {
    window.location.href = '/Request-Demo';
  };

const renderContent = (section, index, isDark) => (
    <div className="space-y-6">
      <h1 className={`text-xl sm:text-2xl md:text-4xl font-bold ${isDark ? 'text-white' : 'text-blue-800'} leading-tight`}>
        {section.title}
      </h1>
      
      <p className={`text-sm sm:text-base md:text-lg ${isDark ? 'text-blue-100' : 'text-blue-700'} leading-relaxed`}>
        {section.description}
      </p>

      <div className="space-y-6">
        {section.features.map((feature, featureIndex) => (
          <div key={featureIndex} className={`border-l-4 ${isDark ? 'border-blue-300' : 'border-blue-200'} pl-6`}>
            <h3 className={`font-semibold text-lg ${isDark ? 'text-white' : 'text-blue-800'} mb-2`}>
              {feature.title}
            </h3>
            <p className={`${isDark ? 'text-blue-100' : 'text-blue-700'} leading-relaxed`}>
              {feature.description}
            </p>
          </div>
        ))}
      </div>

      <button 
        onClick={handleDemoRequest}
        className={`${isDark ? 'bg-white border-white text-blue-900 hover:bg-blue-800 hover:text-white' : 'bg-white border-blue-900 text-blue-900 hover:bg-blue-900 hover:text-white'} border px-8 py-4 rounded-lg font-medium transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-transform duration-200 cursor-pointer`}
      >
        Request a demo →
      </button>
    </div>
  );

  const renderImage = (index) => {
    const imageIndex = index % images.length;
    const isWebapp2 = images[imageIndex] === "/images/webapp2.png";
    const isWebapp3 = images[imageIndex] === "/images/webapp3.svg";
    
    return (
      <div className="relative">
        <div className=" rounded-2xl  relative overflow-hidden ">
          <Image 
            src={images[imageIndex]} 
            alt={`Vendor Security Dashboard ${imageIndex + 1}`} 
            className={`${isWebapp2 ? 'w-3/4 mx-auto border-8 border-[#969796]' : isWebapp3 ? 'mx-auto border-8 border-[#969796]' : 'bg-gradient-to-br from-gray-50 to-gray-100 w-full border border-[#969796]'} h-auto object-contain rounded-xl shadow-2xl`}
            style={{
              filter: 'drop-shadow(0 25px 50px rgba(0, 0, 0, 0.15))',
              width: isWebapp3 ? '85%' : undefined
            }}
            width={600}
            height={400}
          /> 
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen w-full">
      {sections.map((section, index) => {
        const isDark = index % 2 === 1;  
        
        return (
          <div 
            key={index}
            id={section.id}
            className={`py-16 md:py-20 ${isDark ? 'bg-blue-900' : 'bg-white'} w-full`}
          >
            <div className="max-w-7xl mx-auto px-4 md:px-12 ">
              <div className={`grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-20 items-center`}>
                {index % 2 === 0 ? (
                  <>
                    <div className="order-2 -ml-5 lg:order-1">
                      {renderImage(index)}
                    </div>
                    <div className="order-1 lg:order-2">
                      {renderContent(section, index, isDark)}
                    </div>
                  </>
                ) : (
                  <>
                    <div className="order-2 lg:order-1">
                      {renderContent(section, index, isDark)}
                    </div>
                    <div className="order-1 lg:order-2">
                      {renderImage(index)}
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}