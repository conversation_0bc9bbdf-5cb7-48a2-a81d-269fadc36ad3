import React from "react";
import PartnersList from "@/app/Product/Penetration-Testing/components/PartnersList";
import BlogSection from "@/app/Home/components/Blogs";
import Landing from "./components/Landing";
import Security from "./components/Security";
import Testimonial from "../Testimonial";

export const metadata = {
  title: "Capture The Bug | Ecommerce & Retail Security  ",
  description:
    "Secure your digital commerce platforms with Capture The Bug. We protect online storefronts, APIs, and customer data with continuous security testing and fraud prevention.",
  keywords:
    "ecommerce penetration testing, retail cybersecurity, online store security, API vulnerability testing, fraud detection, PCI compliance, digital commerce security, Capture The Bug",
  robots: "index, follow",
  icons: {
    icon: "/favicon.ico",
  },
  openGraph: {
    title: "Capture The Bug | Ecommerce & Retail Security  ",
    type: "website",
    url: "https://capturethebug.xyz/Industries/Ecommerce&Retail",
    description:
      "Protect your ecommerce stack from cyber threats with Capture The Bug-covering storefronts, APIs, third-party plugins, and customer data security audits.",
    images: "https://ibb.co/SXj33dNF", 
  },
  twitter: {
    card: "summary_large_image",
    title: "Capture The Bug | Ecommerce & Retail Security  ",
    description:
      "Secure online stores and customer data with Capture The Bug. We deliver offensive testing and fraud detection for digital commerce platforms.",
    images: "https://ibb.co/SXj33dNF",
  },
};

const testimonial = {
  company: "EROAD",
  logo: "/images/partly_logo.png",
  quote: "Capture The Bug has efficiently and affordably helped us meet our cybersecurity goals. Their tailored solutions and proactive approach have fortified our defenses, providing peace of mind. The real-time bug reports and their dedicated assistance ensure we are vigilant against cyber threats.",
  author: "Nathan Taylor",
  position: "Director of Engineering - Security "
};


export default function Ecommerce() {
  return (
    <>
    <Landing/>
   <Security/>
   <PartnersList/>
   <Testimonial
   company={testimonial.company}
   logo={testimonial.logo}
   quote={testimonial.quote}
   author={testimonial.author}
   position={testimonial.position}
   logoSize={{ width: 140, height: 100 }}
   logoStyle={{ marginLeft: 0 }}
      />     
    <BlogSection/> 
</>
  );
}