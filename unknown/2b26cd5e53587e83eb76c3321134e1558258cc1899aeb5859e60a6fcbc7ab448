import React from "react";
import { Shield, Users, Target } from "lucide-react";
 
export default function Security() {
  return (
    <> 
  <div className="bg-white py-16 sm:py-24">
  <div className="container mx-auto px-4 md:px-24">
    <div className="mb-16">
      <div className="text-secondary-blue text-xl font-bold mb-5">
        How Capture The Bug Helps
      </div>
      <h2 className="text-4xl sm:text-5xl font-bold text-[#010D2C] leading-tight mb-5">
Secure learning environments without disrupting education
      </h2>
      <p className="text-[#6B7280] text-lg font-medium leading-relaxed  ">
From universities to K-12 portals and EdTech startups, we help educational platforms prevent breaches, data leaks, and service disruptions while staying compliant with global data regulations.
      </p>
    </div>

     <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
       <div className="bg-white border-l-4 border-[#58CC02] pl-6 pr-4 py-6">
        <div className="">
          <div className="w-10 h-10 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4">
            <Shield className="w-5 h-5 text-white" />
          </div>
          <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
            LMS & Platform Security
          </h3>
          <p className="text-[#6B7280] text-lg leading-relaxed">
            We secure your <strong>digital classroom stack</strong>-<strong>learning management systems</strong>, <strong>video portals</strong>, and <strong>EdTech tools</strong>-by testing for vulnerabilities that could lead to <strong>data exposure</strong> or <strong>platform downtime</strong>. Whether you&apos;re scaling a <strong>campus network</strong> or managing thousands of <strong>online learners</strong>, we keep learning systems <strong>secure</strong> and <strong>uninterrupted</strong>.
</p></div>
        </div>
       

       <div className="bg-white border-l-4 border-[#58CC02] pl-6 pr-4 py-6">
        <div className="">
          <div className="w-10 h-10 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4">
            <Users className="w-5 h-5 text-white" />
          </div>
          <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
Student Data Privacy & Compliance
          </h3>
          <p className="text-[#6B7280] text-lg leading-relaxed">
            Our assessments help you protect <strong>academic records</strong>, <strong>student identities</strong>, and <strong>usage data</strong> while meeting education-sector privacy benchmarks like <strong>ST4S</strong>, <strong>FERPA</strong>, and <strong>GDPR</strong>. We help you avoid costly <strong>compliance gaps</strong> and build <strong>trust</strong> with schools, students, and parents through <strong>continuous validation</strong>.
</p>
        </div>
      </div>

       <div className="bg-white border-l-4 border-[#58CC02] pl-6 pr-4 py-6">
        <div className=" ">
          <div className="w-10 h-10 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4">
            <Target className="w-5 h-5 text-white" />
          </div>
          <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
            Ransomware & Phishing Simulation
          </h3>
          <p className="text-[#6B7280] text-lg leading-relaxed">
            Schools and EdTech providers are frequent targets of <strong>phishing</strong> and <strong>ransomware attacks</strong>. We simulate <strong>real-world scenarios</strong> across <strong>email systems</strong>, <strong>admin interfaces</strong>, and <strong>third-party integrations</strong>-so you can close gaps before attackers exploit them and keep <strong>learning on track</strong>.
</p></div>
        </div>
      </div>
    </div>
    </div>
   
</>

  );
}