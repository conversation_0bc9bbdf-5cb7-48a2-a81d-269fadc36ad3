@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS custom property for announcement banner height */
:root {
  --announcement-banner-height: 0px;
}

/* Ensure no gap between banner and header */
.announcement-banner-container {
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  box-shadow: none !important;
  margin-bottom: 0 !important;
}

.header-no-gap {
  margin: 0 !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  border: none !important;
  border-top: none !important;
  border-bottom: none !important;
  padding-top: 0 !important;
}



@keyframes moveUpDown {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-100%);
  }
}

.animate-moveUpDown {
  animation: moveUpDown 10s ease-in-out infinite;
}
@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out forwards;
}

@keyframes slideDown {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}

.animate-slide-down {
  animation: slideDown 0.5s ease-out forwards;
}

/* Premium shimmer animation for skeleton loaders */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 1.5s infinite;
}

/* Hero section fade-in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

/* Pulse glow effect for premium elements */
@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(8, 53, 167, 0.2);
  }
  50% {
    box-shadow: 0 0 20px rgba(8, 53, 167, 0.4), 0 0 30px rgba(8, 53, 167, 0.1);
  }
}

.animate-pulse-glow {
  animation: pulseGlow 3s infinite;
}