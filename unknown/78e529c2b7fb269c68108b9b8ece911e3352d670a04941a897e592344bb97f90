"use client";
import React from 'react';
import DynamicCalculator from './DynamicCalculator';

export default function CalculatorSection() {
  return (
    <div className="md:mt-10 mt-10 py-8 md:py-12 lg:py-28 px-4 sm:px-6 md:px-8 lg:px-12 xl:px-28 bg-[#fbf7ff]">
      <div className="max-w-5xl mx-auto">
        {/* Header Section */}
        <div className="text-center mb-12 lg:mb-8">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-secondary-blue mb-4">
            Compare Plans and Calculate Your Savings
          </h2>
          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
            Find the right plan for you by using our price calculator to see how much you could save compared to your current costs.
          </p>
        </div>

        {/* Dynamic Calculator Container */}
        <div className="bg-white rounded-2xl shadow-xl p-6 sm:p-8 md:p-10 border-t-4 border-blue-500">
          <DynamicCalculator />
        </div>
      </div>
    </div>
  );
}
