import React from 'react';
import Image from 'next/image';
import Button from '@/app/common/buttons/Button';

const PartnerSection = () => {
  return (
    <div className="bg-gray-50 py-8 sm:py-12 md:py-16 lg:py-28 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-10 md:gap-12 lg:gap-16 items-center">
          {/* Left Content */}
          <div className="space-y-4 sm:space-y-5 md:space-y-6">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 leading-tight">
              Partner With <br />
              <span className='text-secondary-blue'>
                Capture The Bug
              </span>
            </h1>
            <p className="text-sm sm:text-base md:text-lg text-gray-600 leading-relaxed">
Security is no longer optional - it&apos;s a business need. Capture The Bug delivers trusted <a href="/Services/Web-app" className="text-secondary-blue hover:underline font-semibold">penetration testing</a> for SaaS, APIs, cloud, and networks. Explore our <a href="/How-it-works" className="text-secondary-blue hover:underline font-semibold">proven methodology</a> and see why enterprise teams choose us.
            </p>
            <p className="text-sm sm:text-base md:text-lg text-gray-600">
We&apos;re now inviting partners to help us expand. If you work with teams that need better security, let&apos;s team up. Learn more <a href="/Company/About-Us" className="text-secondary-blue hover:underline font-semibold">about our company</a> and mission.
            </p>
            {/* Quote/Highlight Block */}
            <div className="border-l-4 border-green-500 pl-4 sm:pl-5 md:pl-6 py-3 sm:py-4 bg-white rounded-r-lg shadow-sm">
              <p className="text-sm sm:text-base text-gray-700 leading-relaxed">
You bring the connections. We bring the PTaaS platform, expert testing, and clear results. See our proven track record in our <a href="/Case-Studies" className="text-secondary-blue hover:underline font-semibold">client case studies</a>.
 
              </p>
            </div>
            {/* CTA Button */}
            <div className="pt-2 sm:pt-3 md:pt-4">
              <Button
                href="/Request-Demo"
                variant="primary"
                size="sm"
                className="bg-lime-500  sm:w-1/3  hover:bg-lime-400 text-slate-900 font-semibold px-6 sm:px-8 py-3 sm:py-4 rounded-lg transition-all duration-300 flex items-center gap-3 group shadow-lg hover:shadow-xl transform hover:scale-105 text-sm sm:text-base"
              >                
              Become a Partner
              </Button>
            </div>
          </div>
          {/* Right Image */}
          <div className="flex justify-center lg:justify-end   ">
            <div className="relative w-full">
              <Image
              src="/images/pentestdash.svg"
                alt="Team collaboration illustration"
                width={600}
                height={400}
                className="w-full h-auto rounded-lg"
                priority
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PartnerSection;