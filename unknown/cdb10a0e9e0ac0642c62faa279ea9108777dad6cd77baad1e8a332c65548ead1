import React from "react";
import Image from 'next/image';

const Card = ({ imageSrc, title, description }) => {
  return (
    <div className="flex flex-col text-left w-full max-w-xs">
      <div className="mb-4">
        <Image src={imageSrc} alt={title} className="w-12 h-12" width={48} height={48} />
      </div>
      <h3 className="text-xl font-bold bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent mb-2">{title}</h3>
      <p className="text-slate-600">{description}</p>
    </div>
  );
};

export default function Solutions() {
  const cards = [
    {
      imageSrc: "/images/web-icon-1.png",
      title: "Identify and Resolve Vulnerabilities ",
      description: "Uncover and fix critical vulnerabilities through our comprehensive cloud-based manual testing."
    },
    {
      imageSrc: "/images/web-icon-2.png",
      title: "Confidently Secure Applications",
      description: "Thoroughly test complex features like payment gateways and user workflows via our cloud platform."
    },
    {
      imageSrc: "/images/web-icon-3.png",
      title: "Trust in Proven, In-Depth Testing Standards",
      description: "Adhere to industry-leading standards like OWASP for reliable, comprehensive security testing."
    },
    {
      imageSrc: "/images/web-icon-4.png",
      title: "Leverage Expert Pentesters",
      description: "Access skilled pentesters and advanced tools through our flexible, scalable cloud platform."
    }
  ];

  return (
    <div className="container mx-auto px-4 py-12 md:py-32">
      <div className="flex flex-col gap-8 items-center">
        <h1 className="md:text-4xl text-2xl font-bold text-center bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent max-w-4xl">
          Discover Capture The Bug&apos;s Web PTaaS Solutions        
        </h1>
        <p className="text-center text-slate-600 max-w-4xl">
          Enhance your security posture and meet compliance requirements effortlessly with Capture The Bug&apos;s Web Penetration Testing as a Service (PTaaS). As a leader in vulnerability management, we streamline your security processes, allowing you to focus on what matters-remediation and growth.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-8">
          {cards.map((card, index) => (
            <Card key={index} {...card} />
          ))}
        </div>
      </div>
    </div>
  );
}