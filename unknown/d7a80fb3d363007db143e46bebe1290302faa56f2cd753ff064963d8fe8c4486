import Network from "./Network";
 
export const metadata = {
  title: "Capture The Bug | Network Penetration Testing Services",
  description:
    "Identify vulnerabilities in your internal and external networks with Capture The Bug’s expert-led network penetration testing. Ensure security across your infrastructure and meet compliance standards.",
  keywords: "network penetration testing, infrastructure security testing, internal and external network assessment, firewall misconfiguration testing, PTaaS for networks, lateral movement detection, vulnerability assessment, Capture The Bug",
  robots: "index, follow",
  icons: {
    icon: "/favicon.ico",
  },
  openGraph: {
    title: "Capture The Bug | Network Penetration Testing Services",
    type: "website",
    url: "https://capturethebug.xyz/Services/Network-pentest",
    description:
      "Capture The Bug provides deep-dive network penetration testing to identify real-world threats in your infrastructure. Eliminate vulnerabilities, improve defense, and pass audits with confidence.",
    images: "https://i.ibb.co/1fNP3KzL/Screenshot-2025-06-18-201431.png",
  },
  twitter: {
    card: "summary_large_image",
    title: "Capture The Bug | Network Penetration Testing Services",
    description:
      "Protect your networks with Capture The Bug’s expert network pentesting. Identify misconfigurations, firewall gaps, and infrastructure risks-on-demand.",
    images: "https://i.ibb.co/1fNP3KzL/Screenshot-2025-06-18-201431.png",
  },
};

export default function Page() {
  return   <Network />
}
