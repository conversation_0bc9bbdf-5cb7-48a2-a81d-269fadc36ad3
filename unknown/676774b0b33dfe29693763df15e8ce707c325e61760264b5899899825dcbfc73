import React from "react";
import Image from 'next/image';

const Card = ({ imageSrc, title, description }) => {
  return (
    <div className="flex flex-col text-left max-w-sm">
      <div className="mb-4">
        <Image src={imageSrc} alt={title} className="w-12 h-12" width={48} height={48} />
      </div>
      <h3 className="text-xl font-bold bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent mb-2">{title}</h3>
      <p className="text-slate-600">{description}</p>
    </div>
  );
};

export default function Solutions() {
  const cards = [
    {
      imageSrc: "/images/network-icon-1.png",
      title: "Address Vulnerabilities",
      description: "Identify and mitigate weaknesses in both internal systems and externally accessible assets before they can be exploited."
    },
    {
      imageSrc: "/images/network-icon-2.png",
      title: "Achieve Compliance",
      description: "Meet industry regulations and standards with regular, thorough assessments. including all the documentation you need, including attestation letters and detailed summaries."
    },
    {
      imageSrc: "/images/network-icon-3.png",
      title: "Fortify Your Defenses",
      description: "Reduce the risk of security breaches, downtime, and financial loss with our comprehensive approach that goes beyond automation, offering thorough testing, validation, and remediation."
    }
  ];

  return (
    <div className="container mx-auto px-4 py-12 md:py-32">
      <div className="flex flex-col gap-8 items-center">
        <h1 className="md:text-4xl text-2xl font-bold text-center bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent max-w-4xl">
        Enhance Security Across Your Infrastructure
        </h1>
        <p className="text-center text-slate-600 max-w-4xl">
        Elevate your security posture and ensure compliance with Capture The Bug&apos;s PTaaS solutions. Our platform partners with you to identify vulnerabilities and streamline remediation, allowing you to focus on protecting your business with confidence.
        </p>
        <div className="flex flex-wrap justify-center gap-8 mt-8">
          {cards.map((card, index) => (
            <Card key={index} {...card} />
          ))}
        </div>
      </div>
    </div>
  );
}