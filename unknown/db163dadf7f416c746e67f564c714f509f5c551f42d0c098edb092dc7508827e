import React from "react";
import PartnersList from "@/app/Product/Penetration-Testing/components/PartnersList";
import BlogSection from "@/app/Home/components/Blogs";
import Landing from "./components/Landing";
import Security from "./components/Security";
import Testimonial from "../Testimonial";

const testimonial = {
  company: "Rafay ",
  logo: "/images/rafay_logo.png",
  quote: "As a leading Kubernetes company, we understand the importance of securing our data and systems. We engage Capture The Bug's pentesting as a service platform for black box penetration testing. Their ethical hackers provided a thorough security assessment, with clear and concise reporting that included actionable recommendations. We highly recommend their platform for any organization looking to conduct comprehensive penetration testing.",
  author: "<PERSON>",
  position: "Sr. Director of Engineering  "
};

export const metadata = {
  title: "Capture The Bug | SaaS & Cloud Platform Security",
  description:
    "Protect your cloud-based SaaS products from misconfigurations, breaches, and insider threats. Capture The Bug delivers continuous offensive security from code to cloud.",
  keywords:
    "cloud penetration testing, SaaS security testing, cloud misconfiguration detection, insider threat prevention, DevOps security, cloud infrastructure hardening, SaaS vulnerability assessment, Capture The Bug",
  robots: "index, follow",
  icons: {
    icon: "/favicon.ico",
  },
  openGraph: {
    title: "Capture The Bug | SaaS & Cloud Platform Security",
    type: "website",
    url: "https://capturethebug.xyz/Industries/SaaS&CloudPlatforms",
    description:
      "Capture The Bug helps modern SaaS teams ship securely by identifying cloud vulnerabilities, misconfigurations, and insider risks through offensive security assessments.",
    images: "https://ibb.co/Gv1xpYL8",  
  },
  twitter: {
    card: "summary_large_image",
    title: "Capture The Bug | SaaS & Cloud Platform Security",
    description:
      "Secure your cloud-based products at scale. Capture The Bug offers proactive SaaS and cloud security testing to eliminate risks before they impact users.",
    images: "https://ibb.co/Gv1xpYL8",
  },
};


export default function Saas() {
  return (
    <>
    <Landing/>
   <Security/>
   <PartnersList/>
   <Testimonial
   company={testimonial.company}
   logo={testimonial.logo}
   quote={testimonial.quote}
   author={testimonial.author}
   position={testimonial.position}
   logoSize={{ width: 140, height: 100 }}
   logoStyle={{ marginLeft: 0 }}
      />
     <BlogSection/> 
</>
  );
}