import React from 'react';

const Title = ({ children }) => (
  <h1 className="text-3xl font-bold text-blue-600 mb-6">{children}</h1>
);

const UsefulLinksPage = ({ title, children }) => {
  return (
    <div className="min-h-screen bg-gray-100 py-16 sm:py-24 lg:py-26 px-4 sm:px-6 lg:px-8">
      <div className="max-w-5xl mx-auto">
        <Title>{title}</Title>
        <div className="bg-white shadow-md rounded-lg p-6 flex flex-col gap-6">
          {children}
        </div>
      </div>
    </div>
  );
};

export default UsefulLinksPage;