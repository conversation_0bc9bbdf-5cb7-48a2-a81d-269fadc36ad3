"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { MdOutlineSecurity, MdOutlineUpdate, MdOutlinePayments, MdOutlineAssessment, MdOutlineBusinessCenter } from 'react-icons/md';

const comparisonData = [
  {
    feature: 'Scope',
    icon: <MdOutlineSecurity className="w-6 h-6" />,
    oneOff: 'Full assessment at a specific time',
    subscription: 'Full annual assessment plus monthly focused testing'
  },
  {
    feature: 'Frequency',
    icon: <MdOutlineUpdate className="w-6 h-6" />,
    oneOff: 'Single engagement',
    subscription: 'Continuous, recurring testing'
  },
  {
    feature: 'Cost',
    icon: <MdOutlinePayments className="w-6 h-6" />,
    oneOff: 'Fixed cost, single payment',
    subscription: 'Fixed, can be paid monthly or annually'
  },
  {
    feature: 'Reporting',
    icon: <MdOutlineAssessment className="w-6 h-6" />,
    oneOff: 'Comprehensive report after testing and retesting',
    subscription: 'Live vulnerability reporting through dashboard, detailed annual report'
  },
  {
    feature: 'Ideal For',
    icon: <MdOutlineBusinessCenter className="w-6 h-6" />,
    oneOff: 'One-time assessment, project-based security testing',
    subscription: 'Ongoing security, continuous compliance, proactive threat management'
  }
];

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.07
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 15 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.4 }
  }
};

export default function ServiceComparison() {
  return (
    <section className="w-full py-12 sm:py-16 md:py-20 bg-gradient-to-b from-white to-ctb-bg-light/30 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] bg-repeat opacity-[0.03]"></div>
      <div className="absolute -left-64 top-1/4 w-96 h-96 rounded-full bg-primary-blue/5 blur-3xl"></div>
      <div className="absolute -right-64 bottom-1/4 w-96 h-96 rounded-full bg-primary-blue/5 blur-3xl"></div>
      
      <div className="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <motion.div 
          className="mb-10 sm:mb-12 md:mb-16 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <span className="inline-block px-4 py-1.5 mb-4 rounded-full bg-primary-blue/10 text-primary-blue text-sm font-medium tracking-wide">COMPARISON</span>
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-[600] text-secondary-blue mb-6">
            Service Comparison
          </h2>
          <p className="text-base sm:text-lg max-w-3xl mx-auto leading-relaxed font-semibold text-black">
            Compare our different service offerings to find the best fit for your security needs.
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-blue/60 to-secondary-blue/60 mx-auto rounded-full mt-6"></div>
        </motion.div>

        {/* Enterprise-grade comparison table */}
        <div className="px-2 sm:px-4 md:px-6 lg:px-8 max-w-5xl mx-auto">
          {/* Desktop View */}
          <div className="hidden lg:block relative">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, amount: 0.1 }}
              className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200/80"
            >
              {/* Table Header */}
              <div className="grid grid-cols-12 bg-gradient-to-r from-tertiary-blue to-primary-blue text-white">
                <div className="col-span-3 p-4 sm:p-5 border-r border-white/10 flex items-center justify-center">
                  <h3 className="text-xl sm:text-2xl font-semibold">Features</h3>
                </div>
                <div className="col-span-4 p-4 sm:p-5 border-r border-white/10 text-center relative overflow-hidden">
                  <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] bg-repeat opacity-10"></div>
                  <div className="relative">
                    <h3 className="text-xl sm:text-2xl font-bold">One-Off Pentest</h3>
                  </div>
                </div>
                <div className="col-span-5 p-4 sm:p-5 text-center">
                  <h3 className="text-xl sm:text-2xl font-bold">Subscription Pentest</h3>
                </div>
              </div>
                
              {/* Table Body */}
              {comparisonData.map((item, index) => (
                <motion.div 
                  key={index}
                  variants={itemVariants} 
                  className={`grid grid-cols-12 border-b border-gray-200 last:border-b-0 ${index % 2 === 0 ? 'bg-gray-50/50' : 'bg-white'}`}
                >
                  {/* Feature Column */}
                  <div className="col-span-3 p-4 sm:p-5 border-r border-gray-200">
                    <div className="flex flex-col items-start justify-center gap-3">
                      <div className="flex items-center gap-3">
                        <div className="text-tertiary-blue/90 flex-shrink-0">{item.icon}</div>
                        <span className="font-semibold text-tertiary-blue text-lg sm:text-xl">{item.feature}</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* One-Off Column */}
                  <div className="col-span-4 p-4 sm:p-5 border-r border-gray-200 relative">
                    <div className="absolute left-0 top-5 bottom-5 w-1 bg-primary-blue/40 rounded-r"></div>
                    <div className="pl-3">
                      <p className="text-gray-600 text-base sm:text-lg leading-relaxed text-left">{item.oneOff}</p>
                    </div>
                  </div>

                  {/* Subscription Column */}
                  <div className="col-span-5 p-4 sm:p-5">
                    <p className="text-gray-600 text-base sm:text-lg leading-relaxed text-left">{item.subscription}</p>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
                
          {/* Mobile and Tablet View */}
          <div className="lg:hidden">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, amount: 0.1 }}
              className="space-y-4 sm:space-y-6"
            >
              {comparisonData.map((item, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200/80"
                >
                  {/* Feature Header */}
                  <div className="bg-gradient-to-r from-tertiary-blue to-primary-blue p-4 sm:p-5 text-white">
                    <div className="flex flex-col items-start justify-center gap-2 sm:gap-3">
                      <div className="flex items-center gap-3">
                        <div className="text-white/90 flex-shrink-0">{item.icon}</div>
                        <span className="font-semibold text-lg sm:text-xl">{item.feature}</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Comparison Content */}
                  <div className="divide-y divide-gray-200">
                    {/* One-Off Option */}
                    <div className="p-4 sm:p-5 relative">
                      <div className="absolute left-0 top-0 h-full w-1 bg-primary-blue/60"></div>
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="text-primary-blue font-semibold pl-3 text-base sm:text-lg">One-Off Penetest</h4>
                      </div>
                      <p className="text-gray-600 text-base sm:text-lg pl-3 leading-relaxed text-left">{item.oneOff}</p>
                    </div>
                    
                    {/* Subscription Option */}
                    <div className="p-4 sm:p-5 relative">
                      <div className="absolute left-0 top-0 h-full w-1 bg-tertiary-blue/60"></div>
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="text-tertiary-blue font-semibold pl-3 text-base sm:text-lg">Subscription Pentest</h4>
                      </div>
                      <p className="text-gray-600 text-base sm:text-lg pl-3 leading-relaxed text-left">{item.subscription}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
