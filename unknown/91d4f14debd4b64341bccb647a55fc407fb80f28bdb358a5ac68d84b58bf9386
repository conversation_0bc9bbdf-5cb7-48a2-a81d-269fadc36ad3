'use client'
import React, { useState } from 'react';

const FAQItem = ({ question, answer, isOpen, onToggle, uniqueId }) => (
  <div className="mb-4">
    <button
      onClick={onToggle}
      aria-expanded={isOpen}
      className="flex w-full justify-between items-center text-left bg-gray-100 p-4 rounded-lg transition-all"
    >
      <span className="md:text-lg text-sm font-medium text-gray-900">{question}</span>
      <span className="ml-4">
        {isOpen ? (
          <svg className="h-5 w-5 text-[#1e83fb]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
          </svg>
        ) : (
          <svg className="h-5 w-5 text-[#1e83fb]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
        )}
      </span>
    </button>
    <div
      className={`transition-all duration-300 ease-in-out overflow-hidden ${
        isOpen ? 'max-h-[1000px] opacity-100 py-4' : 'max-h-0 opacity-0 py-0'
      } bg-gray-50`}
    >
      <div className="px-4">
        <p className="md:text-lg text-sm text-gray-700">{answer}</p>
      </div>
    </div>
  </div>
);

const FAQ = () => {
  const [openItem, setOpenItem] = useState(null);

  const handleToggle = (uniqueId) => {
    setOpenItem(prev => (prev === uniqueId ? null : uniqueId));
  };

  const faqs = [
    {
      id: "ptaas-overview",
      question: "What is Penetration Testing as a Service (PTaaS)?",
      answer: "Capture The Bug's Penetration Testing as a Service (PTaaS) delivers continuous, scalable security testing-built for modern engineering and security teams. Our platform combines the depth of expert manual testing with the speed of real-time collaboration, live findings, and seamless integrations into your workflows. PTaaS = Pentesters + Automated Workflows + Real-Time Risk Visibility."
    },
    {
      id: "ptaas-speed",
      question: "How does PTaaS help teams move faster?",
      answer: "Say goodbye to one-off, PDF-based tests and drawn-out procurement cycles. With Capture The Bug, you can launch tests on demand, track vulnerabilities as they're discovered, and remediate faster-without waiting weeks for results. It’s how growing SaaS teams and enterprises stay ahead of threats, maintain compliance, and ship secure software-continuously."
    },
    {
      id: "ptaas-vs-traditional",
      question: "How is PTaaS different from traditional pentesting?",
      answer: "Traditional pentests are slow, static, and typically delivered as PDF reports after weeks of waiting. PTaaS offers a faster, more flexible alternative-allowing security and engineering teams to test continuously, fix issues in real time, and stay audit-ready all year long."
    },
    {
      id: "continuous-pentesting",
      question: "What is Continuous Pentesting?",
      answer: "Continuous pentesting means regularly running manual security tests across your apps, APIs, and cloud environments-so you're not relying on a once-a-year snapshot. It provides ongoing visibility into your risk posture and helps teams catch vulnerabilities as they ship code."
    },
    {
      id: "who-should-use-ptaas",
      question: "Who should use PTaaS?",
      answer: "PTaaS is ideal for fast-moving SaaS teams, regulated enterprises, and any company that ships code frequently and needs security to keep up. It's especially valuable for organizations pursuing SOC 2, ISO 27001, HIPAA, or other compliance frameworks."
    },
    {
      id: "ptaas-automated-human",
      question: "Is PTaaS automated or human-led?",
      answer: "PTaaS platforms like Capture The Bug combine the best of both worlds: real human pentesters conduct the tests, while the platform handles orchestration, reporting, and collaboration-making it scalable and developer-friendly."
    },
    {
      id: "ptaas-retesting",
      question: "Does PTaaS include retesting?",
      answer: "Yes. Capture The Bug's PTaaS subscription includes unlimited retesting, so your team can verify fixes, close findings confidently, and maintain compliance without extra cost or effort."
    },
    {
      id: "ptaas-compliance",
      question: "Can PTaaS help with compliance (SOC 2, ISO 27001, etc.)?",
      answer: "Absolutely. Continuous pentesting supports compliance by providing timely evidence of security controls, detailed vulnerability reports, and official pentest certificates-aligned with the requirements of SOC 2, ISO, and other standards."
    },
    {
      id: "ptaas-start-speed",
      question: "How fast can I start a pentest?",
      answer: "With PTaaS, you can launch a test in minutes-no waiting for contracts, SOWs, or back-and-forth emails. Simply scope your test in the dashboard, select a date, and go."
    },
    {
      id: "multiple-tests",
      question: "Can I run multiple tests at once?",
      answer: "Yes. Our platform supports parallel testing across multiple applications, teams, or environments-making it easy to scale security testing as your company grows."
    },
    {
      id: "ptaas-cost",
      question: "How much does PTaaS cost?",
      answer: "Capture The Bug offers flexible, flat-rate subscriptions that scale with your team. Pricing includes everything-test launches, retesting, reporting, and access to the platform. Get in touch for a custom quote based on your needs."
    }
  ];

  return (
    <section className="w-full py-16 bg-white">
      <div className="max-w-6xl mx-auto px-4">
        <div className="flex flex-col md:flex-row items-stretch md:gap-20 min-h-[400px]">
          {/* Left: Centered FAQ label */}
          <div className="md:w-1/3 w-full flex items-center justify-center mb-8 md:mb-0">
            <h2 className="text-5xl md:text-7xl font-bold text-[#1e83fb] text-center">FAQ</h2>
          </div>
          {/* Right: FAQ List */}
          <div className="md:w-2/3 w-full flex flex-col justify-center">
            {faqs.map((faq) => (
              <FAQItem
                key={faq.id}
                uniqueId={faq.id}
                question={faq.question}
                answer={faq.answer}
                isOpen={openItem === faq.id}
                onToggle={() => handleToggle(faq.id)}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;