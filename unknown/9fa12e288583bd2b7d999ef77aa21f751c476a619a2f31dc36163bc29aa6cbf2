import UsefulLinksPage from '@/app/common/pages/UsefulLinksPage'
import React from 'react'

export const metadata = {
  openGraph: {
    title: "Capture The Bug | Customer Terms and Conditions",
    type: "website",
    url: "https://capturethebug.xyz/Useful-Links/Customer-Terms&Conditions",
    description:
      "These terms and conditions (the “Agreement”) are between Capture The Bug (“CTB”), a New Zealand based bug bounty platform, and the Customer (“Customer”) who wishes to engage CTB to perform crowdsourced security testing services (the “Services”). ",
    images:  "https://i.postimg.cc/V6Y2Y1Ld/Customer-Terms-Conditions.png"
  },
};

export default function page() {
  return (
    <div>
      <title>Capture The Bug | Customer Terms and Conditions</title>
      <UsefulLinksPage title="Customer Terms and Conditions">
        {/* <div className='font-bold'>Code of Ethics</div>        
        <div className='font-bold'>CTB-COE-V2.0</div>        
        <div className='font-bold'>Last update 16-03-2023</div>       */}


<div className='font-bold'>Customer Terms and Conditions</div>


        <p className="mb-7 font-bold">1. Introduction</p>
        <p className='text-slate-600'>
          These terms and conditions (the “Agreement”) are between Capture The Bug (“CTB”), a New Zealand based bug bounty platform, and the Customer (“Customer”) who wishes to engage CTB to perform crowdsourced security testing services (the “Services”). This Agreement sets forth the terms and conditions that govern the engagement between CTB and the Customer and the use of CTB’s hosted service (the “Hosted Service”). By accessing or using the Hosted Service, the Customer agrees to be bound by the terms and conditions of this Agreement. If the Customer does not agree to all of the terms and conditions of this Agreement, the Customer may not use the Hosted Service.
        </p>
      
      
        <p className="mb-7 font-bold">2. Purpose of Terms and Conditions</p>
        <p className='text-slate-600'>
          The purpose of this Agreement is to define the terms and conditions under which CTB will provide the Services to Customer and the rights and responsibilities of both parties with respect to the Services and the Hosted Service. This Agreement also outlines the provisions for payment, confidentiality, intellectual property rights, and indemnification with respect to the Services and the Hosted Service.
        </p>

        <p className="mb-7 font-bold">3. Definitions of Key Terms</p>




        <table className="w-full border-collapse border border-gray-300">
  <thead>
    <tr>
      <th className="border border-gray-300 px-4 py-2">Key Term</th>
      <th className="border border-gray-300 px-4 py-2">Definition</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td className="border border-gray-300 px-4 py-2">Agreement</td>
      <td className="border border-gray-300 px-4 py-2">
        A legally binding contract between the Customer and CTB that outlines the terms and conditions under which Customers can access and use CTB’s Bug Bounty Platform and services.
      </td>
    </tr>
    <tr>
      <td className="border border-gray-300 px-4 py-2">Bounty</td>
      <td className="border border-gray-300 px-4 py-2">
        Financial reward offered by the Customer to Security Researchers for identifying and reporting qualifying vulnerabilities in the Target Assets.
      </td>
    </tr>
    <tr>
      <td className="border border-gray-300 px-4 py-2">Crowdsourced Security Program</td>
      <td className="border border-gray-300 px-4 py-2">
        Referred to in DPTS including Bug Bounty, VDP, or on-demand penetration testing program established by the Customer through the Hosted Service, in which Security Researchers are invited to test the security of the Target Assets and report any vulnerabilities discovered.
      </td>
    </tr>
    <tr>
      <td className="border border-gray-300 px-4 py-2">CTB</td>
      <td className="border border-gray-300 px-4 py-2">
        Capture The Bug – A New Zealand-based incorporated company that provides crowdsourced security and Bug Bounty Platform services.
      </td>
    </tr>
    <tr>
      <td className="border border-gray-300 px-4 py-2">Customer</td>
      <td className="border border-gray-300 px-4 py-2">
        Individual or Organisation who wishes to engage the CTB Platform and services.
      </td>
    </tr>
    <tr>
      <td className="border border-gray-300 px-4 py-2">Customer Data</td>
      <td className="border border-gray-300 px-4 py-2">
        Any data or information provided by Customer to CTB or inputted by Customer into the Hosted Service under this “Agreement”.
      </td>
    </tr>
    <tr>
      <td className="border border-gray-300 px-4 py-2">Distributed Penetration Testing Service (DPTS)</td>
      <td className="border border-gray-300 px-4 py-2">
        Distributed penetration testing is a type of security testing in which a group of individuals or Organisations contribute their skills and expertise to test the security of a system or network. In a distributed penetration testing scenario, the testing is typically conducted remotely and may involve a large number of testers working together to identify and report vulnerabilities.
      </td>
    </tr>
    <tr>
      <td className="border border-gray-300 px-4 py-2">Hosted Services</td>
      <td className="border border-gray-300 px-4 py-2">
        The cloud-based platform and related services provided by CTB to facilitate the Crowdsourced Security Program, including DPTS as described in the Contract.
      </td>
    </tr>
    <tr>
      <td className="border border-gray-300 px-4 py-2">Know Your Customer (KYC)</td>
      <td className="border border-gray-300 px-4 py-2">
        Know Your Customer or KYC is the mandatory process required by CTB to identify and verify the Security Researchers.
      </td>
    </tr>
    <tr>
      <td className="border border-gray-300 px-4 py-2">Program Guide</td>
      <td className="border border-gray-300 px-4 py-2">
        The written description of the scope, rules, and other details of a Crowdsourced Security Program, as provided by the Customer to CTB.
      </td>
    </tr>
    <tr>
      <td className="border border-gray-300 px-4 py-2">Sample Test Report</td>
      <td className="border border-gray-300 px-4 py-2">
        A sample report provided by CTB to the Customer for the purpose of demonstrating the format and content of the Testing Report.
      </td>
    </tr>
    <tr>
      <td className="border border-gray-300 px-4 py-2">Security Researchers</td>
      <td className="border border-gray-300 px-4 py-2">
        Individuals who participate in the Crowdsourced Security Program by testing the security of the Target Assets and reporting any vulnerabilities discovered. There are two types of security researchers present on the platform CTB Verified and Unverified or General Researchers. CTB Verified Researchers have completed the Know Your Customer through Third Party Partner appointed by CTB.
      </td>
    </tr>
    <tr>
      <td className="border border-gray-300 px-4 py-2">Services</td>
      <td className="border border-gray-300 px-4 py-2">
        Services Provided by CTB to Customer, including but not limited to the crowdsourced security testing services, and Hosted services.
      </td>
    </tr>
    <tr>
      <td className="border border-gray-300 px-4 py-2">Target Assets</td>
      <td className="border border-gray-300 px-4 py-2">
        The websites, applications, or other systems that are the subject of a Crowdsourced Security Program.
      </td>
    </tr>
    <tr>
      <td className="border border-gray-300 px-4 py-2">Testing Report</td>
      <td className="border border-gray-300 px-4 py-2">
        The written report provided by CTB to Customer, detailing the results of the Testing Services, including any vulnerabilities identified and recommendations for remediation.
      </td>
    </tr>
    <tr>
      <td className="border border-gray-300 px-4 py-2">VDP (Vulnerability Disclosure Program)</td>
      <td className="border border-gray-300 px-4 py-2">
        Customer allows external security researchers to report vulnerabilities in their Target Assets through Hosted services.
      </td>
    </tr>
  </tbody>
</table>



<p className="mb-7 font-bold">4. Engagement</p>

<p className="mb-7 font-bold">Scope of Terms and Conditions</p>
<p className='text-slate-600'>
  These Terms apply to the Customer’s use of the Services. In addition to these Terms, Customer’s use of the Services is also subject to any Contract, Program Guide, and any other agreements or policies which include any amended or established documents of the same or similar nature in the future provided by CTB.
</p>

<p className="mb-7 font-bold">Acceptance of the Terms and Conditions</p>
<p className='text-slate-600'>
  By accessing or using the Services, the Customer agrees to be bound by these Terms. If a Customer does not agree to the entirety of the terms and conditions of this Agreement, the Customer may not access or use the Services.
</p>

<p className="mb-7 font-bold">5. Provision of Hosted Services</p>

<p className="mb-7 font-bold">Description of Hosted Services</p>
<p className='text-slate-600'>
  CTB provides the Hosted Services as an online platform for organizing and managing the Crowdsourced Security Program. Through the Hosted Services, Customer can request the Testing Services, view and manage the Testing Report, and communicate with Security Researchers.
</p>

<p className="mb-7 font-bold">Access to the Hosted Services</p>
<p className='text-slate-600'>
  CTB will make its Hosted Services available to the Customer for use during the agreed upon term of this Agreement and any applicable agreements and contracts. CTB will also maintain a program to ensure the security and integrity of the Hosted Service and the Testing Report, using industry standards. They will also make an effort to keep the Hosted Service available 24/7, except for scheduled maintenance (which they will give reasonable advance notice for) or any downtime caused by unforeseen events or circumstances beyond their control. Customers are only allowed to use the Hosted Services for the purpose of receiving the Testing Services specified in the applicable contract, and they must follow the restrictions below.
</p>

<p className="mb-7 font-bold">Restrictions on the Use of the Hosted Services</p>
<div className='text-slate-600'>

  <ul className="list-disc pl-7">
    <li>The Customer is not allowed to sell, rent, lease, transfer, assign, reproduce, distribute, host, or use the Hosted Service for commercial gain or to benefit any third party;</li>
    <li>The Customer is not allowed to modify, translate, adapt, merge, create derivative works from, disassemble, decompile, reverse compile, or reverse engineer the Hosted Service or try to discover the source code of the underlying software, unless it is legally allowed to do so;</li>
    <li>The Customer is not allowed to bypass or disable any digital rights management, usage rules, or other security features of the Hosted Service, or try to gain unauthorized access to the Hosted Service or disrupt its integrity or performance, or the data contained within it;</li>
    <li>The Customer is not allowed to use the Hosted Service to build a similar or competing website, application, or service;</li>
    <li>The Customer is not allowed to copy, reproduce, distribute, republish, download, display, post, or transmit any part of the Hosted Service in any form or by any means;</li>
    <li>The Customer is not allowed to remove or destroy any copyright or proprietary notices contained in the Hosted Service.</li>
  </ul>
  
  
</div>

<p className='text-slate-600'>
The Customer must use the Hosted Service in compliance with all applicable laws. The Customer is responsible for all activities conducted under their logins on the Hosted Service and for complying with this Agreement. The Customer is responsible for securing all passwords and other access protocols needed to access the Hosted Service. The Customer must notify CTB immediately if their passwords or access protocols are lost, stolen, disclosed to an unauthorized third party, or otherwise compromised.
</p>

<p className="mb-7 font-bold">6. Contracts</p>
<p className='text-slate-600'>
  Each Crowdsourced Security Program that the Customer orders during the term of this Agreement will be described in a CTB quotation or similar purchase order. The security program will become effective either when both parties sign it or when the Customer issues a purchase order that references the CTB contract (which means that the Customer agrees to the terms of the contract). The security program will start on a date that CTB and the Customer agree upon. All contracts placed on an annual basis will automatically renew for additional one-year terms at the current prices unless the contract states otherwise or unless either party informs the other party of their intention to terminate the contract within 60 days before the end of the current year’s term. All other contracts (meaning on-demand contracts or contracts not placed on an annual basis) will expire when the security program is completed or when the security program is terminated or expires according to this Agreement.
</p>

<p className="mb-7 font-bold">7. Performance of the Testing Services</p>
<p className='text-slate-600'>
  Upon signing a contract with CTB, both parties will assign a specific person to manage their respective roles in the Crowdsourced Security Program. CTB will provide a description of the program (Program Guide) to Security Researchers, who will then search for and report any vulnerabilities they find in the Target systems. Customers can access this information through the Hosted Service for the duration of the program. CTB will review and verify the reported vulnerabilities, determine if they fall within the scope of the program, provide instructions to Customers on how to reproduce the vulnerabilities, and determine if Bounty are due for any validated vulnerabilities according to the terms of the program. Testing Services must be completed within the specified time frame of the contract or they will be forfeited.
</p>

<p className="mb-7 font-bold">8. Payments to Security Researchers</p>
<p className='text-slate-600'>
  CTB will provide reports to the Customer through the Hosted Services that recommend Bounty for the Security Researchers based on their work in the applicable Crowdsourced Security Program. The Customer will have five (5) days to review, approve, or reject these recommendations. If a Customer rejects recommendations, they must provide written notice with their reasons for rejection. CTB will review the issue and provide the revised report. The recommendation will be considered approved unless the Customer provides written notice within the approval period. After approval, CTB will pay the approved reward to the security researcher using the Customer’s deposited bounty pool. CTB must give all the Bounty in connection with the Program Guide for the specific Target Asset.
</p>

<p className="mb-7 font-bold">9. Relationship Between Capture The Bug (CTB) and Security Researchers</p>
<p className='text-slate-600'>
  CTB uses its technology to bring together Customers and Security Researchers, but it does not control or oversee the Security Researchers, who are not employees of CTB. The relationship between CTB and the Security Researchers is that of an independent contractor. This Agreement does not create a partnership, joint venture, or employer-employee relationship between the Security Researchers and CTB, or between Customer and any employees, agents, or contractors of CTB. The Security Researchers are not agents of CTB and do not have the authority to act on behalf of CTB.
</p>

<p className="mb-7 font-bold">10. Fees for the Hosted Services and Testing Services and Payment Terms</p>
<p className='text-slate-600'>
  Customers will pay CTB fees for each Crowdsourced Security Program as specified in the applicable Contract within thirty (30) days of receiving an invoice. Unless otherwise stated in the Contract, all Fees shall be invoiced upon execution of the Contract. These fees will include an annual or monthly Licence fee, Triage fee, and a pool of money that Customers will deposit for Bounty (Bounty Pool) to be paid to Security Researchers. If the pool of money for Bounty runs out before the Crowdsourced Security Program is complete, Customers can add more money to the pool. Any remaining money in the Bounty pool at the end of the agreement can be used for a future Contract or returned to the Customer, whichever they prefer. Customers are also responsible for paying any taxes, duties, or other fees related to the Services.
</p>

<p className='text-slate-600'>
  If payment is not received within thirty (30) days of the invoice date, a late payment fee of 3% per month will be applied to the unpaid balance calculated and apportioned daily to the date that the unpaid balance is paid up to date. This fee will be calculated and added to the unpaid balance on the first day of each month compounding until the full amount is paid in full. The Customer agrees to pay all costs and expenses (including reasonable attorneys’ fees) incurred by CTB in enforcing its rights under this clause. If a Customer’s account is more than thirty (30) days overdue on payment, CTB may suspend the Services until the Customer has settled all payments due and owing to CTB.
</p>

<p className='text-slate-600'>
  If a Customer gets a price discount, they may have to do joint marketing activities with CTB, such as a Customer case study or press release, and allow CTB to use their logo in these activities.
</p>

<p className="mb-7 font-bold">11. Confidentiality</p>
<p className='text-slate-600'>
Confidential Information refers to any information that is marked as confidential or would be considered confidential, based on the circumstances and content of the information. This can include information shared between the parties during this Agreement, the pricing and documentation in the contract, information about the security researchers, metadata related to the testing report, and Customer data. Both parties agree to not use or disclose the other party’s confidential information. However, the receiving party can disclose confidential information if required by law, but must give the disclosing party advance notice to contest the disclosure. The rights to the confidential information will remain with the disclosing party. In addition, some crowdsourced security programs may include provisions for the disclosure of certain vulnerabilities to third parties, such as government agencies or industry groups, in order to promote the responsible disclosure and addressing of security issues.</p>


<p className="mb-7 font-bold">12. Ownership</p>
      <p className='text-slate-600'>
        CTB owns the rights to the Hosted Service and any improvements made to it, and Customer owns the rights to the Target Assets and any improvements made to it. CTB can only use the Testing Report as needed to perform the Testing Services and make it available to the Customer through the Hosted Service. The Customer can only use the Testing Report for its own internal business purposes in connection with the Crowdsourced Security Program. CTB has the right to use and share information from the Testing Report that doesn’t identify the Customer, and the Customer can suggest ideas or give feedback to CTB about how to improve the Hosted Services or Testing Services. However, these suggestions do not give CTB any rights to the Customer’s patents or copyrights, and CTB cannot publicly state that any suggestions came from Customers.
      </p>

      <p className="mb-7 font-bold">13. Intellectual Property Rights</p>
      <p className='text-slate-600'>
        Intellectual Property Rights refers to all patents, copyrights, trade secrets, trademarks, and other proprietary and intellectual property rights on a worldwide basis, including applications and registrations for these rights. This includes patents for inventions, copyrights for original works, trade secrets for confidential information, trademarks for branding and branding-related elements, and other intellectual property rights such as moral rights. These rights are used to protect creative and innovative works and ideas.
      </p>

      <p className="mb-7 font-bold">14. Capture The Bug Representation and Warranties</p>
      <p className='text-slate-600'>
        CTB promises that it will try its best to make sure the Services are done in a professional and skilled way that meets industry standards. CTB has the right and ability to enter into and carry out this Agreement. CTB will follow all laws, regulations, and rules that apply to its performance under this Agreement. CTB does not guarantee that the Testing Services will find all vulnerabilities or that the results of the Hosted Service and Testing Services will make sure Customer’s applications or systems are secure. CTB does not guarantee that the Hosted Service will work without errors or interruptions. Except for the warranties specifically mentioned in this section, to the maximum extent allowed by law, the Hosted Service and Testing Services are provided “as is” and CTB does not make any other promises or guarantees, either express or implied, about the Hosted Service and Testing Services, including their quality, suitability for a particular purpose, or that they do not infringe on anyone else’s rights.
      </p>

      <p className="mb-7 font-bold">15. Indemnification</p>
      <p className='text-slate-600'>
        CTB will protect the Customer from any legal action brought by a third party if the action is based on a claim that the Hosted Service violates any New Zealand patent or copyright or steals any trade secrets. CTB will cover the costs and damages that may result from such action. This protection is only valid if the Customer informs CTB about the legal action in writing, allows CTB to have full control over the defense and settlement negotiations, and where practicable the Customer will be expected to assist CTB with the defence if requested. If the Hosted Service may potentially be the subject of legal action for infringement, CTB can choose to either obtain the right for the Customer to continue using the Hosted Service, modify the Hosted Service to no longer be infringing, or end the agreement and refund any prepaid, unused fees to Customer. However, CTB will not be responsible for any legal action based on the use of the Hosted Service in a way that goes against the agreement, the use of the Hosted Service with products or data not provided by CTB, or any modifications to the Hosted Service made by someone other than CTB. This section outlines CTB’s full responsibility and Customer’s only solution for legal actions related to infringement claims. Customer will defend CTB from any legal action brought by a third party if the action is based on a claim that CTB’s access to the Target Assets or the data within the Target Assets was not authorised. Customer will also cover the costs and damages that may result from such action. This protection is only valid if Customer agrees to the settlement of such action.
      </p>

      <p className="mb-7 font-bold">16. Limitation of Liability</p>
      <p className='text-slate-600'>
      CAPTURE THE BUG (CTB) AND THE CUSTOMER AGREE THAT THE TOTAL AMOUNT OF DAMAGES THAT EITHER PARTY COULD POTENTIALLY BE RESPONSIBLE FOR WILL NOT EXCEED THE TOTAL AMOUNT PAID, PROPOSED TO BE PAID OR CONTEMPLATED TO BE PAID TO CAPTURE THE BUG (CTB) FOR THE HOSTED SERVICES IN THE TWELVE (12) MONTHS LEADING UP TO THE EVENT OR ACTION THAT CAUSED THE LIABILITY. NEITHER PARTY WILL BE HELD RESPONSIBLE FOR LOST PROFITS, LOSS OF BUSINESS, LOSS OF USE OR LOSS OF DATA, DELAYS OR INTERRUPTIONS OF BUSINESS, OR LOST GOODWILL, OR FOR THE COST OF OBTAINING SUBSTITUTE GOODS, SOFTWARE OR SERVICES, OR FOR ANY INCIDENTAL, INDIRECT, CONSEQUENTIAL OR PUNITIVE DAMAGES. THIS LIMITATION ON LIABILITY APPLIES TO ANY ISSUES THAT ARISE FROM OR RELATE TO THIS AGREEMENT, EVEN IF EITHER PARTY WAS AWARE THAT SUCH DAMAGES WERE POSSIBLE OR MAY EXSIST.</p>

      <p className="mb-7 font-bold">17. Terms of Termination</p>
      <p className='text-slate-600'>
      The agreement will commence on a certain date agreed to between the Parties and will last until it is terminated by either party in accordance with the terms of the agreement. Either party may end the agreement or any contract immediately by writing to the other party if they have significantly broken a part of the agreement or contract and the breach has not been fixed within thirty (30) days of receiving notice of the breach.</p>
      <p className='text-slate-600'>
      Upon termination or expiration of this Agreement or the applicable Contract, the Customer will immediately and without delay stop using the Hosted Service. Some sections of the Agreement, including definitions, the independent contractor relationship, fees, confidentiality, ownership, CTB’s representations and warranties, indemnification, limitation of liability, and the effects of termination will endure even after the Agreement ends.</p>


      <p className="mb-7 font-bold">18. Generic Provision</p>
      <p className='text-slate-600'>
     New Zealand laws shall govern any action arising out of or related to this Agreement, and the choice of law rules of any jurisdiction shall not apply. Each party agrees to the exclusive personal jurisdiction and venue of the People’s Courts located in Hamilton, New Zealand. Suppose any provision of this Agreement is held to be invalid or unenforceable. Suppose any part of this Agreement is determined to be invalid or unenforceable. In that case, the other parts of the Agreement will still apply, and the invalid or unenforceable part will be changed to be legally valid and enforceable as much as possible. This Agreement may not be assigned by a party without the other party’s express prior written consent, except in connection with the merger or sale of substantially all of such party’s stock or assets or some other acquisition transaction. Any attempted assignment in violation of the foregoing will be null and void. Neither party shall be liable under this Agreement for failure or delay in performance caused by a Force Majeure Event, except for payment obligations. If a Force Majeure Event occurs, the party affected shall use commercially reasonable efforts to resume the performance excused by the Force Majeure Event. “Force Majeure Event” means any event beyond the reasonable control of the party affected by such an event that causes a party to delay or fail to perform under this Agreement. Customers may not use, export, import, or transfer the Hosted Service or Testing Report except strictly with all applicable laws, including but not limited to all New Zealand export laws and regulations. In the event of any conflict between this Agreement and an accepted Contract, this Agreement will control unless the Contract expressly modifies the terms of this Agreement with respect to the Crowdsourced Security Program described in that Contract. In order to waive a charge, a waiver must be signed by the person whose charges will be waived. If either party does not enforce a specific part of this Agreement at any time, it does not mean they will not enforce it in the future. This Agreement is the final and complete Agreement between the parties and replaces any previous agreements or understandings between them. CTB may modify, amend or update this Agreement at any time without notice. Except for Contracts, the terms of any purchase order or similar document submitted by Customer to CTB will have no force or effect.</p>


     <p className="mb-7 font-bold">19. Acceptance of the Terms and Conditions</p>
      <p className='text-slate-600'>
      By engaging CTB services, the Customer agrees and acknowledges that it has read and understands all of these terms and conditions and accepts that:</p>
      <p className='text-slate-600'>
      CAPTURE THE BUG IS PROVIDED “AS-IS,” “AS AVAILABLE,” WITH “ALL FAULTS”, AND ALL WARRANTIES, EXPRESS OR IMPLIED, ARE DISCLAIMED (INCLUDING BUT NOT LIMITED TO THE DISCLAIMER OF ANY IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE). CAPTURE THE BUG MAY CONTAIN BUGS, ERRORS, PROBLEMS OR OTHER LIMITATIONS. CAPTURE THE BUG, INCLUDING ALL OUR AFFILIATES, HAVE NO LIABILITY WHATSOEVER FOR YOUR USE OF OUR WEBSITE, OTHER THAN AS SPECIFIED IN OUR LEGAL TERMS. CAPTURE THE BUG CANNOT GUARANTEE AND DOES NOT PROMISE ANY SPECIFIC RESULTS FROM USE OF OUR WEBSITE. CAPTURE THE BUG DOES NOT REPRESENT OR WARRANT THAT CAPTURE THE BUG IS ACCURATE, COMPLETE, RELIABLE, CURRENT OR ERROR-FREE OR THAT IT IS FREE OF VIRUSES OR OTHER HARMFUL COMPONENTS. THEREFORE, YOU SHOULD EXERCISE CAUTION IN THE USE AND DOWNLOADING OF ANY SUCH CONTENT AND USE INDUSTRY-RECOGNIZED PROGRAMS TO DETECT AND REMOVE VIRUSES. ALL RESPONSIBILITY OR LIABILITY FOR ANY DAMAGES CAUSED BY VIRUSES SOMEHOW ATTRIBUTED TO CAPTURE THE BUG IS DISCLAIMED. WITHOUT LIMITING THE FOREGOING, YOU UNDERSTAND AND AGREE THAT YOU USE CAPTURE THE BUG AT YOUR OWN RISK AND THAT YOU WILL BE SOLELY RESPONSIBLE FOR YOUR USE THEREOF AND ANY DAMAGES TO YOU, YOUR MOBILE DEVICE OR COMPUTER SYSTEM, OR OTHER HARM OF ANY KIND THAT MAY RESULT. WE, ARE NOT LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL OR CONSEQUENTIAL DAMAGES (INCLUDING DAMAGES FOR LOSS OF INCOME, BUSINESS, PROFITS, LITIGATION, OR THE LIKE), WHETHER BASED ON BREACH OF CONTRACT, BREACH OF WARRANTY, TORT (INCLUDING NEGLIGENCE), PRODUCT LIABILITY OR OTHERWISE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGES. THE NEGATION AND LIMITATION OF DAMAGES SET FORTH ABOVE ARE FUNDAMENTAL ELEMENTS OF THE BASIS OF THE BARGAIN BETWEEN YOU AND CAPTURE THE BUG. CAPTURE THE BUG WOULD NOT BE PROVIDED WITHOUT SUCH LIMITATIONS. NO ADVICE OR INFORMATION, WHETHER ORAL OR WRITTEN, OBTAINED BY YOU FROM US THROUGH CAPTURE THE BUG SHALL CREATE ANY WARRANTY, REPRESENTATION OR GUARANTEE NOT EXPRESSLY STATED IN OUR LEGAL TERMS.</p>





      


   






        
      
      </UsefulLinksPage>
    </div>
  )
}
