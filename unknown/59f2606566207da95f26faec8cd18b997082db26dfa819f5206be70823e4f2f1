'use client';

import React, { useState } from 'react';
import { Plus, Minus } from 'lucide-react';

const FAQItem = ({ question, answer, isOpen, onToggle, uniqueId }) => (
  <div className="mb-4">
    <button
      onClick={onToggle}
      aria-expanded={isOpen}
      className="flex w-full justify-between items-center text-left bg-gray-100 p-4 rounded-lg transition-all"
    >
      <span className="text-sm md:text-lg font-medium text-gray-900">
        {question}
      </span>
      <span className="ml-4 shrink-0">
        {isOpen ? (
          <Minus className="h-5 w-5 text-[#1e83fb]" />
        ) : (
          <Plus className="h-5 w-5 text-[#1e83fb]" />
        )}
      </span>
    </button>

    <div
      className={`transition-all duration-300 ease-in-out overflow-hidden ${
        isOpen ? 'max-h-[1000px] opacity-100 py-4' : 'max-h-0 opacity-0 py-0'
      } bg-gray-50`}
    >
      <div className="px-4">
        <p className="text-sm md:text-base text-gray-700 leading-relaxed">{answer}</p>
      </div>
    </div>
  </div>
);

const FAQ = () => {
  const [openItem, setOpenItem] = useState(null);

  const handleToggle = (uniqueId) => {
    setOpenItem((prev) => (prev === uniqueId ? null : uniqueId));
  };

  const faqs = [
    {
      id: 'webapp-pt-definition',
      question: 'What is Web Application Penetration Testing (Web App PT)?',
      answer:
        'Web Application Penetration Testing involves simulating real-world cyberattacks on your web applications to identify and address security vulnerabilities. This proactive approach helps ensure that your applications are resilient against potential threats.',
    },
    {
      id: 'ptaas-difference',
      question:
        "How does Capture The Bug's PTaaS differ from traditional penetration testing?",
      answer:
        "Capture The Bug's Penetration Testing as a Service (PTaaS) offers continuous, on-demand testing integrated directly into your development and deployment pipelines. Unlike traditional, time-bound testing, PTaaS provides real-time insights, collaborative workflows, and aligns with agile development practices.",
    },
    {
      id: 'vulnerability-types',
      question:
        'What types of vulnerabilities can be identified through your testing?',
      answer:
        'Our testing uncovers a range of vulnerabilities, including those listed in the OWASP Top 10, misconfigurations, insecure APIs, and business logic flaws. We also simulate real-world attack scenarios to identify hidden weaknesses in your web applications.',
    },
    {
      id: 'tool-integration',
      question: 'Can Capture The Bug integrate with our existing development tools?',
      answer:
        'Yes, our platform seamlessly integrates with popular project management and communication tools like Jira and Slack. This ensures that vulnerability findings and remediation steps are communicated effectively within your existing workflows.',
    },
    {
      id: 'testing-process',
      question: 'How does the testing process work with Capture The Bug?',
      answer:
        'Our process involves scoping and planning to define objectives and deliverables; testing and exploitation to identify security flaws; reporting and recommendations with detailed findings and remediation guidance; and continuous support, offering ongoing assistance for retesting to ensure your systems remain secure.',
    },
    {
      id: 'standards-followed',
      question: 'What standards do you follow during testing?',
      answer:
        'We adhere to industry-leading standards, including the OWASP Top 10, to ensure comprehensive and reliable security testing. Our methodologies are designed to align with best practices and regulatory requirements.',
    },
    {
      id: 'initiate-speed',
      question: 'How quickly can we initiate a penetration test with your platform?',
      answer:
        "Capture The Bug's PTaaS platform allows for rapid deployment of penetration tests. You can schedule tests on demand, aligning with your development cycles and ensuring timely assessments.",
    },
    {
      id: 'post-support',
      question: 'Do you provide support after the assessment is completed?',
      answer:
        'Absolutely. We offer continuous support, including assistance with remediation and retesting, to ensure that identified vulnerabilities are effectively addressed and that your applications remain secure over time.',
    },
    {
      id: 'cloud-suitability',
      question: 'Is your service suitable for cloud-based applications?',
      answer:
        'Yes, our penetration testing services are designed to assess both cloud-based and on-premises web applications. We tailor our approach to match your specific infrastructure and deployment models.',
    },
    {
      id: 'compliance',
      question: 'How does your platform help in meeting compliance requirements?',
      answer:
        "Our comprehensive testing and detailed reporting assist in meeting various compliance standards by identifying and addressing security vulnerabilities, thereby supporting your organization's regulatory obligations.",
    },
  ];

  return (
    <section className="w-full py-16 bg-gray-50">
      <div className="max-w-6xl mx-auto px-4">
        <div className="flex flex-col md:flex-row md:gap-20 gap-10">
          {/* Left: Section Heading */}
          <div className="md:w-1/3 w-full flex items-center justify-center">
            <h2 className="text-4xl md:text-6xl font-bold text-[#1e83fb] text-center">
              FAQ
            </h2>
          </div>

          {/* Right: FAQ Items */}
          <div className="md:w-2/3 w-full flex flex-col justify-center">
            {faqs.map((faq) => (
              <FAQItem
                key={faq.id}
                uniqueId={faq.id}
                question={faq.question}
                answer={faq.answer}
                isOpen={openItem === faq.id}
                onToggle={() => handleToggle(faq.id)}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
