import React, { useMemo } from "react";
import Link from "next/link";
import { MdPeople, MdMiscellaneousServices, MdArrowOutward } from "react-icons/md";

const ValueCard = ({ Icon, title, href, buttonTitle }) => (
  <div className="bg-gray-100 p-8 rounded-lg hover:bg-orange-100">
    <Icon className="text-black w-8 h-8 mb-4" />
    <h3 className="text-2xl font-semibold mb-4">{title}</h3>
    <div className="text-sm text-black hover:underline">
      <Link href={href} className="flex items-center gap-2">
        {buttonTitle} <MdArrowOutward size={15} />
      </Link>
    </div>
  </div>
);

export default function Mission() {
  const values = useMemo(() => [
    {
      Icon: MdPeople,
      title: "Join our Team",
      href: "/Company/Contact-Us",
      buttonTitle: "Contact us",
    },
    {
      Icon: MdMiscellaneousServices,
      title: "See our services",
      href: "/Company/Penetration-Testing",
      buttonTitle: "Learn more",
    },
  ], []);

  return (
    <div className="md:p-28 p-10">
      <div className="md:text-4xl text-3xl bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent text-center mb-10 md:px-72 font-bold">
        Join our mission to democratize security testing
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {values.map((value, index) => (
          <ValueCard
            key={index}
            Icon={value.Icon}
            title={value.title}
            href={value.href}
            buttonTitle={value.buttonTitle}
          />
        ))}
      </div>
    </div>
  );
}
