export const metadata = {
  title: "Capture The Bug | #1 Penetration Testing as a Service (PTaaS) Platform 2025",
  description: "Leading PTaaS platform for continuous penetration testing. Get real-time vulnerability detection, expert-led security assessments, and compliance-ready reports. Trusted by 500+ companies across AU, NZ & US. Start your free security assessment today.",
  keywords: "penetration testing as a service, PTaaS platform, continuous security testing, vulnerability assessment, web application penetration testing, API security testing, network penetration testing, cybersecurity compliance, SOC 2 penetration testing, ISO 27001 security testing, OWASP testing, security audit, ethical hacking services, penetration testing companies, cybersecurity platform, automated security testing, manual penetration testing, security vulnerability scanner, penetration testing tools, cybersecurity consulting",
  robots: "index, follow",
  icons: {
    icon: '/favicon.ico',
  },
  openGraph: {
    title: "Capture The Bug | #1 Penetration Testing as a Service (PTaaS) Platform 2025",
    type: "website",
    url: "https://capturethebug.xyz/",
    description: "Leading PTaaS platform for continuous penetration testing. Get real-time vulnerability detection, expert-led security assessments, and compliance-ready reports. Trusted by 500+ companies across AU, NZ & US.",
    images: "https://i.ibb.co/9m1ScnX1/Screenshot-2025-06-18-115057.png",
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Capture The Bug | #1 Penetration Testing as a Service (PTaaS) Platform 2025',
    description: 'Leading PTaaS platform for continuous penetration testing. Get real-time vulnerability detection, expert-led security assessments, and compliance-ready reports. Trusted by 500+ companies.',
    images: "https://i.ibb.co/9m1ScnX1/Screenshot-2025-06-18-115057.png",
  }
};
 
import Home from "./Home";

export default function HomePage() {
  return <Home />;
}
