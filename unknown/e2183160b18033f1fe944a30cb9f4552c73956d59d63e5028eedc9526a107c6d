import React from 'react';
import Image from 'next/image';
import { FaQuoteRight } from "react-icons/fa";

export default function Testimonial() {
  return (
    <div className="flex justify-center items-center py-32 bg-white md:px-0 px-4">
      <div className="flex items-center border-2 border-blue-800 rounded-lg p-6 shadow-lg max-w-6xl gap-10 flex-col md:flex-row md:py-14 py-10">
        <div className="flex items-start gap-4">
          <FaQuoteRight className="text-blue-800 text-2xl" />
          <div className="flex-1 md:pr-28">
            <blockquote className="text-gray-800 font-medium text-sm md:text-lg">
              Capture The Bug has efficiently and affordably helped us meet our cybersecurity goals. Their tailored solutions and proactive approach have fortified our defenses, providing peace of mind. The real-time bug reports and their dedicated assistance ensure we are vigilant against cyber threats.
            </blockquote>
            <figcaption className="text-slate-700 text-sm md:text-lg py-4">
              - <PERSON>, Chief Operating Officer at Partly
            </figcaption>
          </div>
        </div>
        <div className="relative flex-shrink-0 w-32 h-32 ml-6 md:mt-0 mt-32">
          <div className="absolute w-64 h-64 bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] -top-28 -right-8"></div>
          <div className="relative w-64 h-64 -top-16 -left-14">
            <Image
              src="/images/NathanPic.png"
              alt="Nathan Taylor's portrait"
              layout="fill"
              objectFit="cover"
              className="border border-blue-600"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
