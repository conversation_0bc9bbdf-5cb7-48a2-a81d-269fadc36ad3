import React from "react";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";

export default function Security() {
  return (
    <>
      <div className="bg-white py-10 sm:py-16 md:py-24">
        <div className="container mx-auto px-4 sm:px-6 md:px-12 lg:px-24">
          <div className="mb-10 sm:mb-14 md:mb-16 text-center md:text-left">
            <div className="text-secondary-blue text-lg sm:text-xl font-bold mb-3 sm:mb-5">
              How Capture The Bug Helps
            </div>
            <h2 className="text-2xl sm:text-4xl md:text-5xl font-bold text-[#010D2C] leading-tight mb-4 sm:mb-5">
              Securing the future of digital finance
            </h2>
            <h3 className="text-xl sm:text-xl md:text-2xl font-bold text-secondary-blue leading-tight mb-4 sm:mb-5">
              Trusted Penetration Testing & Security Platform for Banks, Fintechs & Payment Providers
            </h3>
            <p className="text-[#6B7280] text-base sm:text-lg md:text-xl leading-relaxed mt-5 mx-auto md:mx-0">
              The shift to API-driven banking, mobile-first apps, and third-party fintech integrations has unlocked massive potential-and unprecedented risks. Capture The Bug helps financial institutions proactively detect and fix vulnerabilities <strong>before attackers exploit them</strong>-from fraud routes to zero-day flaws. Our intelligent pentesting platform is tailored for <strong>fintechs</strong>, <strong>neobanks</strong>, <strong>payment services</strong>, and <strong>legacy banks alike</strong>.
            </p>
          </div>

          {/* Main feature cards - 2x2 grid layout */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
           
             <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-2 sm:pr-4 py-6 rounded-md shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="w-10 h-10 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4">
                <Brain className="w-5 h-5 text-white" />
              </div>
             <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
  Expert-Led Manual Security Testing
</h3>
<p className="text-[#6B7280] text-base sm:text-lg leading-relaxed">
  Certified red teamers and threat hunters perform <span className="font-bold">Manual penetration testing</span> to simulate real-world cyberattacks across:
  <br className="hidden sm:block" /><span className="block sm:inline">• Financial APIs</span>
  <br className="hidden sm:block" /><span className="block sm:inline">• Authentication logic</span>
  <br className="hidden sm:block" /><span className="block sm:inline">• Transactional backends</span>
  <br className="hidden sm:block" /><span className="block sm:inline">• Cloud-native infrastructure</span>
  <br className="mt-2 sm:mt-0" />
  <span className="font-bold">
Uncover business logic flaws, broken access controls, and zero-day risks.
  </span>
</p>

            </div>
             <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-2 sm:pr-4 py-6 rounded-md shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="w-10 h-10 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4">
                <Lock className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
                Seamless Compliance, Simplified
              </h3>
              <p className="text-[#6B7280] text-base sm:text-lg leading-relaxed">
                Align security efforts with regulatory mandates like:
                <br className="hidden sm:block" /><span className="block sm:inline">• GDPR</span>
                <br className="hidden sm:block" /><span className="block sm:inline">• PCI-DSS</span>
                <br className="hidden sm:block" /><span className="block sm:inline">• RBI cybersecurity frameworks</span>
                <br className="mt-2 sm:mt-0" />
                <span className="font-bold">
                  Our reports and audits are tailored for audit-readiness, policy enforcement, and audit-ready penetration testing.
                </span>
              </p>
            </div>

          
          </div>

          {/* Secondary feature cards - 1x2 grid layout */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Card 3 - Real-Time Fraud & Risk Prioritization */}
            <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-2 sm:pr-4 py-6 rounded-md shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="w-10 h-10 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4">
                <AlertTriangle className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-lg sm:text-xl font-bold text-[#010D2C] mb-2 sm:mb-3">
                Real-Time Fraud & Risk Prioritization
              </h3>
              <p className="text-[#6B7280] text-sm sm:text-lg leading-relaxed">
                Our intelligent pentesting platform prioritizes security gaps based on financial impact-not just severity.
                From injection flaws to mobile takeover routes, we prioritize and remediate based on <span className="font-bold">fraud risk and revenue exposure</span>-not just severity scores.
              </p>
            </div>
             {/* Card 1 - End-to-End Financial Security */}
            <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-2 sm:pr-4 py-6 rounded-md shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="w-10 h-10 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4">
                <Shield className="w-5 h-5 text-white" />
              </div>
       <h3 className="text-lg sm:text-xl font-bold text-[#010D2C] mb-2 sm:mb-3">
  End-to-End Financial Security
</h3>

<p className="text-[#6B7280] text-sm sm:text-lg leading-relaxed">
  From core banking infrastructure and mobile wallets to KYC onboarding flows and third-party integrations-we test every endpoint in your digital finance ecosystem to ensure <span className="font-bold">endpoint protection for fintechs</span> and deliver comprehensive <span className="font-bold">core banking security testing</span> across development, deployment, and operations.
</p>


            </div>

           
          </div>
        </div>
      </div>
    </>
  );
}