'use client'
import Image from "next/image";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

const logos = [
  { src: '/images/EROAD_Logo.webp' , alt: "EROAD Logo"},
  { src: '/images/blackpearl_logo.png' , alt: "BlackPearl Logo"},
  { src: '/images/lawvu.jpg', alt: "lawvu Logo" },
  { src: '/images/parkable_logo.png', alt: "Parkable Logo" },
  { src: '/images/Cotiss_Logo.svg', alt: "Cotiss Logo" },
  { src: '/images/paysauce_logo.png' , alt: "PaySauce Logo"},
  { src: '/images/aplyid_logo.jpeg' , alt: "Apylid Logo"},
  { src: '/images/datamasque_logo.jpeg' , alt: "Datamasque Logo"},
  { src: '/images/whiparound_logo.png' , alt: "Whip Around Logo"},
  { src: '/images/devoli_logo.jpeg' , alt: "Devoli Logo"},
  { src: '/images/kademi_logo.png', alt: "Kademi Logo" },
  { src: '/images/rafay_logo.png', alt: "Rafay Logo" },
  { src: '/images/partly_logo.png', alt: "Partly Logo" },
  { src: '/images/yabble_logo.png', alt: "Yabble Logo" },
  { src: '/images/orbit_logo.png', alt: "orbit remit Logo" },
  { src: '/images/forsite_logo.png', alt: "forsite Logo" },
  { src: '/images/cronberry_logo.png', alt: "cronberry Logo" },
  { src: '/images/Bonnet_logo.jpeg', alt: "Bonnet Logo" },
  { src: '/images/Zebpay_logo.png', alt: "Zebpay Logo" },
  { src: '/images/immerseme_logo.jpeg' , alt: "Immerseme Logo"},
];


const settings = {
  dots: false,
  infinite: true,
  slidesToShow: 5,
  slidesToScroll: 1,
  autoplay: true,
  speed: 5000,
  autoplaySpeed: 0,
  cssEase: "linear",
  pauseOnHover: true,
  arrows: false,
  rtl: false,
  responsive: [
    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 4,
      }
    },
    {
      breakpoint: 600,
      settings: {
        slidesToShow: 3,
      }
    },
    {
      breakpoint: 480,
      settings: {
        slidesToShow: 2,
      }
    }
  ]
};

export default function PartnersList() {
  return (
    <div className="w-full bg-[#fbf7ff] py-8">
      <div className="container mx-auto flex flex-col items-center">
        <h2 className="text-xl text-center md:text-2xl py-6 font-[400] mb-10">
        Trusted by modern teams-from funded startups to listed enterprises 
        </h2>
      </div>
      <div className="w-full mb-10">
        <Slider {...settings}>
          {logos.map((logo, index) => (
            <div key={index} className="flex items-center justify-center">
              <div className="relative w-32 h-16">
                <Image
                  src={logo.src}
                  alt={logo.alt}
                  fill
                  style={{ objectFit: 'contain' }}
                  quality={80}
                  loading="eager"
                />
              </div>
            </div>
          ))}
        </Slider>
      </div>
    </div>
  );
}
