"use client";
import React from 'react';
import { motion } from 'framer-motion';
import {
  Shield,
  TrendingUp,
  Clock,
  ArrowRight,
  AlertTriangle,
  Target,
  Award
} from 'lucide-react';
import { getLocationData } from '../data/locationData';
import Button from '@/app/common/buttons/Button';
import { designSystem, getSectionWrapper, getCardStyles, getIconStyles, getGridStyles } from '../styles/designSystem';
import Image from 'next/image';

/**
 * Case Studies Component
 * Displays location-specific case studies and success stories with proper SEO structure
 */
const CaseStudies = ({ country }) => {
  const locationData = getLocationData(country);

  // Location-specific case studies - ONE per country for consistency
  const caseStudies = {
    nz: [
      {
        id: 1,
        industry: "Health & Safety Management Software",
        companySize: "50-200 employees",
        title: "Leading NZ Health & Safety Platform Achieves Zero Security Incidents",
        challenge: "A leading NZ health and safety platform needed comprehensive security testing to protect critical compliance data and maintain customer trust during rapid user expansion.",
        solution: "Comprehensive vulnerability assessment and penetration testing across web applications, APIs, and cloud infrastructure, with focus on contractor management and safety reporting systems.",
        results: [
          "Remediated 18 critical vulnerabilities",
          "100% NZ Privacy Act 2020 compliance",
          "Zero security incidents post-remediation",
          "Supported 300% user growth securely"
        ],
        Duration: "4 weeks",
        vulnerabilities: 18,
        riskReduction: "96%",
        compliance: "NZ Privacy Act 2020",
        icon: <Shield className="w-8 h-8 text-primary-blue" />,
        color: "from-primary-blue/10 to-secondary-blue/5",
        badge: "Health & Safety"
      }
    ],
    au: [
      {
        id: 1,
        industry: "B2B SaaS",
        companySize: "100k+ users",
        title: "Australia Success Story: SaaS API Security for Enterprise Buyers",
        challenge: "B2B SaaS Provider needed to secure its APIs and core app infrastructure ahead of a SOC 2 audit and a major enterprise procurement process.",
        solution: "REST & GraphQL API pentesting, privilege escalation and role-abuse simulation, CI/CD and network infrastructure review, executive-ready reporting + remediation roadmap.",
        results: [
          "Passed SOC 2 Phase 1",
          "Signed 6-figure enterprise deal",
          "95% API risk reduction",
          "24 vulnerabilities found and remediated"
        ],
        timeline: "3 weeks",
        vulnerabilities: 24,
        riskReduction: "95%",
        compliance: "SOC 2",
        icon: <TrendingUp className="w-8 h-8 text-primary-blue" />,
        color: "from-primary-blue/10 to-secondary-blue/5",
        badge: "SaaS",
        region: "Sydney, Melbourne"
      }
    ],
    us: [
      {
        id: 1,
        industry: "Healthcare",
        companySize: "2000+ employees",
        title: "Hospital Network Prevents Data Breach",
        challenge: "A major US hospital network required comprehensive security testing to protect patient data and maintain HIPAA compliance across multiple facilities.",
        solution: "Multi-site penetration testing including medical device security, network segmentation assessment, and HIPAA compliance validation.",
        results: [
          "Protected 100,000+ patient records",
          "Prevented potential $5M+ breach costs",
          "Maintained HIPAA compliance",
          "Enhanced patient trust and safety"
        ],
        timeline: "6 weeks",
        vulnerabilities: 42,
        riskReduction: "96%",
        compliance: "HIPAA",
        icon: <Shield className="w-8 h-8 text-primary-blue" />,
        color: "from-primary-blue/10 to-secondary-blue/5",
        badge: "Healthcare"
      }
    ]
  };

  const studies = caseStudies[country] || caseStudies.nz;

  return (
    <section className={getSectionWrapper('gray')}>
      <div className={designSystem.section.maxWidth}>
        {/* Section Header */}
        <motion.div
          {...designSystem.animation.fadeInUp}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h2 className={designSystem.typography.sectionTitle}>
            {locationData.country} Success Stories
          </h2>
          <p className={designSystem.typography.sectionSubtitle}>
            Discover how we&apos;ve helped leading {locationData.country} organizations
            strengthen their cybersecurity posture and achieve compliance goals.
          </p>
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-[2fr_3fr] gap-10 lg:gap-16 items-stretch">
          {/* Left Column - Main Case Study */}
          <div className="relative flex flex-col justify-center h-full">
            <div className="bg-gradient-to-br from-secondary-blue to-primary-blue rounded-3xl p-8 lg:p-12 text-white shadow-2xl relative overflow-hidden min-h-[500px] flex flex-col justify-center">
              <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
              <div className="relative z-10">
                <div className="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium mb-8">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                  <span>{locationData.country} Success Story</span>
                </div>
                <h2 className="text-4xl lg:text-4xl font-bold mb-8 leading-tight">
                  {studies[0]?.title || `${locationData.country} Security Success`}
                </h2>
                <p className="text-lg lg:text-lg leading-relaxed opacity-95 mb-8">
                  {studies[0]?.challenge || `Discover how we've helped leading ${locationData.country} organizations strengthen their cybersecurity posture and achieve compliance goals.`}
                </p>

                {/* Key Results */}
                {studies[0] && (
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold mb-4">Key Results:</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                        <div className="text-2xl font-bold text-green-400">{studies[0].vulnerabilities}</div>
                        <div className="text-sm opacity-90">Vulnerabilities Fixed</div>
                      </div>
                      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                        <div className="text-2xl font-bold text-green-400">{studies[0].riskReduction}</div>
                        <div className="text-sm opacity-90">Risk Reduction</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Column - Case Study Details */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {/* Challenge Card */}
            <div className="bg-white rounded-2xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 group border border-orange-500/20">
              <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <AlertTriangle className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="text-xl lg:text-2xl font-bold text-gray-900 mb-4">
                Challenge
              </h3>
              <p className="text-gray-600 leading-relaxed text-sm lg:text-base">
                {studies[0]?.challenge || "Complex security challenges requiring comprehensive testing and compliance validation."}
              </p>
            </div>

            {/* Solution Card */}
            <div className="bg-white rounded-2xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 group border border-primary-blue/20">
              <div className="w-12 h-12 bg-primary-blue/10 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Target className="w-8 h-8 text-primary-blue" />
              </div>
              <h3 className="text-xl lg:text-2xl font-bold text-gray-900 mb-4">
                Solution
              </h3>
              <p className="text-gray-600 leading-relaxed text-sm lg:text-base">
                {studies[0]?.solution || "Comprehensive vulnerability assessment and penetration testing across all digital touchpoints."}
              </p>
            </div>

            {/* Timeline Card */}
            <div className="bg-white rounded-2xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 group border border-purple-500/20">
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Clock className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-xl lg:text-2xl font-bold text-gray-900 mb-4">
              Pentest Duration
              </h3>
              <p className="text-gray-600 leading-relaxed text-sm lg:text-base">
                Project completed in {studies[0]?.timeline || "4 weeks"} with comprehensive testing and detailed reporting.
              </p>
            </div>

            {/* Compliance Card */}
            <div className="bg-white rounded-2xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 group border border-emerald-500/20">
              <div className="w-12 h-12 bg-emerald-100 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Award className="w-8 h-8 text-emerald-600" />
              </div>
              <h3 className="text-xl lg:text-2xl font-bold text-gray-900 mb-4">
                Compliance
              </h3>
              <p className="text-gray-600 leading-relaxed text-sm lg:text-base">
                Achieved {studies[0]?.compliance || "full compliance"} with industry standards and regulatory requirements.
              </p>
            </div>
          </div>
        </div>

        {/* Professional Bottom CTA */}
        <motion.div
          {...designSystem.animation.fadeInUp}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mt-24"
        >
          <div className="bg-white/80 backdrop-blur-sm border border-primary-blue/20 rounded-2xl p-6 sm:p-8 md:p-12 max-w-4xl mx-auto shadow-lg">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Ready to Build Your Own Security Success Story?
            </h3>
            <p className="text-xl text-gray-700 mb-8 max-w-2xl mx-auto leading-relaxed">
              Partner with us to strengthen your cybersecurity posture and meet compliance goals - just like leading {locationData.country} tech and enterprise teams have.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                href="/Request-Demo"
                variant="primary"
                size="lg"
                rightIcon={<ArrowRight className="w-5 h-5" />}
              >
                Start Your Success Story
              </Button>
              <Button
                href="/Company/Contact-Us"
                variant="secondary"
                size="lg"
              >
                Discuss Your Needs
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Compliance CTA Section */}
        <motion.div
          {...designSystem.animation.fadeInUp}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="mt-20"
        >
          <div className="bg-white/60 backdrop-blur-sm border border-primary-blue/20 rounded-2xl p-8 md:p-12 shadow-lg">
            <div className="text-center mb-8">
              <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                Get ISO 27001, SOC, GDPR, PCI DSS, HIPAA compliance-ready without the hassle
              </h3>
              <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
                Our security engine covers all the essential tests required for you to achieve ISO 27001, SOC 2, GDPR, PCI DSS, and HIPAA compliance. Secure your systems thoroughly and ensure every loophole is covered with our comprehensive testing.
              </p>
            </div>

            {/* Professional Certifications */}
            <div className="flex justify-center items-center gap-6 mb-8 flex-wrap">
              {[
                {
                  name: "ISO 27001",
                  logo: "/images/certifications/iso.png",
                  alt: "ISO 27001 Information Security Management System"
                },
                {
                  name: "SOC",
                  logo: "/images/certifications/soc.png",
                  alt: "SOC 2 Service Organization Control"
                },
                {
                  name: "GDPR",
                  logo: "/images/certifications/gdpr.png",
                  alt: "GDPR General Data Protection Regulation"
                },
                {
                  name: "PCI DSS",
                  logo: "/images/certifications/pcidss.png",
                  alt: "PCI DSS Payment Card Industry Data Security Standard"
                }
              ].map((cert, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  className="flex items-center justify-center w-20 h-20 bg-white rounded-full shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300 group"
                >
                  <Image
                    src={cert.logo}
                    alt={cert.alt}
                    width={48}
                    height={48}
                    className="object-contain group-hover:scale-105 transition-transform duration-300"
                  />
                </motion.div>
              ))}
            </div>

            {/* CTA Button */}
            <div className="text-center">
              <Button
                href="/Request-Demo"
                variant="primary"
                size="lg"
                rightIcon={<ArrowRight className="w-4 h-4 sm:w-5 sm:h-5" />}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 sm:px-8 py-3 sm:py-4 rounded-lg font-semibold text-sm sm:text-lg"
              >
                <span className="hidden sm:inline">Schedule a discovery call</span>
                <span className="sm:hidden">Schedule Discovery</span>
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default CaseStudies;
