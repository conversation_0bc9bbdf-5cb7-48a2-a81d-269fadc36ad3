{"name": "ctb-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@azure/communication-email": "^1.0.0", "@heroicons/react": "^2.1.5", "@intercom/messenger-js-sdk": "^0.0.11", "@lottiefiles/dotlottie-react": "^0.14.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "framer-motion": "^12.17.3", "lucide-react": "^0.512.0", "motion": "^12.16.0", "next": "^15.3.3", "prop-types": "^15.8.1", "react": "^19.1.0", "react-confetti": "^6.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-modal": "^3.16.1", "react-router-dom": "^6.26.0", "react-slick": "^0.30.2", "sharp": "^0.33.4", "slick-carousel": "^1.8.1", "tailwind-merge": "^3.3.0"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.2.5", "next-sitemap": "^4.2.3", "postcss": "^8", "tailwindcss": "^3.4.1"}}