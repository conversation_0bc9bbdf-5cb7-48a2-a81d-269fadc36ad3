"use client";

import React from 'react';

export default function Passion() {
  const values = [
    {
      title: "Growth Mindset",
      description: "The threat landscape changes daily - and so do we. Our growth mindset drives constant iteration, learning, and platform upgrades. It's how we stay ahead of attackers and ahead of the curve.",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="#062575" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
        </svg>
      ),
      iconBg: "bg-ctb-blue-0"
    },
    {
      title: "Determination",
      description: "We don't shy away from complexity. Whether it's testing hardened environments or supporting critical fixes, we show up with grit and follow-through - delivering real security outcomes, not just reports.",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="#0835A7" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      iconBg: "bg-ctb-blue-0"
    },
    {
      title: "Empathy",
      description: "Security can be frustrating. We get it. That's why we build tools that work with developers, not against them - transforming friction into flow and blockers into solutions.",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="#027bfb" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
      ),
      iconBg: "bg-ctb-blue-0"
    },
    {
      title: "Integrity",
      description: "Trust is earned - and protected. We hold ourselves to the highest ethical, technical, and privacy standards, ensuring every pentest, report, and remediation recommendation is honest, verified, and defensible.",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="#011B70" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
      ),
      iconBg: "bg-ctb-blue-0"
    }
  ];

  return (
    <section className="w-full py-16 lg:py-24 bg-tertiary-blue relative overflow-hidden">
      {/* Decorative blurred circles */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-10 left-10 w-72 h-72 bg-secondary-blue/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 right-10 w-96 h-96 bg-primary-blue/10 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* MODIFIED: Changed grid definition to a 2fr 3fr ratio */}
        <div className="grid grid-cols-1 lg:grid-cols-[2fr_3fr] gap-10 lg:gap-16 items-stretch">
          {/* Left Column - REMOVED lg:col-span-1 */}
          <div className="relative flex flex-col justify-center h-full">
            <div className="bg-gradient-to-br from-secondary-blue to-primary-blue rounded-3xl p-8 lg:p-12 text-white shadow-2xl relative overflow-hidden min-h-[500px] flex flex-col justify-center">
              <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
              <div className="relative z-10">
                <div className="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium mb-8">
                  <div className="w-2 h-2 bg-ctb-green-50 rounded-full mr-3 animate-pulse"></div>
                  <span>Our Foundation</span>
                </div>
                <h2 className="text-4xl lg:text-4xl font-bold mb-8 leading-tight">
                  Passion For Security
                </h2>
                <p className="text-lg lg:text-lg leading-relaxed opacity-95">
                  At Capture The Bug, cybersecurity isn&apos;t just our focus - it&apos;s our foundation. We design every feature, workflow, and decision around protecting what matters most: your data, your users, and your momentum. Security isn&apos;t a checkbox. It&apos;s a commitment.
                </p>
              </div>
            </div>
          </div>
          {/* Right Column - REMOVED lg:col-span-2 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {values.map((value, index) => (
              <div
                key={index}
                className="bg-white rounded-2xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 group border border-ctb-blue-150/10"
              >
                <div className={`w-12 h-12 ${value.iconBg} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  {value.icon}
                </div>
                <h3 className="text-xl lg:text-2xl font-bold text-ctb-blue-350 mb-4">
                  {value.title}
                </h3>
                <p className="text-ctb-blue-150 leading-relaxed text-sm lg:text-base">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
