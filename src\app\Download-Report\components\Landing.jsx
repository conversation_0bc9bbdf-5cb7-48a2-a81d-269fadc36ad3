"use client";
import React, { useState } from "react";
import Image from "next/image";
import Button from "@/app/common/buttons/Button";

const companyEmailRegex = /^[^@\s]+@(?!(gmail|yahoo|hotmail|outlook|aol|icloud|protonmail|zoho|mail|gmx|yandex)\.)[^@\s]+\.[^@\s]+$/i;

const Landing = () => {
  const [formData, setFormData] = useState({
    businessEmail: "",
    firstName: "",
    lastName: "",
    jobTitle: "",
    company: "",
    agreeToTerms: false,
  });
  const [errors, setErrors] = useState({});
  const [success, setSuccess] = useState("");
  const [apiError, setApiError] = useState("");
  const [canDownload, setCanDownload] = useState(false);
  const [token, setToken] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrors({});
    setApiError("");
    setSuccess("");
    setCanDownload(false);
    setToken("");
    setIsLoading(true);
   
    try {
      // Make the API request
      const response = await fetch("/api/submissions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...formData,
          formType: 'download-report',
        }),
      });
     
      const result = await response.json();
      if (response.ok && result.success) {
        // Clear form fields on success
        setFormData({
          businessEmail: "",
          firstName: "",
          lastName: "",
          jobTitle: "",
          company: "",
          agreeToTerms: false,
        });
       
        setSuccess("Thank you! Your report is ready to download.");
        setCanDownload(true);
 
        // Create a temporary link element
        const link = document.createElement('a');
        link.href = '/ctb-report-2025.pdf'; // Direct path to PDF in public folder
        link.download = 'State-of-Offensive-Security-Report-2025.pdf'; // Name for the downloaded file
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        setApiError(result.error || "Something went wrong. Please try again.");
      }
    } catch (err) {
      setApiError("Network error. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className=" min-h-[calc(100vh-64px)] bg-gradient-to-br from-slate-50 to-blue-50 overflow-x-hidden">
      
       <div className="container mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-0 py-16 sm:py-24 lg:py-26 max-w-7xl">
        <div className="grid lg:grid-cols-2 gap-6 md:gap-8 lg:gap-12 xl:gap-20 items-start">
          
          {/* Left Column - Content */}
          <div className="space-y-8 lg:space-y-10 lg:sticky lg:top-24 max-w-[800px] mx-auto lg:mx-0">
            <div className="space-y-6 max-w-lg">
              <h1 className="text-3xl sm:text-4xl lg:text-4xl font-[600]" style={{ color: '#062575' }}>
                Download the State of<br />
                Offensive Security Report 2025
              </h1>
              <p className="text-base md:text-md text-black">
                What you don&apos;t know can hurt you. In this data-rich report, Capture The Bug uncovers how even the most mature security teams are still vulnerable-due to persistent flaws, delayed remediation, and the rapid evolution of AI threats.
              </p>
              <div className="pt-2">
                <h2 className="text-2xl font-bold mb-4" style={{ color: '#062575' }}>This year&apos;s report you&apos;ll learn:</h2>
                <ul className="space-y-4">
                  <li className="flex items-start space-x-3">
                    <span className="mt-1 flex-shrink-0" style={{ color: '#60a5fa' }}>
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </span>
                    <span className="text-md text-black"><span className="font-bold">Why 94% of web apps still suffer from broken access controls,</span> and how attackers are exploiting them every day-not with 0-days, but with unpatched, known flaws.</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <span className="mt-1 flex-shrink-0" style={{ color: '#60a5fa' }}>
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </span>
                    <span className="text-md text-black"><span className="font-bold">How long it really takes to fix critical vulnerabilities</span> (hint: the median is 60+ days), and why nearly half remain unpatched a year later.</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <span className="mt-1 flex-shrink-0" style={{ color: '#60a5fa' }}>
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </span>
                    <span className="text-md text-black"><span className="font-bold">Why AI is now both a weapon and a weakness.</span> Discover how attackers are using generative AI to launch more convincing phishing attacks and how security teams are missing coverage for AI systems.</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <span className="mt-1 flex-shrink-0" style={{ color: '#60a5fa' }}>
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </span>
                    <span className="text-md text-black"><span className="font-bold">The new gold standard for modern security teams-</span>including continuous testing, shift-left culture, and AI-aware pentesting practices that help you stay ahead of threats.</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Right Column - Form */}
          <div className="w-full max-w-full lg:max-w-[700px] mx-auto">
            <div className="bg-white rounded-2xl shadow-xl p-4 sm:p-6 lg:p-8">
              {/* Report Cover Image */}
              <div className="mb-6 text-center">
                <div className="relative w-48 h-64 mx-auto">
                  <Image
                    src="/images/download-report-cover.jpeg"
                    alt="State of Pentesting Report 2025"
                    fill
                    className="object-contain"
                  />
                </div>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4 md:space-y-6">
                {/* Business Email */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Business Email *
                  </label>
                  <input
                    type="email"
                    name="businessEmail"
                    value={formData.businessEmail}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all"
                    placeholder="<EMAIL>"
                    required
                    disabled={isLoading}
                  />
                </div>

                {/* Name Fields */}
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      First Name *
                    </label>
                    <input
                      type="text"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all"
                      placeholder="First Name"
                      required
                      disabled={isLoading}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all"
                      placeholder="Last Name"
                      required
                      disabled={isLoading}
                    />
                  </div>
                </div>

                {/* Job Title */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Job Title *
                  </label>
                  <input
                    type="text"
                    name="jobTitle"
                    value={formData.jobTitle}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all"
                    placeholder="Job Title"
                    required
                    disabled={isLoading}
                  />
                </div>

                {/* Company */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Company *
                  </label>
                  <input
                    type="text"
                    name="company"
                    value={formData.company}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all"
                    placeholder="Company Name"
                    required
                    disabled={isLoading}
                  />
                </div>

                {/* Privacy Notice */}
                <div className="text-xs text-slate-500">
                  <p>
                    Capture The Bug needs the contact information you provide to us to contact you about our products and services. 
                    You may unsubscribe from these communications at anytime. For information on how to unsubscribe, as well as our privacy practices and commitment to 
                    protecting your privacy, check out our{" "}
                    <a href="https://capturethebug.xyz/Useful-Links/Privacy-Policy" className="text-[#027bfc] hover:underline">Privacy Policy</a>.
                  </p>
                </div>

                {/* Success/Error Messages */}
                {success && <div className="text-green-600 text-sm font-semibold">{success}</div>}
                {apiError && <div className="text-red-600 text-sm font-semibold">{apiError}</div>}

                {/* Download Button */}
                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  fullWidth
                  className="w-full"
                  disabled={isLoading}
                >
                  {isLoading ? "Downloading..." : "DOWNLOAD THE REPORT"}
                </Button>
                {canDownload && token && (
                  <Button
                    href={`/api/download-report?token=${token}`}
                    variant="success"
                    size="lg"
                    fullWidth
                    className="w-full mt-4"
                  >
                    Click here to download your PDF
                  </Button>
                )}
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Landing;