import React from "react";
import PartnersList from "@/app/Product/Penetration-Testing/components/PartnersList";
import BlogSection from "@/app/Home/components/Blogs";
import Landing from "./components/Landing";
import Security from "./components/Security";
import Testimonial from "../Testimonial";

export const metadata = {
  title: "Capture The Bug | Legal Tech Security",
  description:
    "Safeguard your legal operations platform, case data, and confidential communications. Capture The Bug provides privacy-first penetration testing tailored for LegalTech.",
  keywords:
    "legaltech penetration testing, law firm cybersecurity, privileged communication protection, legal document security, legal platform data breach, privacy compliance security, legal case data protection, Capture The Bug",
  robots: "index, follow",
  icons: {
    icon: "/favicon.ico",
  },
  openGraph: {
    title: "Capture The Bug | Legal Tech Security",
    type: "website",
    url: "https://capturethebug.xyz/Industries/Legal&Compliance",
    description:
      "Capture The Bug helps legal tech providers prevent data breaches, secure privileged communications, and meet international privacy standards through specialized offensive security testing.",
    images: "https://ibb.co/TDFFn2jf", 
  },
  twitter: {
    card: "summary_large_image",
    title: "Capture The Bug | Legal Tech Security",
    description:
      "Protect your legal tech platforms and sensitive data from cyber threats. Capture The Bug delivers expert-led security assessments for the legal industry.",
    images: "https://ibb.co/TDFFn2jf",
  },
};


const testimonial = {
  company: "LawVu",
  logo: "/images/lawvu.jpg",
  quote: "Capture The Bug's continuous pentesting approach has been a game-changer for us at LawVu. By integrating their solution, we've significantly reduced the time our development team spends on security tasks, leading to both time and cost savings. Their platform's real-time insights and seamless integration into our workflow have enhanced our security posture without disrupting our development cycles.",
  author: "Sarah Webb",
  position: "Chief Operating Officer "
};


export default function Legal() {
  return (
    <>
    <Landing/>
   <Security/>
   <PartnersList/>
   <Testimonial
   company={testimonial.company}
   logo={testimonial.logo}
   quote={testimonial.quote}
   author={testimonial.author}
   position={testimonial.position}
   logoSize={{ width: 140, height: 100 }}
   logoStyle={{ marginLeft: 0 }}
      />
     <BlogSection/> 
</>
  );
}