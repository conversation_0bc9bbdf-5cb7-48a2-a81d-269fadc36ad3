import DarkButton from "@/app/common/buttons/DarkButton";
import Image from "next/image";
import React from "react";

export default function Case1() {
  return (
    <div className="md:p-12 p-8 flex md:flex-row flex-col justify-between items-start bg-gray-50">
      <div className="Image md:w-[35%] w-full md:pr-10 md:mt-0 mt-10 mb-6 md:mb-0">
        <Image
          src="/images/Case 1-img.png"
          width={750}
          height={550}
          alt="Cybersecurity case study results showing successful penetration testing implementation and vulnerability remediation for enterprise client"
        />
      </div>

      <div className="Content w-full flex flex-col gap-6 md:gap-8 md:w-[65%] md:pl-10">
        <div className="Title text-base md:text-xl lg:text-2xl text-black py-2 flex flex-col md:flex-row md:items-center md:gap-2">
          <div className="font-bold">Industry:</div>
          <span className="text-base md:text-lg lg:text-xl">
            Health and Safety Management Software
          </span>
        </div>

        <div className="Title text-base md:text-xl lg:text-2xl text-black py-2 flex flex-col md:flex-row md:items-center md:gap-2">
          <div className="font-bold">Services Provided:</div>
          <span className="text-base md:text-lg lg:text-xl">
            Vulnerability Assessment & Penetration Testing
          </span>
        </div>

        <div className="description leading-8 md:pr-10 text-slate-800 md:text-xl md:text-justify text-sm">
          Our client is a leading provider of health and safety management
          solutions in New Zealand, offering a platform that streamlines site
          operations, contractor compliance, risk management, and safety
          reporting. <br />
          As their user base expanded, ensuring the security and scalability of
          their platform infrastructure became a critical focus for maintaining
          data integrity and compliance.
        </div>

        <div className="button flex gap-2 mt-4">
          <a
            href="/Health and Safety Management Software Case Study.pdf"
            download="Health and Safety Management Software Case Study.pdf"
          >
            <DarkButton className="px-8 py-4 text-sm md:text-lg">
              Download Success Story
            </DarkButton>
          </a>
        </div>
      </div>
    </div>
  );
}
