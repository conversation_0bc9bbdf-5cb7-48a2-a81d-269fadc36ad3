
'use client';
import { motion } from 'framer-motion';
import React, { useState, useEffect } from "react";
import Webinar from "./Webinar";
import Podcast from "./Podcast";

const AnimatedGridHero = () => {
  const [isClient, setIsClient] = useState(false);
  const [gridData, setGridData] = useState([]);

  useEffect(() => {
    setIsClient(true);
    
    // Generate grid data once on client side
    const generateGridData = () => {
      return Array.from({ length: 64 }, (_, i) => {
        const isColored = Math.random() > 0.7;
        const blinkSpeed = Math.random();
        let blinkClass = '';
        
        if (isColored) {
          if (blinkSpeed < 0.33) blinkClass = 'blink-fast';
          else if (blinkSpeed < 0.66) blinkClass = 'blink-medium';
          else blinkClass = 'blink-slow';
        }
        
        return {
          id: i,
          isColored,
          blinkClass,
          animationDelay: Math.random() * 6
        };
      });
    };

    setGridData({
      topRight: generateGridData(),
      bottomLeft: generateGridData().map(item => ({
        ...item,
        animationDelay: Math.random() * 8
      }))
    });
  }, []);

  // Render a placeholder during SSR
  if (!isClient) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <div className="min-h-screen w-full relative overflow-hidden bg-gray-50 flex flex-col items-center justify-center">
            <div className="min-h-screen mt-1 sm:mt-2 w-full relative overflow-hidden bg-gray-50 flex flex-col items-center justify-center rounded-lg sm:rounded-2xl lg:rounded-3xl">
              <div className="relative z-20 text-center leading-relaxed max-w-4xl mx-auto px-3 sm:px-4">
                <h1 className="text-lg sm:text-xl md:text-3xl lg:text-5xl font-semibold mb-3 sm:mb-4 text-center text-gray-600 leading-tight">
                  Explore Our Latest{' '}
                  <span className="text-xl sm:text-2xl md:text-4xl lg:text-5xl font-semibold text-blue-600 text-center block sm:inline">
                    News, <br className="sm:hidden" /> Insights, and Webinars
                  </span>
                </h1>
              </div>
            </div>
            <div className="w-full">
              <Webinar />
            </div>
            <div className="w-full">
              <Podcast />
            </div>
          </div>
        </motion.div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <div className="min-h-screen w-full relative overflow-hidden bg-gray-50 flex flex-col items-center justify-center">
          <style jsx>{`
            @keyframes colorBlink {
              0%, 70% { 
                opacity: 0.3;
              }
              85% { 
                opacity: 0.8;
              }
              100% { 
                opacity: 0.3;
              }
            }
            
            @keyframes colorFade {
              0%, 60% { 
                opacity: 0.2;
              }
              80% { 
                opacity: 0.7;
              }
              100% { 
                opacity: 0.2;
              }
            }
            
            .blink-slow {
              animation: colorBlink 4s ease-in-out infinite;
            }
            
            .blink-medium {
              animation: colorBlink 3s ease-in-out infinite;
            }
            
            .blink-fast {
              animation: colorFade 2.5s ease-in-out infinite;
            }
          `}</style>

          <div className="min-h-screen mt-1 sm:mt-2 w-full relative overflow-hidden bg-gray-50 flex flex-col items-center justify-center rounded-lg sm:rounded-2xl lg:rounded-3xl">
            {/* Top right grid pattern - Hidden on mobile and tablet */}
            <div className="hidden lg:block absolute top-0 right-0 w-64 h-64 opacity-50">
              <div className="grid grid-cols-8 gap-2 w-full h-full">
                {gridData.topRight?.map((item) => (
                  <div 
                    key={item.id} 
                    className={`w-full h-full rounded-sm ${
                      item.isColored ? `bg-primary-blue ${item.blinkClass}` : 'bg-gray-200'
                    }`}
                    style={{
                      animationDelay: `${item.animationDelay}s`
                    }}
                  ></div>
                ))}
              </div>
            </div>

            {/* Bottom left grid pattern - Hidden on mobile and tablet */}
            <div className="hidden lg:block absolute bottom-0 left-0 w-64 h-64 opacity-50">
              <div className="grid grid-cols-8 gap-2 w-full h-full">
                {gridData.bottomLeft?.map((item) => (
                  <div 
                    key={item.id} 
                    className={`w-full h-full rounded-sm ${
                      item.isColored ? `bg-ctb-green-50 ${item.blinkClass}` : 'bg-gray-200'
                    }`}
                    style={{
                      animationDelay: `${item.animationDelay}s`
                    }}
                  ></div>
                ))}
              </div>
            </div> 
            
            <div className="relative z-20 text-center leading-relaxed max-w-4xl mx-auto px-3 sm:px-4">
              <h1 className="text-lg sm:text-xl md:text-3xl lg:text-5xl font-semibold mb-3 sm:mb-4 text-center text-gray-600 leading-tight">
                Explore Our Latest{' '}
                <span className="text-xl sm:text-2xl md:text-4xl lg:text-5xl font-semibold text-blue-600 text-center block sm:inline">
                  News, <br className="sm:hidden" /> Insights, and Webinars
                </span>
              </h1>
            </div>
          </div>

          <div className="w-full">
            <Webinar />
          </div>
          <div className="w-full">
            <Podcast />
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default AnimatedGridHero;