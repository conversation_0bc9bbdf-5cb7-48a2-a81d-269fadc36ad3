"use client"
import React from 'react';
import { ArrowRight } from 'lucide-react';
import Image from 'next/image';
 

export default function CustomerSection() {
  return (
    <div className="relative">
      {/* Background layers */}
      <div className="absolute inset-0 z-0 flex flex-col overflow-hidden">
        <div className="flex-1 bg-[#F8F4F3]"></div>
        <div className="flex-1 bg-[#58CC024D]"></div>
      </div>

      {/* Content container */}
      <div 
        className="py-6 w-full md:w-11/12 lg:w-3/4 mx-auto  rounded-xl relative bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url(/images/customer-bg2.png)",
          backgroundBlendMode: "overlay"
        }}
      >          
        <div className="grid grid-cols-1 lg:grid-cols-2 items-center min-h-[200px] relative z-10">
          <div className="p-6 sm:p-8 lg:p-10 flex flex-col justify-center">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-800 mb-4 sm:mb-6 leading-tight">
              Continuous Pentesting. 
              <br />
              <span className="text-gray-700">Real Security.</span>
            </h2>
            
   <a href="/Request-Demo">
      <button className="bg-blue-900 hover:bg-blue-800 text-white px-4 py-1.5 sm:px-5 sm:py-2 md:px-6 md:py-2 rounded-lg font-semibold transition-colors duration-200 inline-flex items-center gap-2 text-sm sm:text-base">
        Request a demo
        <ArrowRight className="w-4 h-4" />
      </button>
    </a>
  </div>

          <div className="relative  px-10 flex items-center justify-center">
            <div className="relative w-full max-w-md">
              <Image 
                src="/images/webinar2.png" 
                alt="Compliance dashboard illustration" 
                className="w-full h-auto rounded-3xl shadow-lg"
                width={400}
                height={300}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}