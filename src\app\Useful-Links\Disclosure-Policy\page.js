import UsefulLinksPage from '@/app/common/pages/UsefulLinksPage'
import React from 'react'

export const metadata = {
  openGraph: {
    title: "Capture The Bug | Disclosure Policy",
    type: "website",
    url: "https://capturethebug.xyz/Useful-Links/Disclosure-Policy",
    description:
      "Capture The Bug (“CTB”) believes that the coordinated, orderly, public disclosure of vulnerabilities is a crucial aspect of the vulnerability disclosure process.",
    images:  "https://i.postimg.cc/d09SNXmv/Disclosure-Policy.png"
  },
};


export default function page() {
  return (
    <div>
      <title>Capture The Bug | Disclosure Policy</title>
      <UsefulLinksPage title="Disclosure Policy">
        {/* <div className='font-bold'>Code of Ethics</div>        
        <div className='font-bold'>CTB-COE-V2.0</div>        
        <div className='font-bold'>Last update 16-03-2023</div>       */}


<div className='font-bold'>Disclosure Policy</div>
        <p className='text-slate-600'>Capture The Bug (“CTB”) believes that the coordinated, orderly, public disclosure of vulnerabilities is a crucial aspect of the vulnerability disclosure process. This policy applies to all submissions made through the CTB platform, including New, Triaged, Unresolved, Resolved, Duplicates, Out of Scope, Not Applicable, and Won’t Fix submissions. Program Owners and researchers are encouraged to work together to share information in a mutually agreed manner.</p>  

        <div className='font-bold'>Vulnerability Disclosure Policy for CTB</div>

        <p className='text-slate-600'>CTB believes that the coordinated, orderly, public disclosure of vulnerabilities is a crucial aspect of the vulnerability disclosure process. This policy applies to all submissions made through the CTB platform, including New, Triaged, Unresolved, Resolved, Duplicates, Out of Scope, Not Applicable, and Won’t Fix submissions. Program Owners and researchers are encouraged to work together to share information in a mutually agreed manner.</p> 

        <div className='font-bold'>Coordinated Disclosure</div>


        <p className='text-slate-600'>Coordinated Disclosure is the recommended policy for all new public programs and is optional for ongoing private bounty programs. In this model, Program Owners commit to allowing researchers to publish mutually agreed information about the vulnerability after it has been fixed. Program Owners require explicit permission to disclose in the submission record. This applies to all submissions for the program, regardless of validity or acceptance.</p> 
        <p className='text-slate-600'>In the principle of CTB’s Coordinated Disclosure, researchers can externally disclose limited or full disclosures approved by Program Owners. CTB’s Coordinated Disclosure allows Program Owners and Researchers to work through the disclosure process, during which all parties must agree on a date and the disclosure level (limited or full) for a vulnerability or exploit to be disclosed. Once the vulnerability or exploit is disclosed on CTB’s platform, the Researcher can disclose the vulnerability or exploit publicly as long as it adheres to the agreed type of disclosure – limited or full, and any other parameters agreed for the disclosure.</p> 
        <p className='text-slate-600'>When you disclose a submission publicly, your username will be shown on the CTB platform.</p> 



        <div className='font-bold'>Non-Disclosure</div>
        <p className='text-slate-600'>Non-Disclosure is the default policy for CTB’s Next Generation Penetration Testing. It is common in private bounty programs. In the absence of a Coordinated or Custom Disclosure policy, the expectation of the Researcher and the Program Owner is non-disclosure. This is documented in our Researcher terms and conditions and Code of Ethics.</p> 

        <p className='text-slate-600'>This means no submissions may be publicly disclosed at any time and is designated by the following text in the program bounty brief:</p> 
        <p className='text-slate-600'>“Disclosure – Please note: This program does not allow disclosure. You may not release information about vulnerabilities found in this program to the public.”</p> 


        <div className='font-bold'>Custom Disclosure</div>

        <p className='text-slate-600'>In some cases, CTB customers customize disclosure requirements in their Program Guide</p> 

        <div className='font-bold'>Program Disclosure</div>

        <p className='text-slate-600'>The existence or details of private programs must not be communicated to anyone who is not a CTB employee or an authorized employee of the organization responsible for the program.</p> 
        <p className='text-slate-600'>If there is a conflict between the disclosure terms listed on a Program’s brief and the CTB’s Researcher Terms and Conditions, the Program Brief supersedes the CTB’s terms. If you have any questions, send an <NAME_EMAIL></p> 


        <div className='font-bold'>Accidental Disclosure: Insecure POC video sharing</div>

<p className='text-slate-600'>It is recommended to include a video or screenshot as Proof-of-Concept in your submissions. These files should not be shared publicly. This includes uploading to any publicly accessible websites (e.g., YouTube, Imgur, etc.).</p> 








        
      
      </UsefulLinksPage>
    </div>
  )
}
