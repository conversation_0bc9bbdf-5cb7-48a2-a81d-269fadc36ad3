import WhiteButton from "@/app/common/buttons/WhiteButton";
import Link from "next/link";
import React from "react";

export default function AboutLanding() {
  return (
    <div>
      <div className="flex md:flex-row flex-col items-center bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] md:py-28 md:px-20 p-10 md:gap-60 gap-4">
        <div className="Title md:text-[50px] text-2xl font-bold text-white md:w-[90%] md:leading-[60px] md:ml-0 -ml-[100px]">
          About <br /> Capture The Bug
        </div>
        <div className="flex flex-col items-start">
          <div className="subTitle md:text-lg text-white">
            Helping high growth tech companies secure their assets confidently.
          </div>
          <div className="Button pt-6">
            <Link
              href="https://outlook.office.com/bookwithme/user/<EMAIL>?anonymous&ep=pcard"
              target="_blank"
              rel="noopener noreferrer" // Added for security
            >
              <WhiteButton className="rounded-md font-semibold px-8 py-2">
                Book a 30 min consultation
              </WhiteButton>
            </Link>
          </div>

          {/* Uncomment if needed */}
          {/* <div className="checkedTrusts flex flex-row items-center mt-6">
            <div className="eachTrust flex flex-row items-center">
              <TiTick color="white" size={30} />
              <div className="content text-sm">Manual Penetration Testing</div>
            </div>

            <div className="eachTrust flex flex-row items-center">
              <TiTick color="white" size={30} />
              <div className="content text-sm">
                Full time penetration testers
              </div>
            </div>

            <div className="eachTrust flex flex-row items-center">
              <TiTick color="white" size={30} />
              <div className="content text-sm">Remediation Support</div>
            </div>
          </div> */}
        </div>
      </div>
    </div>
  );
}
