import React from "react";

const Features = () => {
  const features = [
    {
      icon: (
        <svg className="w-8 h-8 text-secondary-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
      ),
      title: "Boost Security Coverage ",
      description: "Identify and fix vulnerabilities in real time with manual testing delivered through an always-on platform. No waiting. No static reports. Just continuous, actionable insight. "
    },
    {
      icon: (
        <svg className="w-8 h-8 text-secondary-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      ),
      title: "Empower Developer Workflows ",
      description: "Integrate security testing directly into your SDLC. Developers get clear, timely findings-so they can triage, retest, and resolve without leaving their workflow. "
    },
    {
      icon: (
        <svg className="w-8 h-8 text-secondary-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
        </svg>
      ),
      title: "Simplify IT and Risk Management ",
      description: "Capture The Bug connects with your existing tools to centralize visibility, track risk across assets, and eliminate back-and-forth with vendors or spreadsheets. "
    },
    {
      icon: (
        <svg className="w-8 h-8 text-secondary-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      title: "Strategic Visibility for Security Leaders  ",
      description: "CTOs and CISOs use Capture The Bug for audit-ready reports, real-time remediation metrics, and smart prioritization-aligned with compliance, risk, and product velocity."
    }
  ];

  return (
    <div className="py-20 md:py-24 lg:py-32 px-4 sm:px-6 md:px-8 lg:px-12 xl:px-28 bg-white">
      <div className="max-w-8xl mx-auto">
        {/* Header */}
        <div className="text-center mb-20 lg:mb-24">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-[600] leading-tight mb-6">
            <span className="text-tertiary-blue">Smarter Security Testing for </span>
            <span className="text-secondary-blue">Modern Digital Teams</span>
          </h2>
          <p className="text-[#3C3C3CCC] text-lg md:text-xl leading-relaxed max-w-5xl mx-auto">
            Continuous, collaborative, and built for speed-Capture The Bug&apos;s PTaaS platform helps security, engineering, and leadership teams work better together to reduce risk and move faster.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-10">
          {features.map((feature, index) => (
            <div key={index} 
              className="text-left bg-white rounded-xl p-8 border border-gray-200 shadow-md hover:shadow-xl hover:border-secondary-blue transition-all duration-300 hover:-translate-y-1 group"
            >
              {/* Icon */}
              <div className="mb-6 relative z-10">
                <div className="p-4 bg-secondary-blue/10 rounded-xl w-fit transform transition-transform duration-300 group-hover:scale-110 group-hover:bg-secondary-blue/20">
                  {feature.icon}
                </div>
              </div>
              
              {/* Content */}
              <div className="relative z-10">
                <h3 className="text-2xl font-[600] text-tertiary-blue mb-4 group-hover:text-secondary-blue transition-colors duration-300">
                  {feature.title}
                </h3>
                <p className="text-tertiary-blue/80 text-base leading-relaxed">
                  {feature.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Features;