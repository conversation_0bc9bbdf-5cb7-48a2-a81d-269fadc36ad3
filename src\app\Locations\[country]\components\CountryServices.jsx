'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, Shield, Server, Database, Code, Users, ArrowRight, Zap, Cloud, Smartphone, Plug, Globe } from 'lucide-react';
import { designSystem, getSectionWrapper, getCardStyles, getIconStyles, getGridStyles } from '../styles/designSystem';

const CountryServices = ({ country }) => {
  const countryData = {
    nz: {
      title: "Comprehensive Pentesting for NZ Businesses",
      description: "Our New Zealand-based team delivers end-to-end Vulnerability Assessment and Penetration Testing (VAPT) services - helping Kiwi businesses meet local compliance requirements and secure their digital assets across web, mobile, APIs, and network infrastructure.",
      services: [
        {
          icon: <Shield className="w-6 h-6 text-primary-blue" />,
          title: "NZ Privacy Act Compliance Testing",
          description: "Compliance-focused VAPT aligned with the NZ Privacy Act 2020. We help New Zealand organizations validate their technical readiness for privacy obligations through targeted testing and risk exposure reviews.",
          badge: "Compliance",
          color: "from-primary-blue/10 to-primary-blue/5",
          features: [
            "PII exposure and data flow testing",
            "Misconfigured storage or APIs",
            "Access control flaws affecting personal data",
            "Compliance gap reporting for NZ privacy audits"
          ]
        },
        {
          icon: <Server className="w-6 h-6 text-primary-blue" />,
          title: "Comprehensive VAPT",
          description: "Comprehensive testing for all digital touchpoints. Our VAPT methodology simulates real-world attack scenarios across every layer of your digital stack - identifying vulnerabilities before they become threats.",
          badge: "VAPT",
          color: "from-primary-blue/10 to-secondary-blue/5",
          features: [
            "OWASP Top 10 web & mobile testing",
            "API security aligned with OWASP API Top 10",
            "Network penetration testing (internal & external)",
            "Role escalation, injection flaws, access control gaps"
          ]
        },
        {
          icon: <Cloud className="w-6 h-6 text-primary-blue" />,
          title: "Cloud Security for Kiwi Businesses",
          description: "Secure your AWS, Azure, and hybrid cloud infrastructure. We test your cloud environment for misconfigurations, excessive permissions, and insecure deployment patterns - with NZ data sovereignty in mind.",
          badge: "Cloud",
          color: "from-primary-blue/10 to-secondary-blue/5",
          features: [
            "Cloud misconfiguration review",
            "IAM and role-based access testing",
            "Serverless and container service assessment",
            "Local data residency & compliance validation"
          ]
        }
      ]
    },
    au: {
      title: "Comprehensive Pentesting for Australian Businesses",
      description: "We provide end-to-end penetration testing services tailored for SaaS platforms, mobile-first products, API-driven applications, and network infrastructure - helping Australian tech companies achieve compliance, protect customers, and scale securely.",
      services: [
        {
          icon: <Cloud className="w-6 h-6 text-primary-blue" />,
          title: "SaaS Platform Security",
          description: "Compliance-aligned security testing for cloud-native Australian software products. We assess your SaaS architecture to identify misconfigurations, access flaws, and multi-tenant exposures - aligned with ISO 27001, Essential Eight, and ACSC.",
          badge: "SaaS",
          color: "from-primary-blue/10 to-primary-blue/5",
          features: [
            "Authentication & session security",
            "Role-based access controls (RBAC)",
            "Data exposure & permission testing",
            "CI/CD pipeline misconfigurations",
            "Secure deployment & cloud storage review"
          ]
        },
        {
          icon: <Smartphone className="w-6 h-6 text-primary-blue" />,
          title: "Mobile App Security (iOS & Android)",
          description: "Real-world testing based on OWASP MASVS & ASD standards. We simulate mobile attacks on both frontend apps and backend APIs - ensuring your apps can withstand runtime manipulation and post-exploitation techniques.",
          badge: "Mobile",
          color: "from-primary-blue/10 to-secondary-blue/5",
          features: [
            "SSL pinning bypass, insecure storage",
            "API token leakage & replay attacks",
            "Root/jailbreak detection testing",
            "Code tampering, dynamic analysis & patching checks"
          ]
        },
        {
          icon: <Plug className="w-6 h-6 text-primary-blue" />,
          title: "API & Microservices Pentesting",
          description: "Deep testing of REST, GraphQL, and microservice communications. We identify flaws in authorization, data exposure, and API logic - based on OWASP API Security Top 10 and real-world attack chains.",
          badge: "API",
          color: "from-primary-blue/10 to-secondary-blue/5",
          features: [
            "Broken object-level authorization (BOLA)",
            "Role escalation & business logic flaws",
            "Rate limiting & DoS protection bypass",
            "Token handling & session abuse vectors"
          ]
        },
        {
          icon: <Globe className="w-6 h-6 text-primary-blue" />,
          title: "Network Infrastructure Penetration Testing",
          description: "Test your perimeter and internal network against real-world threats. We simulate both external (internet-facing) and internal attacker scenarios to assess vulnerabilities in your network, servers, and firewall configurations.",
          badge: "Network",
          color: "from-primary-blue/10 to-secondary-blue/5",
          features: [
            "Firewall & IPS/IDS evasion",
            "Network segmentation & VLAN hopping",
            "Insecure ports, legacy protocols & weak services",
            "Lateral movement simulation & internal reconnaissance",
            "Exposure of admin interfaces, SNMP, RDP, SSH"
          ]
        }
      ]
    },
    us: {
      title: "Comprehensive Pentesting for US Businesses",
      description: "Our United States penetration testing portfolio offers comprehensive security solutions aligned with federal regulations and industry-specific compliance requirements.",
      services: [
        {
          icon: <Shield className="w-6 h-6 text-primary-blue" />,
          title: "NIST Cybersecurity Framework",
          description: "Penetration testing services aligned with the NIST Cybersecurity Framework for US federal and enterprise organizations.",
          badge: "NIST",
          color: "from-primary-blue/10 to-primary-blue/5",
          features: [
            "NIST 800-53 controls assessment",
            "FedRAMP penetration testing",
            "FISMA compliance validation",
            "Zero trust architecture evaluation"
          ]
        },
        {
          icon: <Database className="w-6 h-6 text-primary-blue" />,
          title: "Healthcare Security Compliance",
          description: "Specialized security testing for US healthcare organizations focusing on HIPAA compliance and patient data protection.",
          badge: "HIPAA",
          color: "from-primary-blue/10 to-secondary-blue/5",
          features: [
            "HIPAA Security Rule assessment",
            "Medical device security testing",
            "Healthcare API security review",
            "PHI data flow analysis"
          ]
        },
        {
          icon: <Code className="w-6 h-6 text-primary-blue" />,
          title: "Financial Technology Security",
          description: "Comprehensive security testing for US fintech companies and financial institutions.",
          badge: "PCI DSS",
          color: "from-primary-blue/10 to-secondary-blue/5",
          features: [
            "PCI DSS penetration testing",
            "SOC 2 security assessment",
            "Payment application security review",
            "Digital banking platform testing"
          ]
        }
      ]
    }
  };

  const data = countryData[country.toLowerCase()];

  // Dynamic grid layout based on number of services
  const getResponsiveGridLayout = (serviceCount) => {
    switch (serviceCount) {
      case 1:
        return 'grid grid-cols-1 max-w-2xl mx-auto gap-6';
      case 2:
        return getGridStyles('two');
      case 3:
        return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8';
      case 4:
        return getGridStyles('four');
      default:
        return getGridStyles('three');
    }
  };

  return (
    <section className={getSectionWrapper('white')}>
      <div className={designSystem.section.maxWidth}>
        <motion.div
          {...designSystem.animation.fadeInUp}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h2 className={designSystem.typography.sectionTitle}>{data.title}</h2>
          <p className={designSystem.typography.sectionSubtitle}>
            {data.description}
          </p>
        </motion.div>

        <motion.div
          variants={designSystem.animation.staggerContainer}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true }}
          className={getResponsiveGridLayout(data.services.length)}
        >
          {data.services.map((service, index) => (
            <motion.div
              key={index}
              variants={designSystem.animation.staggerItem}
              className={`${getCardStyles()} min-h-[500px] flex flex-col`}
            >
              {/* Header with icon and badge */}
              <div className="flex items-center justify-between mb-8">
                <div className={getIconStyles('large')}>
                  {service.icon}
                </div>
                <span className="text-sm font-semibold text-primary-blue bg-primary-blue/10 px-3 py-1 rounded-full">
                  {service.badge}
                </span>
              </div>

              <h3 className={`${designSystem.typography.cardTitle} mb-6`}>
                {service.title}
              </h3>

              <p className="text-gray-600 mb-10 leading-relaxed flex-grow">
                {service.description}
              </p>

              {/* Features list */}
              <div className="space-y-5 mt-auto">
                {service.features.map((feature, i) => (
                  <motion.div
                    key={i}
                    className="flex items-start"
                    initial={{ opacity: 0, x: -10 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.4, delay: 0.1 * i }}
                  >
                    <div className="flex-shrink-0 w-7 h-7 bg-primary-blue/10 rounded-lg flex items-center justify-center mr-4 mt-0.5">
                      <CheckCircle className="w-5 h-5 text-primary-blue" />
                    </div>
                    <span className="text-gray-700 leading-relaxed text-base">
                      {feature}
                    </span>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default CountryServices; 