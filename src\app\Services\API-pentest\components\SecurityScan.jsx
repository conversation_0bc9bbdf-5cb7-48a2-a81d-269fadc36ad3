import Image from 'next/image';
import React from 'react';
import { MdAccessTime, MdSecurity, MdLightbulb, MdCode } from 'react-icons/md';

const SecurityScan = () => {
  return (
    <div className="flex md:flex-row flex-col md:p-20 bg-gradient-to-r from-blue-600 to-blue-900">
      {/* Left Half - Image Placeholder */}
      <div className="md:w-1/2 md:p-0 p-10">
      <Image src="/images/web-app-3.png" width={500} height={500} alt="" />
      </div>

      {/* Right Half - Content */}
      <div className="md:w-1/2 p-10 flex flex-col justify-center text-white">
        <div className="space-y-6">
          <div className="flex items-center">
            <MdAccessTime className="mr-4" size={30} />
            <span className="md:text-3xl">Continuous Pentesting</span>
          </div>
          <hr className="border-b border-blue-700"/>
          <div className="flex items-center pt-10">
            <MdSecurity className="mr-4" size={30} />
            <span className="md:text-3xl ">Scalable Pentesting Program</span>
          </div>
          <div className="flex items-center">
            <MdLightbulb className="mr-4" size={30} />
            <span className="md:text-3xl ">Real-Time Remediation Guidance</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecurityScan;