'use client';
import React, { useState, useEffect, useRef } from 'react';
import { Clock, Search, TrendingUp } from 'lucide-react';

const AnimatedCounter = ({ end, duration = 2000, suffix = '' }) => {
  const [count, setCount] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (!isVisible) return;

    let startTime = null;
    const startValue = 0;
    const endValue = end;

    const animate = (currentTime) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);
      
      // Easing function for smooth animation
      const easeOut = 1 - Math.pow(1 - progress, 3);
      const current = Math.floor(startValue + (endValue - startValue) * easeOut);
      
      setCount(current);
      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }, [isVisible, end, duration]);

  return (
    <div ref={ref} className="text-3xl sm:text-4xl lg:text-5xl font-bold text-secondary-blue">
      {count}{suffix}
    </div>
  );
};

export default function ResultsOutcomes() {
  return (
    <div className="bg-gray-50 py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        
        {/* Header */}
        <div className="mb-12 sm:mb-16 text-center sm:text-left">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-secondary-blue mb-2">
            Results & Outcomes
          </h2>
          <p className="text-lg text-gray-600 mt-4">
            Real results from teams using our <a href="/Services/Web-app" className="text-secondary-blue hover:underline font-semibold">web application testing</a> and remediation platform. Ready to see similar results? <a href="/Request-Demo" className="text-secondary-blue hover:underline font-semibold">Request a demo</a> today.
          </p>
        </div>

        {/* Three Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
          
          {/* Stat 1 - MTTR Reduction */}
          <div className="flex flex-col sm:flex-row items-start space-y-4 sm:space-y-0 sm:space-x-4 p-4 sm:p-0">
            <div className="bg-white rounded-xl p-3 sm:p-4 shadow-sm border border-purple-200 flex-shrink-0 mx-auto sm:mx-0">
              <Clock className="w-5 h-5 sm:w-6 sm:h-6 text-secondary-blue" />
            </div>
            <div className="space-y-2 text-center sm:text-left w-full">
              <AnimatedCounter end={70} suffix="%" duration={2500} />
              <h3 className="text-base sm:text-lg font-semibold text-secondary-blue leading-tight">
                Reduction in mean time to<br className="hidden sm:block" />
                <span className="sm:hidden"> </span>remediate (MTTR)
              </h3>
              <p className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                After integrating Capture The Bug, a SaaS company reduced average fix time from 17 days to under 6 days.
              </p>
            </div>
          </div>

          {/* Stat 2 - Validated Vulnerabilities */}
          <div className="flex flex-col sm:flex-row items-start space-y-4 sm:space-y-0 sm:space-x-4 p-4 sm:p-0">
            <div className="bg-white rounded-xl p-3 sm:p-4 shadow-sm border border-purple-200 flex-shrink-0 mx-auto sm:mx-0">
              <Search className="w-5 h-5 sm:w-6 sm:h-6 text-secondary-blue" />
            </div>
            <div className="space-y-2 text-center sm:text-left w-full">
              <AnimatedCounter end={36} duration={2000} />
              <h3 className="text-base sm:text-lg font-semibold text-secondary-blue leading-tight">
                Validated vulnerabilities<br className="hidden sm:block" />
                <span className="sm:hidden"> </span>retested and closed
              </h3>
              <p className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                One engineering team used our platform to collaborate directly with pentesters, resolving issues across their API, frontend, and auth modules.
              </p>
            </div>
          </div>

          {/* Stat 3 - Patch Success Rate */}
          <div className="flex flex-col sm:flex-row items-start space-y-4 sm:space-y-0 sm:space-x-4 p-4 sm:p-0 md:col-span-2 lg:col-span-1">
            <div className="bg-white rounded-xl p-3 sm:p-4 shadow-sm border border-purple-200 flex-shrink-0 mx-auto sm:mx-0">
              <TrendingUp className="w-5 h-5 sm:w-6 sm:h-6 text-secondary-blue" />
            </div>
            <div className="space-y-2 text-center sm:text-left w-full">
              <AnimatedCounter end={52} suffix="%" duration={2200} />
              <h3 className="text-base sm:text-lg font-semibold text-secondary-blue leading-tight">
                Improvement in patch success<br className="hidden sm:block" />
                <span className="sm:hidden"> </span>rate after first retest
              </h3>
              <p className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                By enabling back-and-forth communication with researchers, a fintech client fixed more issues correctly on the first try - reducing churn across releases.
              </p>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
}