"use client";
import React, { useState } from "react";
import { ArrowR<PERSON>, Copy, Check, Play, Headphones, Coffee, Share2 } from 'lucide-react';
import Image from 'next/image';

const PodcastItem = ({ linkUrl, image, title, description, date, isReversed }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  
  const shortDescription = description.length > 150 ? description.substring(0, 150) + "..." : description;
  
  const handleShare = async () => {
    const shareData = {
      title: title,
      text: description,
      url: linkUrl
    };

    try {
      if (navigator.share && navigator.canShare(shareData)) {
        await navigator.share(shareData);
      } else { 
        await navigator.clipboard.writeText(linkUrl);
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000);
      }
    } catch (error) { 
      try {
        await navigator.clipboard.writeText(linkUrl);
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000);
      } catch (clipboardError) {
        console.error('Failed to share or copy:', clipboardError);
      }
    }
  };

  return (
    <div className="max-w-6xl mx-auto"> 
      <div className="bg-gradient-to-br from-blue-50 via-white to-indigo-50 rounded-xl sm:rounded-2xl shadow-lg sm:shadow-xl p-4 sm:p-6 md:p-8 lg:p-12 mb-6 sm:mb-8">
        <div className="flex flex-col lg:flex-row items-center gap-6 sm:gap-8 lg:gap-12">
  
          <div className="flex-1 text-center lg:text-left space-y-4 sm:space-y-6 order-2 lg:order-1">
             
            <div className="flex items-center justify-center lg:justify-start gap-2">
              <div className="inline-flex items-center gap-2 bg-blue-600 text-white text-xs sm:text-sm font-medium px-3 sm:px-4 py-1.5 sm:py-2 rounded-full shadow-md">
                <Headphones size={14} className="sm:w-4 sm:h-4" />
                <span className="hidden xs:inline">Podcast by Capture The Bug</span>
                <span className="xs:hidden">CTB Podcast</span>
              </div>
            </div>
             
            <div className="space-y-2 sm:space-y-3">
              <h1 className="text-xl xs:text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 leading-tight">
                Capture The Bug Podcast
              </h1>
              <h2 className="text-lg xs:text-xl sm:text-2xl lg:text-3xl font-semibold text-blue-600 leading-tight">
                Cybersecurity Deep Dive!
              </h2>
            </div>
            
            
            <div className="prose prose-gray max-w-none">
              <p className="text-gray-600 text-sm xs:text-base lg:text-lg leading-relaxed">
                {isExpanded ? description : shortDescription}
              </p>
              
              {description.length > 150 && (
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="text-blue-600 hover:text-blue-800 font-medium mt-2 sm:mt-3 text-xs xs:text-sm transition-colors duration-200 underline decoration-dotted underline-offset-4"
                >
                  {isExpanded ? 'Show less' : 'Read full description'}
                </button>
              )}
            </div>
 
            <div className="flex items-center justify-center lg:justify-start gap-2 text-gray-500 text-xs xs:text-sm">
              <Coffee size={14} className="sm:w-4 sm:h-4" />
              <span className="italic">Grab a cup of coffee and dive in!</span>
            </div>
          </div>

          <div className="w-full max-w-xs sm:max-w-sm lg:max-w-md flex-shrink-0 order-1 lg:order-2">
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-xl sm:rounded-2xl blur-lg sm:blur-xl opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>
              <div className="relative bg-white p-3 sm:p-4 rounded-xl sm:rounded-2xl shadow-md sm:shadow-lg">
                <Image 
                  src={image} 
                  alt={title} 
                  className="w-full h-auto rounded-lg sm:rounded-xl shadow-sm sm:shadow-md transition-transform duration-500 group-hover:scale-105" 
                  width={400}
                  height={300}
                />
                <div className="absolute inset-0 bg-black/10 rounded-lg sm:rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <div className="bg-white/90 rounded-full p-3 sm:p-4 transform scale-0 group-hover:scale-100 transition-transform duration-300">
                    <Play size={20} className="sm:w-6 sm:h-6 text-blue-600 ml-1" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 sm:mt-8 pt-4 sm:pt-6 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-3 sm:gap-4">
            
            <a 
              href={linkUrl || "#"}
              target="_blank"
              rel="noopener noreferrer"
              className="group inline-flex items-center gap-2 sm:gap-3 bg-green-500 text-white text-base sm:text-lg font-semibold px-6 sm:px-8 py-3 sm:py-4 rounded-full hover:bg-green-600 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 w-full sm:w-auto justify-center"
            >
              <div className="bg-white/20 rounded-full p-1">
                <Play size={16} className="sm:w-5 sm:h-5 ml-0.5" />
              </div>
              <span className="hidden xs:inline">Listen on Spotify</span>
              <span className="xs:hidden">Spotify</span>
              <ArrowRight size={16} className="sm:w-5 sm:h-5 transition-transform duration-300 group-hover:translate-x-1" />
            </a>
            
            <div className="flex gap-3 w-full sm:w-auto justify-center"> 
              <button 
                onClick={handleShare}
                className="inline-flex items-center gap-2 bg-white text-gray-700 text-sm font-medium px-4 py-2.5 sm:py-3 rounded-full border-2 border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-all duration-300 shadow-sm flex-1 sm:flex-initial justify-center"
              >
                {isCopied ? <Check size={16} className="text-green-600" /> : <Share2 size={16} />}
                {isCopied ? 'Copied!' : 'Share'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const PodcastSection = () => {
  const podcastData = [
    {
      linkUrl: "https://open.spotify.com/show/14FDRXQTVuua8epnSZKW9u", 
      image: "/images/pod.png",
      title: "Capture The Bug Podcast - Cybersecurity Deep Dive!",
      description: "Welcome to Capture The Bug - Cybersecurity Deep Dive!, the podcast by Capture The Bug where we're breaking down barriers and making cybersecurity accessible for everyone. Think of us as the Robinhood of pentesting, democratizing the field and empowering companies of all sizes to secure their digital assets without breaking the bank. Based out of New Zealand, each episode takes you on a deep dive into the world of ethical hacking and cybersecurity. Whether you're a tech-savvy pro or just starting out, grab a cup of coffee and tune in for a sip of knowledge! ☕",
      date: "Podcast",
      isReversed: false
    }
  ];

  return (
    <section className="bg-gradient-to-b from-gray-50 to-white py-12 sm:py-16 md:py-20 lg:py-24">
      <div className="max-w-7xl mx-auto px-3 xs:px-4 sm:px-6 lg:px-8">
        {podcastData.map((item, index) => (
          <PodcastItem key={index} {...item} />
        ))}
      </div>
    </section>
  );
};

export default PodcastSection;