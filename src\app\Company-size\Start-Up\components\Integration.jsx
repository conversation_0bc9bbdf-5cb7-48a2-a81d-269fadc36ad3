"use client";

import React, { useState } from 'react';
import Link from "next/link";
import { ArrowRight } from "lucide-react";
import { motion } from 'framer-motion';
import Button from "../../../common/buttons/Button";
import Image from 'next/image';

const Integration = () => {
  const [activeSection, setActiveSection] = useState('get-compliant');

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen bg-white"
    >
      {/* Top Section */}
      <section className="py-16 sm:py-24 text-center">
        <div className="container mx-auto px-4 sm:px-6">
          <motion.h2 
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-3xl sm:text-4xl font-bold text-secondary-blue mb-12 sm:mb-16"
          >
            Founders trust Capture The Bug
          </motion.h2>

          <div className="flex flex-col md:flex-row justify-center items-start md:items-center gap-10 sm:gap-16 max-w-6xl mx-auto">
            {[
              {
                title: "Built for startups",
                description: "Security for fast-moving teams that need real results-not PDFs."
              },
              {
                title: "$1,000 credit",
                description: "Eligible early-stage startups get up to $1,000 off their first pentest."
              },
              {
                title: "9/10 recommend us",
                description: "Loved for real-time findings and audit-ready reports."
              }
            ].map((item, index) => (
              <React.Fragment key={index}>
                <motion.div 
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.6, delay: 0.2 + (index * 0.1) }}
                  className="flex-1 px-4 sm:px-8"
                >
                  <h3 className="text-xl sm:text-2xl font-bold text-secondary-blue mb-2">
                    {item.title}
                  </h3>
                  <p className="text-base sm:text-lg text-gray-700">
                    {item.description}
                  </p>
                </motion.div>
                {index < 2 && (
                  <div className="hidden md:block w-px h-36 bg-gray-300"></div>
                )}
              </React.Fragment>
            ))}
          </div>

          {/* CTA */}
          <motion.div 
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="mt-12 sm:mt-16 text-center space-y-3"
          >
            <motion.div 
            >
              <Button
                href="/Claim-Credit"
                variant="primary"
                size="lg"
                className="!overflow-hidden"
                style={{ transform: 'none' }}
              >
                Claim your credit →
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </section>
 

     <section className="py-5 sm:py-20 bg-blue-50">
  <div className="container mx-auto px-4 sm:px-6">
    <div className="flex flex-col lg:flex-row items-center gap-12 max-w-7xl mx-auto">
      <motion.div 
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.7 }}
        className="flex-1 w-full min-h-6 sm:min-h-96 rounded-xl   flex  "
      >
        <Image 
          src="/images/APIDash.svg" 
          alt="Security dashboard or pentesting illustration"
          className="w-full h-full mt-0 xl:-mt-28 object-cover rounded-xl"
          width={600}
          height={400}
        />
        <motion.div 
          animate={{ y: [0, -10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="text-secondary-blue text-5xl sm:text-6xl font-bold opacity-50"
        >
          
        </motion.div>
      </motion.div>

            <motion.div 
              initial={{ x: 50, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="flex-1 w-full"
            >
              <h3 className="text-3xl sm:text-4xl font-bold text-secondary-blue mb-4 leading-tight">
                Launch secure and scale with confidence
              </h3>
              <p className="text-base sm:text-lg text-gray-700 leading-relaxed mb-5">
                Capture The Bug PTaaS helps early-stage teams meet security demands with real, manual pentests mapped to frameworks like SOC 2 and ISO 27001 - with results delivered directly into your dev workflow. No waitlists. No PDFs. No friction.
              </p>
              <div className="grid md:grid-cols-2 gap-6 sm:gap-8 mb-48">
                {[
                  {
                    title: "Audit-ready reporting",
                    description: "Exportable reports tailored for SOC 2, ISO 27001, HIPAA, PCI DSS, and investor due diligence - generated from real pentest results."
                  },
                  {
                    title: "Workflow-native integrations",
                    description: "Push vulnerabilities to Jira, notify via Slack, sync with GitHub, and close findings fast - all in one dashboard."
                  }
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.6, delay: 0.9 + (index * 0.1) }}
                    whileHover={{ y: -5 }}
                    className="border-l-4 border-secondary-blue pl-4 sm:pl-6 bg-white p-4 sm:p-6 rounded-lg shadow-sm h-full flex flex-col"
                  >
                    <h4 className="text-lg sm:text-xl font-semibold text-gray-800 mb-2">
                      {item.title}
                    </h4>
                    <p className="text-sm sm:text-base text-gray-700 flex-grow">
                      {item.description}
                    </p>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Bottom Section CTA */}
      <motion.div 
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 1.1 }}
        className="relative z-10 bg-gradient-to-br from-gray-50 to-white w-[95%] sm:w-[80%] rounded-2xl mx-auto transform translate-y-1 -mt-24 sm:-mt-32 mb-6"
        style={{
          '@media (width: 1512px)': {
            width: '82%',
            marginTop: '-2.5rem',
            padding: '3rem'
          }
        }}
      >
        <div className="bg-ctb-green-50 rounded-2xl p-6 sm:p-8 text-center shadow-xl">
          <motion.h2 
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 1.2 }}
            className="text-2xl sm:text-4xl font-bold text-secondary-blue mb-4 sm:mb-6"
          >
            See the platform in action
          </motion.h2>
          <motion.p 
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 1.3 }}
            className="text-sm sm:text-base text-gray-700 leading-relaxed mb-6 sm:mb-8 max-w-2xl mx-auto"
          >
            Discover how Capture The Bug&apos;s <span className="font-semibold">Pentesting-as-a-Service platform</span> helps you manage security testing just like you manage code - <span className="font-semibold ">on demand, in your workflow, and fully transparent .</span> <br />
            From real-time findings to Jira-ready triage and SOC 2-ready reports, everything lives inside one clean dashboard.
          </motion.p>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              href="/Product/Penetration-Testing"
              variant="primary"
              size="lg"
              className="group relative px-3 sm:px-5 overflow-hidden"
              rightIcon={
                <motion.svg 
                  animate={{ x: [0, 4, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                  xmlns="http://www.w3.org/2000/svg" 
                  className="w-5 h-5" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </motion.svg>
              }
            >
              Explore CTB PTaaS Platform
            </Button>
          </motion.div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default Integration;