'use client';
import React from "react";
import Head from 'next/head';
import PartnersList from "@/app/Product/Penetration-Testing/components/PartnersList";
import BlogSection from "@/app/Home/components/Blogs";
import Landing from "./components/Landing";
import Security from "./components/Security";
import Testimonial from "../Testimonial";
import BreadcrumbNavigation from "@/app/common/components/BreadcrumbNavigation";
import { createIndustryBreadcrumbs } from "@/app/common/hooks/useBreadcrumbs";

const testimonial = {
  company: "Forsite",
  logo: "/images/forsite_logo_2.png",
  quote: "As a fast-moving SaaS provider, we've witnessed the significant advantages offered by Capture The Bug's platform. The ability to immediately address vulnerabilities as they are identified not only saves time for our developers but also reduces costs associated with lengthy security processes. Our collaboration with penetration testers through the platform has been seamless. We are enthusiastic about the ongoing partnership with Capture The Bug, looking forward to strengthening our security posture and further cost savings",
  author: "<PERSON>",
  position: "Chief Executive Officer "
};




export default function Healthtech() {
  const breadcrumbs = createIndustryBreadcrumbs('Health & Safety Tech', 'Health&SafetyTech');

  return (
    <>
      <Head>
        <title>Capture The Bug | Health & Safety Tech Security</title>
        <meta name="description" content="Secure health & safety platforms, contractor check-in systems, and compliance workflows with Capture The Bug. Prevent attacks and ensure uninterrupted operations." />
        <meta name="keywords" content="health tech penetration testing, safety compliance cybersecurity, contractor check-in security, hazard register protection, safety platform vulnerability assessment, digital health security, compliance system testing, Capture The Bug" />
        <meta name="robots" content="index, follow" />
        <link rel="icon" href="/favicon.ico" />

        {/* Open Graph */}
        <meta property="og:title" content="Capture The Bug | Health & Safety Tech Security" />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://capturethebug.xyz/Industries/Health&SafetyTech" />
        <meta property="og:description" content="Capture The Bug helps health & safety tech providers protect digital tools and sensitive data with proactive security assessments tailored to compliance-heavy environments." />
        <meta property="og:image" content="https://ibb.co/yB65rVb7" />

        {/* Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Capture The Bug | Health & Safety Tech Security" />
        <meta name="twitter:description" content="Defend your safety tech systems from cyber threats. Capture The Bug delivers specialized offensive testing for compliance, safety, and check-in platforms." />
        <meta name="twitter:image" content="https://ibb.co/yB65rVb7" />
      </Head>

      <div className="relative">
        {/* Breadcrumb Navigation - positioned absolutely at the top */}
        <div className="absolute top-0 left-0 right-0 pt-2 sm:pt-2 md:pt-5 xl:pt-14 z-10">
          <div className="max-w-7xl px-2 sm:px-2 md:px-16">
            <BreadcrumbNavigation items={breadcrumbs} />
          </div>
        </div>

        <Landing/>
        <Security/>
        <PartnersList/>
        <Testimonial
          company={testimonial.company}
          logo={testimonial.logo}
          quote={testimonial.quote}
          author={testimonial.author}
          position={testimonial.position}
          logoSize={{ width: 140, height: 100 }}
          logoStyle={{ marginLeft: -10 }}
        />
        <BlogSection/>
      </div>
    </>
  );
}