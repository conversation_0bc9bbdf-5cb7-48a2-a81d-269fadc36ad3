"use client";

export default function AboutUsLanding() {
  return (
    <section className="relative bg-white overflow-hidden py-24 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto text-center">
        {/* Small label above */}
        <p className="text-sm text-gray-500 mb-4 uppercase tracking-wide">About</p>

        {/* Main heading with emphasized purple text */}
        <h1 className="text-4xl sm:text-5xl font-semibold leading-tight text-gray-900">
          On a mission to <br />
          <span className="text-purple-600 font-bold">
            secure the internet and protect consumer data
          </span>
        </h1>
      </div>

      {/* Image placeholder and decorative shapes */}
      <div className="absolute bottom-0 right-0 w-64 h-64 sm:w-96 sm:h-96 md:w-[400px] md:h-[400px]">
        {/* Placeholder for your image */}
        <div className="w-full h-full bg-gray-100 rounded-lg shadow-lg flex items-center justify-center text-gray-400 font-semibold text-lg select-none">
          Image Placeholder
        </div>

        {/* Optional decorative shapes */}
        {/* You can replace these with your SVG or images */}
        <svg
          className="absolute top-0 left-0 w-24 h-24 text-purple-200"
          fill="currentColor"
          viewBox="0 0 100 100"
          aria-hidden="true"
        >
          <circle cx="50" cy="50" r="50" />
        </svg>
      </div>
    </section>
  );
}
