import React from 'react';
import { Suspense } from 'react';
import { notFound } from 'next/navigation';

// Valid country codes
const validCountryCodes = ['nz', 'au', 'us'];

export default async function CountryLayout({ children, params }) {
  // Await params before accessing its properties
  const resolvedParams = await params;

  // Check if the country code is valid
  if (!validCountryCodes.includes(resolvedParams.country.toLowerCase())) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#f8faff] to-white">
      <Suspense fallback={<div className="flex justify-center items-center min-h-screen">Loading...</div>}>
        {children}
      </Suspense>
    </div>
  );
} 