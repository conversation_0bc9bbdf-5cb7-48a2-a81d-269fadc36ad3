"use client";
import React, { useState, useEffect } from "react";
import { sendEmail } from "@/app/utils/send-email";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import Button from "@/app/common/buttons/Button";
import { FaQuoteLeft } from "react-icons/fa";
import { HiOutlineEnvelope } from "react-icons/hi2";

const testimonials = [
  {
    quote: "Capture The Bug helped us with our company's security compliance needs. Their team of highly skilled and professional security experts provided a quality service at a reasonable price. We highly recommend their IT cybersecurity services!",
    author: "<PERSON>",
    position: "Senior Security and DevOps Engineer",
    company: "Kademi",
    companyUrl: "https://kademi.co",
    logo: "/images/brands/kademi_brand_logo.png"
  },
  {
    quote: "The team at Capture The Bug have been amazing and super easy to work with. In reality, security testing is ongoing, and needs to be effective yet cost efficient. I love the CTB platform format over traditional pen testing, not sure I could go back!”",
    author: "<PERSON>",
    position: "CPO",
    company: "Yabble",
    companyUrl: "https://yabble.com",
    logo: "/images/brands/yabble_brand_logo.png"
  },
  {
    quote: "The team at Capture The Bug has been instrumental in helping us achieve and maintain our security certifications. Their knowledge and support have been invaluable to our business.",
    author: "Robbie Gill",
    position: "Sr. Director of Engineering",
    company: "Rafay",
    companyUrl: "https://rafay.co",
    logo: "/images/brands/rafay_brand_logo.png"
  }
];

const CompletePage = () => {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    companyEmail: "",
    companyName: "",
    phoneNumber: "",
    industry: "",
    message: "",
    stayInformed: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const [apiError, setApiError] = useState("");
  const [success, setSuccess] = useState("");
  const [canDownload, setCanDownload] = useState(false);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (isSubmitting) return;
    setIsSubmitting(true);
    setApiError("");
    setSuccess("");
    setCanDownload(false);
    try {
      // Make the API request to store submission and send email
      const response = await fetch("/api/submissions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          firstName: formData.firstName,
          lastName: formData.lastName,
          businessEmail: formData.companyEmail,
          company: formData.companyName,
          phoneNumber: formData.phoneNumber,
          industry: formData.industry,
          message: formData.message,
          stayInformed: formData.stayInformed,
          formType: 'request-quote',
          subject: 'New Contact Form Submission'
        }),
      });
     
      const result = await response.json();
      if (response.ok && result.success) {
        // Clear form fields on success
        setFormData({
          firstName: "",
          lastName: "",
          companyEmail: "",
          companyName: "",
          phoneNumber: "",
          industry: "",
          message: "",
          stayInformed: false,
        });
       
        setSuccess("Thank you! Your message has been sent. We'll get back to you shortly.");
        setCanDownload(true);
      } else {
        setApiError(result.error || "Something went wrong. Please try again.");
      }
    } catch (err) {
      console.error("Submission error:", err);
      setApiError("Network error. Please try again later.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#f5f6fa]">
      <div className="w-full max-w-7xl mx-auto flex flex-col lg:flex-row items-center justify-center min-h-[80vh] py-8 gap-0 lg:gap-0">
        {/* Left: Testimonials Only */}
        <div className="flex-1 flex flex-col bg-[#f5f6fa] px-4 py-8 min-h-[500px]">
          {/* Fixed Header Section */}
          <div className="w-full text-center mb-8">
            <div className="flex items-center justify-center gap-4 mb-4">
              <h1 className="text-3xl sm:text-4xl font-bold text-ctb-light-blue">
                Get in Touch with Us
              </h1>
              <HiOutlineEnvelope className="text-ctb-blue-350 text-4xl" />
            </div>
            <p className="text-lg text-slate-600">
              Trusted by Industry Leaders
            </p>
          </div>
          
          {/* Testimonials Section */}
          <div className="flex-1 flex items-center justify-center">
            <div className="w-full max-w-xl mx-auto bg-[#f5f6fa] rounded-xl flex flex-col items-center">
              <div className="w-full flex flex-col items-center">
                <div className="mb-6">
                  <Image
                    src={testimonials[currentTestimonial].logo}
                    alt={`${testimonials[currentTestimonial].company} logo`}
                    width={180}
                    height={60}
                    className="object-contain"
                  />
                </div>
                <FaQuoteLeft className="text-[#3b82f6] text-3xl mb-4" />
                <p className="text-slate-700 text-lg md:text-xl mb-6 text-center max-w-2xl">
                  {testimonials[currentTestimonial].quote}
                </p>
                <div className="text-center w-full max-w-2xl mx-auto">
                  <div className="font-bold text-slate-900">{testimonials[currentTestimonial].author}</div>
                  <div className="text-slate-600 text-sm">{testimonials[currentTestimonial].position}</div>
                  <a
                    href={testimonials[currentTestimonial].companyUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[#3b82f6] hover:underline text-sm"
                  >
                    {testimonials[currentTestimonial].company}
                  </a>
                </div>
                {/* Slider Dots */}
                <div className="flex justify-center space-x-2 mt-8">
                  {testimonials.map((_, idx) => (
                    <button
                      key={idx}
                      onClick={() => setCurrentTestimonial(idx)}
                      className={`w-2.5 h-2.5 rounded-full transition-colors duration-200 border border-[#3b82f6] ${
                        currentTestimonial === idx ? 'bg-[#3b82f6]' : 'bg-white'
                      }`}
                      aria-label={`Go to testimonial ${idx + 1}`}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Right: Form Only */}
        <div className="flex-1 w-full flex items-center justify-center bg-transparent px-2 sm:px-4 py-4 sm:py-8 min-h-[500px]">
          <div className="w-full max-w-[500px] sm:max-w-[600px] md:max-w-[700px] lg:max-w-none mx-auto lg:mx-0">
            <div className="bg-white rounded-xl sm:rounded-2xl shadow-lg sm:shadow-xl p-4 sm:p-6 lg:p-8">
              <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-5 md:space-y-6">
                {/* Name Fields */}
                <div className="grid sm:grid-cols-2 gap-3 sm:gap-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      First Name *
                    </label>
                    <input
                      type="text"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all"
                      placeholder="First Name"
                      required
                      disabled={isSubmitting}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all"
                      placeholder="Last Name"
                      required
                      disabled={isSubmitting}
                    />
                  </div>
                </div>

                {/* Work Email */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1.5 sm:mb-2">
                    Work email *
                  </label>
                  <input
                    type="email"
                    name="companyEmail"
                    value={formData.companyEmail}
                    onChange={handleChange}
                    className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all text-sm sm:text-base"
                    placeholder="<EMAIL>"
                    required
                    disabled={isSubmitting}
                  />
                </div>

                {/* Company Name */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1.5 sm:mb-2">
                    Company Name *
                  </label>
                  <input
                    type="text"
                    name="companyName"
                    value={formData.companyName}
                    onChange={handleChange}
                    className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all text-sm sm:text-base"
                    placeholder="Company Name"
                    required
                    disabled={isSubmitting}
                  />
                </div>

                {/* Phone Number */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1.5 sm:mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleChange}
                    className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all text-sm sm:text-base"
                    placeholder="Enter number"
                    required
                    disabled={isSubmitting}
                  />
                </div>

                {/* Industry Dropdown */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1.5 sm:mb-2">
                    Industry *
                  </label>
                  <select
                    name="industry"
                    value={formData.industry}
                    onChange={handleChange}
                    className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all bg-white text-sm sm:text-base"
                    required
                    disabled={isSubmitting}
                  >
                    <option value="">Please select</option>
                    <option value="Advertising & Media, Publishing">Advertising & Media, Publishing</option>
                    <option value="Auto">Auto</option>
                    <option value="Aviation, Railways">Aviation, Railways</option>
                    <option value="Finance / Fintech / Payments">Finance / Fintech / Payments</option>
                    <option value="Software Dev (B2B)">Software Dev (B2B)</option>
                    <option value="IT Services">IT Services</option>
                    <option value="Healthcare">Healthcare</option>
                    <option value="Education / Edtech">Education / Edtech</option>
                    <option value="Government">Government</option>
                    <option value="Other">Other</option>
                  </select>
                </div>

                {/* Message */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1.5 sm:mb-2">
                    Message *
                  </label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    rows="4"
                    className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all resize-none text-sm sm:text-base"
                    placeholder="Tell us about your compliance and security needs..."
                    required
                    disabled={isSubmitting}
                  ></textarea>
                </div>

                {/* Checkbox */}
                <div className="flex items-center space-x-2 sm:space-x-3">
                  <input
                    type="checkbox"
                    name="stayInformed"
                    checked={formData.stayInformed}
                    onChange={handleChange}
                    className="h-4 w-4 text-[#062575] focus:ring-[#062575] border-slate-300 rounded"
                    disabled={isSubmitting}
                  />
                  <label className="text-xs sm:text-sm text-slate-600">
                    I want to stay informed about CTB updates
                  </label>
                </div>

                {/* Privacy Policy */}
                <p className="text-xs text-slate-500">
                  By submitting, I agree to CTB&apos;s <Link href="/Useful-Links/Privacy-Policy" className="text-[#062575] hover:text-[#027bfc] underline">Privacy Policy</Link>.
                </p>

                {/* Submit Button */}
                <Button
                  type="submit"
                  variant="primary"
                  fullWidth={true}
                  disabled={isSubmitting}
                  className="font-semibold py-3 sm:py-4 px-6 disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base"
                >
                  {isSubmitting ? 'Submitting...' : 'Submit'}
                </Button>

                {/* Success and Error Messages */}
                {success && (
                  <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-start">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-green-800">{success}</p>
                      </div>
                    </div>
                  </div>
                )}

                {apiError && (
                  <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-start">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-red-800">{apiError}</p>
                      </div>
                    </div>
                  </div>
                )}
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompletePage;
