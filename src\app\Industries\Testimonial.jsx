import React from "react";
import Image from "next/image";
import Link from "next/link";

/**
 * @param {Object} props
 * @param {string} props.company 
 * @param {string} props.logo  
 * @param {string} props.quote 
 * @param {string} props.author 
 * @param {string} props.position 
 * @param {Object} [props.logoSize]  
 * @param {Object} [props.logoStyle] 
 */
const Testimonial = ({
  company,
  logo,
  quote,
  author,
  position,
  logoSize = { width: 96, height: 40 },
  logoStyle = {},
}) => {
  return (
    <section className="py-10 sm:py-16 px-2 sm:px-4">
      <div className="max-w-2xl md:max-w-4xl mx-auto">
        <div className="text-center mb-8 sm:mb-12">
          <h2 className="text-2xl sm:text-4xl font-bold text-gray-900 mb-3 sm:mb-4">
            What Our Client Say
          </h2>
          <div className="w-20 sm:w-28 h-[2px] bg-gray-600 mx-auto"></div>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm relative">
          <div className="p-6 sm:p-10 md:p-12">
            <div className="flex flex-col sm:flex-row justify-between items-center sm:items-start mb-6 sm:mb-8 gap-4">
              <div
                className="flex items-center justify-center relative"
                style={{ width: logoSize.width, height: logoSize.height, ...logoStyle }}
              >
                <Image
                  src={logo}
                  alt={company + " logo"}
                  fill
                  className="object-contain"
                  sizes={`(max-width: 640px) 80vw, 200px`}
                  style={{ objectFit: "contain" }}
                />
              </div>
            </div>
            <blockquote className="text-base sm:text-md md:text-lg text-gray-800 leading-relaxed mb-6 sm:mb-8 font-medium">
              &quot;{quote}&quot;
            </blockquote>
            <div className="flex flex-col sm:flex-row gap-2 mb-1 sm:mb-2">
              <div>
                <h4 className="text-base sm:text-lg font-semibold text-gray-900">{author}</h4>
                <p className="text-base sm:text-lg text-gray-600">{position}</p>
                <p className="text-base sm:text-lg text-gray-500 font-medium">{company}</p>
              </div>
            </div>
          </div>
          
          {/* Desktop/Laptop positioning - absolute positioned at bottom right */}
          <div className="hidden sm:block absolute bottom-10 right-6 sm:bottom-10 sm:right-10 md:bottom-12 md:right-12">
            <Link
              href="/Customers"
              className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 hover:underline font-medium transition-colors duration-200"
            >
              <span className="text-xl font-bold italic">See What Others Are Saying</span>
              <svg 
                className="ml-1 w-4 h-4 font-bold" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M9 5l7 7-7 7" 
                />
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M13 5l7 7-7 7" 
                />
              </svg>
            </Link>
          </div>
          
          {/* Mobile positioning - centered at bottom */}
          <div className="block sm:hidden pb-6 text-center">
            <Link
              href="/Customers"
              className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 hover:underline font-medium transition-colors duration-200"
            >
              <span className="text-lg font-bold italic">See What Others Are Saying</span>
              <svg 
                className="ml-1 w-4 h-4 font-bold" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M9 5l7 7-7 7" 
                />
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M13 5l7 7-7 7" 
                />
              </svg>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonial;