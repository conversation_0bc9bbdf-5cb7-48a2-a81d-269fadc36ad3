import PropTypes from 'prop-types';
import Link from 'next/link';
import Image from 'next/image';

export default function BlogCard({ image, title, date, description, readMoreLink }) {
  return (
    <div className="max-w-sm overflow-hidden">
      <Link href={readMoreLink}>
      <Image className="w-full" src={image} alt={title} width={400} height={256} />
      <div className="px-2 py-4">
        <div className="text-sm bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent font-bold mb-2">{date}</div>
        <div className="font-bold text-[20px] mb-6">{title}</div>
        <p className="text-slate-500 text-[13px]">{description}</p>
      </div>
      <div className="px-2 pt-4 pb-2">
        <div className="text-black font-semibold hover:underline">
          READ MORE
        </div>
      </div>
      </Link>
    </div>
  );
}

BlogCard.propTypes = {
  image: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired,
  date: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  readMoreLink: PropTypes.string.isRequired,
};
