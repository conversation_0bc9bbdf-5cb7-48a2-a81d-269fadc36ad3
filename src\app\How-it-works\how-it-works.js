
'use client';
import React from 'react'
import { motion } from 'framer-motion';
import Landing from './components/Landing';
import PentestWorkflowTabs from './components/Workflowtabs';
import Introduction from '../Product/Penetration-Testing/components/Introduction';
import FAQ from './components/FAQ';
import PartnersList from '../Home/components/PartnersList';
import Blogs  from '../Home/components/Blogs';
import BreadcrumbNavigation from '@/app/common/components/BreadcrumbNavigation';
import { createCustomBreadcrumbs } from '@/app/common/hooks/useBreadcrumbs';


function page() {
    const breadcrumbs = createCustomBreadcrumbs([
      {
        name: "How it Works",
        url: "/How-it-works",
        current: true,
        description: "Learn how our penetration testing process works"
      }
    ]);

    return (
      <div className="relative">
          <title>Capture The Bug | How it works? </title>

          {/* Breadcrumb Navigation - positioned absolutely at the top */}
          <div className="absolute top-0 left-0 right-0 pt-2 sm:pt-2 md:pt-5 xl:pt-14 z-10">
            <div className="max-w-7xl px-2 sm:px-2 md:px-16">
              <BreadcrumbNavigation items={breadcrumbs} />
            </div>
          </div>
          <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.5 }}
                    >
                      <motion.div
                        initial={{ y: 20, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ duration: 0.6, delay: 0.1 }}
                      >
          <Landing /> 
          </motion.div>
          <PartnersList/>
          <PentestWorkflowTabs/>   
          <Blogs />
          </motion.div>
          <FAQ/>
      </div>
    )
  }
  
  export default page