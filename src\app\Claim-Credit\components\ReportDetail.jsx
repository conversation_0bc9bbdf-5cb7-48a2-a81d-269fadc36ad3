// ReportDetail.jsx
"use client";
import React from "react";

// Original findings data - maintained
const findings = [
  {
    id: 1,
    text: "Most organizations (81%) are confident that their security posture is strong. Yet pentesting consistently reveals hidden vulnerabilities.",
  },
  {
    id: 2,
    text: "Three-quarters of organizations have service-level agreements (SLAs) that serious pentest findings should be fixed in 14 days or less. Yet few meet this goal.",
  },
  {
    id: 3,
    text: "The median time to resolve (MTTR) stands at 37 days for serious pentest findings-five times longer than the two-week SLA set by most organizations.",
  },
];

// BlueCheck icon, now with blue color for white card backgrounds
const BlueCheck = () => (
  <svg className="w-6 h-6 text-[#027bfc]" fill="currentColor" viewBox="0 0 20 20">
    <path
      fillRule="evenodd"
      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
      clipRule="evenodd"
    />
  </svg>
);

// Download icon for the button
const DownloadIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
  </svg>
);


const ReportDetail = () => {
  return (
    <div className="min-h-screen bg-slate-50 text-slate-800">
      {/* Section 1: Hero / Introduction */}
      <section className="w-full bg-gradient-to-br from-[#062575] to-[#025099] text-white py-12 sm:py-16 md:py-20 lg:py-32 px-4 sm:px-6 md:px-10 lg:px-16">
        <div className="max-w-5xl mx-auto text-center">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-5xl font-bold mb-6 leading-tight">
            State of Pentesting Report 2025
          </h1>
          <div className="w-20 sm:w-24 h-1.5 bg-[#027bfc] rounded-full mb-8 mx-auto" />
          <p className="text-base sm:text-lg md:text-xl lg:text-xl text-slate-200 max-w-3xl mx-auto leading-relaxed">
            Discover the most important findings from thousands of pentests. See how real-world security compares to expectations-and where organizations can improve.
          </p>
        </div>
      </section>

      {/* Section 2: Key Findings */}
      <section className="w-full py-10 sm:py-14 md:py-20 lg:py-24 px-4 sm:px-6 md:px-10 lg:px-12 bg-white">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#062575] mb-8 sm:mb-12 md:mb-16 text-center">
            Core Discoveries from the Report
          </h2>
          {/* Responsive grid for findings */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 md:gap-10 lg:gap-12">
            {findings.map((finding, idx) => (
              <div
                key={finding.id}
                className="bg-white w-full max-w-full p-5 rounded-xl border-2 border-[#027bfc] shadow-md flex flex-col transition-all duration-300 hover:shadow-xl hover:border-[#062575] hover:-translate-y-1"
              >
                <div className="flex-shrink-0 mb-4">
                  <BlueCheck />
                </div>
                <p className="text-slate-700 text-base sm:text-base md:text-lg leading-relaxed flex-grow">
                  {finding.text}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Section 3: Call to Action */}
      <section className="w-full bg-slate-100 py-10 sm:py-14 md:py-20 lg:py-24 px-4 sm:px-6 md:px-10">
        <div className="max-w-3xl mx-auto text-center">
          <h3 className="text-xl sm:text-2xl md:text-3xl font-semibold text-[#062575] mb-4 sm:mb-6">
            Ready to Dive Deeper?
          </h3>
          <p className="text-base sm:text-lg md:text-xl text-slate-600 mb-6 sm:mb-10">
            Get the complete picture with in-depth analysis, data breakdowns, and actionable recommendations.
          </p>
          <a
            href="#download"
            className="inline-flex items-center justify-center bg-[#027bfc] hover:bg-[#062575] text-white font-bold px-6 sm:px-8 md:px-10 py-3 sm:py-4 rounded-lg shadow-md hover:shadow-lg transition-all text-base sm:text-lg md:text-xl group"
          >
            <DownloadIcon />
            Download the Full Report
          </a>
        </div>
      </section>
    </div>
  );
};

export default ReportDetail;
