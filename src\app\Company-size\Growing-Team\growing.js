
'use client';
import React from 'react'
import { motion } from 'framer-motion';
import Landing from './components/Landing'
import ToolsForGrowth from './components/Integration'
import ComprehensiveSection from './components/Comprehensive.jsx'
import FrameworksSection from './components/Framework';
import BlogSection from '@/app/Home/components/Blogs';
import PartnersList from './components/PartnersList';
import BreadcrumbNavigation from '@/app/common/components/BreadcrumbNavigation';
import { createCompanySizeBreadcrumbs } from '@/app/common/hooks/useBreadcrumbs';

export default function GrowingTeam() {
  const breadcrumbs = createCompanySizeBreadcrumbs('Growing Team', 'Growing-Team');

  return (
    <div className="relative">
      {/* Breadcrumb Navigation - positioned absolutely at the top */}
      <div className="absolute top-0 left-0 right-0 pt-2 sm:pt-2 md:pt-5 xl:pt-14 z-10">
        <div className="max-w-7xl px-2 sm:px-2 md:px-16">
          <BreadcrumbNavigation items={breadcrumbs} />
        </div>
      </div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <Landing/>
        <PartnersList/>
      </motion.div>
      
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <FrameworksSection/>
      </motion.div>
      
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <ToolsForGrowth/>
      </motion.div>
      
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <BlogSection/>
      </motion.div>
    </motion.div>
    </div>
  )
}
