"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { getCookieConsent, saveCookieConsent, initializeAnalytics, initializeMarketingTools } from '../../utils/cookieConsent.js';

const CookieConsent = () => {
  const [showConsent, setShowConsent] = useState(false);
  const [isSlidingDown, setIsSlidingDown] = useState(false);
  const [showPreferences, setShowPreferences] = useState(false);
  const [preferences, setPreferences] = useState({
    essential: true, // Always required
    analytics: false,
    marketing: false,
  });

  useEffect(() => {
    // Check if user already gave consent after a short delay
    const timer = setTimeout(() => {
      const hasConsent = getCookieConsent() !== 'none';
      if (!hasConsent) {
        setShowConsent(true);
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Initialize tools based on saved preferences when component mounts
  useEffect(() => {
    if (getCookieConsent() === 'all') {
      setPreferences({
        essential: true,
        analytics: true,
        marketing: true,
      });
      
      // Initialize third-party tools based on consent
      initializeAnalytics();
      initializeMarketingTools();
    }
  }, []);

  const handleDismiss = () => {
    setIsSlidingDown(true);
    setTimeout(() => {
      setShowConsent(false);
      setIsSlidingDown(false); // Reset for next time
    }, 500); // Match animation duration
  };

  const acceptAll = () => {
    saveCookieConsent('all');
    setPreferences({
      essential: true,
      analytics: true,
      marketing: true,
    });
    
    // Push consent to dataLayer for GTM
    if (typeof window !== 'undefined' && window.dataLayer) {
      window.dataLayer.push({
        'event': 'cookie_consent_update',
        'cookie_consent': 'all'
      });
    }
    
    // Initialize tools after consent is given
    initializeAnalytics();
    initializeMarketingTools();
    
    handleDismiss();
  };

  const acceptEssential = () => {
    saveCookieConsent('essential');
    setPreferences({
      essential: true,
      analytics: false,
      marketing: false,
    });
    
    // Push consent to dataLayer for GTM
    if (typeof window !== 'undefined' && window.dataLayer) {
      window.dataLayer.push({
        'event': 'cookie_consent_update',
        'cookie_consent': 'essential'
      });
    }
    
    handleDismiss();
  };

  const savePreferences = () => {
    const consentType = (preferences.analytics || preferences.marketing) ? 'all' : 'essential';
    saveCookieConsent(consentType);
    
    // Push consent to dataLayer for GTM
    if (typeof window !== 'undefined' && window.dataLayer) {
      window.dataLayer.push({
        'event': 'cookie_consent_update',
        'cookie_consent': consentType
      });
    }
    
    // Initialize tools if user has opted in
    if (consentType === 'all') {
      initializeAnalytics();
      initializeMarketingTools();
    }
    
    handleDismiss();
  };

  if (!showConsent) return null;

  return (
    <div className={`fixed inset-x-0 bottom-0 z-50 px-4 py-4 ${
      isSlidingDown ? 'animate-slide-down' : 'animate-slide-up'
    }`}>
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg rounded-lg p-6">
          <div className="space-y-4">
            <div className="space-y-3">
              <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                This website stores cookies on your computer. These cookies are used to improve your website 
                experience and provide more personalized services to you, both on this website and through other media. 
                To find out more about the cookies we use, see our{' '}
                <Link href="/Useful-Links/Privacy-Policy" className="text-ctb-green-50 hover:underline">
                  Privacy Policy
                </Link>.
              </p>
              <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                We won&apos;t track your information when you visit our site. But in order to comply with your preferences, we&apos;ll 
                have to use just one tiny cookie so that you&apos;re not asked to make this choice again.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 pt-2">
              <button 
                onClick={acceptEssential}
                className="px-6 py-2 text-sm border border-gray-300 dark:border-gray-600 
                          rounded transition-colors hover:bg-gray-50 dark:hover:bg-gray-700
                          text-gray-700 dark:text-gray-300"
              >
                Essentials Only
              </button>
              <button 
                onClick={acceptEssential}
                className="px-6 py-2 text-sm border border-primary-blue 
                          rounded transition-colors hover:bg-primary-blue dark:hover:bg-orange-900/20
                          text-primary-blue hover:text-white dark:text-primary-blue"
              >
                Decline
              </button>
              <button 
                onClick={acceptAll}
                className="px-6 py-2 text-sm rounded transition-colors text-white
                          bg-secondary-blue hover:bg-primary-blue"
              >
                Accept
              </button>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default CookieConsent;