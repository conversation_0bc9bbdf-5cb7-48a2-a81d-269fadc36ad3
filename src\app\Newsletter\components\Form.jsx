'use client'
import React, { useState } from 'react';

const Form = () => {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [agreed, setAgreed] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (isSubmitting) return;
    
    setIsSubmitting(true);
    try {
      console.log('Form submitted:', { firstName, lastName, email, agreed });
      // Add your newsletter subscription logic here
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulated API call
    } catch (error) {
      console.error('Error submitting form:', error);
      alert('Failed to subscribe to newsletter.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-7xl mx-auto md:p-20 p-10 bg-white">
      <div className="flex flex-col md:flex-row md:space-x-8 md:gap-20">
        <div className="md:w-1/2 mb-8 md:mb-0">
          <h1 className="text-3xl font-bold mb-4">NEWSLETTER</h1>
          <p className="text-gray-600 mb-4">
            Subscribe to our newsletter to receive the latest security news once a month.
          </p>
          <p className="text-gray-600">
            We&apos;ll send you our best work – articles, interviews, guides – along with a selection of content we&apos;ve found interesting elsewhere on the web. Nothing more, nothing less.
          </p>
          <h2 className="text-2xl font-bold mt-8">Let&apos;s Keep In Touch?</h2>
        </div>

        <form onSubmit={handleSubmit} className="md:w-1/2 space-y-4">
          <div className="flex flex-wrap -mx-2">
            <div className="w-full sm:w-1/2 px-2 mb-4 sm:mb-0">
            <label className="md:text-lg font-bold">First Name</label>
              <input
                type="text"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded"
                required
              />
            </div>
            <div className="w-full sm:w-1/2 px-2">
            <label className="md:text-lg font-bold">Last Name</label>
              <input
                type="text"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded"
                required
              />
            </div>
          </div>
          <div className="w-full">
          <label className="md:text-lg font-bold">Email</label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded"
            required
          />
          </div>
          <div className="flex items-start">
            <input
              type="checkbox"
              id="agree"
              checked={agreed}
              onChange={(e) => setAgreed(e.target.checked)}
              className="mr-2"
              required
            />
            <label htmlFor="agree" className="text-sm text-gray-600">
              I agree to receive Capture The Bug&apos;s newsletter*
            </label>
          </div>
          <p className="text-sm text-gray-600">
            We value your privacy, and you may unsubscribe at any time. For more information, see our{' '}
            <a href="/Useful-Links/Privacy-Policy" className="text-blue-600 hover:underline">
              Privacy Policy
            </a>
            .
          </p>
          <button
            type="submit"
            className="w-full bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] text-white py-2 px-4 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-opacity duration-200"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'SUBSCRIBING...' : 'SUBSCRIBE'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default Form;