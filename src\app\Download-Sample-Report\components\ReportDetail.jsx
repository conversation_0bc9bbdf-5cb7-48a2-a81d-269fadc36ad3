"use client";
import React from "react";
import { ShieldExclamationIcon, ClockIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';

// --- ICONS ---
// Icon for the download button
const DownloadIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
  </svg>
);

// --- DATA ---
// Updated data to include titles and icons for the new card design
const findings = [
  {
    id: 1,
    title: "Sample Report Preview",
    description: "Get a firsthand look at our comprehensive security assessment format and methodology through this detailed sample report.",
    icon: <ShieldExclamationIcon />,
  },
  {
    id: 2,
    title: "Real-World Examples",
    description: "See how we identify, analyze, and provide solutions for common security vulnerabilities in real-world scenarios.",
    icon: <ClockIcon />,
  },
  {
    id: 3,
    title: "Expert Methodology",
    description: "Experience our thorough approach to security testing and learn how we help organizations strengthen their defenses.",
    icon: <ExclamationTriangleIcon />,
  },
];

const ReportDetail = () => {
  return (
    <div className="bg-ctb-bg-light text-tertiary-blue">
      {/* Section 1: Hero / Introduction */}
      <section className="w-full bg-gradient-to-br from-secondary-blue to-primary-blue text-white overflow-hidden">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-20 md:py-28 text-center relative">
            {/* Background decorative elements */}
            <div className="absolute -top-20 -left-20 w-64 h-64 bg-white/5 rounded-full opacity-50 blur-3xl"></div>
            <div className="absolute -bottom-20 -right-20 w-64 h-64 bg-white/10 rounded-full opacity-50 blur-3xl"></div>

            <div className="relative z-10">
                <h1 className="text-4xl md:text-5xl lg:text-5xl font-bold mb-4 leading-tight">
                    Download Sample Pentest Report
                </h1>
                <p className="text-lg md:text-lg text-blue-200 max-w-3xl mx-auto leading-relaxed">
                    See our security assessment methodology in action. Get a detailed sample report showcasing our comprehensive approach to identifying and addressing security vulnerabilities.
                </p>
            </div>
        </div>
      </section>

      {/* Section 2: Key Benefits */}
      <section className="w-full bg-tertiary-blue py-16 sm:py-20 md:py-24">
        <div className="max-w-screen-2xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
          <div className="text-center mb-12 sm:mb-16 md:mb-20">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-[500] text-center mb-4 leading-tight text-white">
              What&apos;s Included
            </h2>
          </div>
          
          <div className="md:px-4 lg:px-8 xl:px-16">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-6 items-stretch w-full">
              {findings.map((finding) => (
                <div
                  key={finding.id}
                  className="group relative flex flex-col justify-center items-center bg-white/10 backdrop-blur-sm rounded-2xl border border-ctb-green-50/20 shadow-[0_10px_35px_-15px_rgba(0,0,0,0.2)] hover:shadow-[0_25px_65px_-12px_rgba(0,0,0,0.3)] transition-all duration-500 ease-in-out p-6 md:p-8 min-h-[160px] overflow-hidden transform hover:-translate-y-1 hover:border-ctb-green-50/40"
                >
                  {/* Premium Background Effects */}
                  <div className="absolute inset-0 bg-gradient-to-br from-ctb-green-50/[0.03] via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                  
                  {/* Top right corner decorative element */}
                  <div className="absolute -top-12 -right-12 w-20 h-20 rounded-full bg-gradient-to-br from-ctb-green-50/10 to-ctb-green-50/5 group-hover:from-ctb-green-50/20 group-hover:to-ctb-green-50/10 transition-colors duration-700" />
                  
                  {/* Animated glow effect */}
                  <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[120%] h-[120%] rounded-full bg-ctb-green-50/5 blur-3xl opacity-0 group-hover:opacity-30 scale-0 group-hover:scale-100 transition-all duration-700" />
                  
                  {/* Left border accent */}
                  <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-ctb-green-50/20 via-ctb-green-50/40 to-ctb-green-50/20 transform origin-bottom scale-y-0 group-hover:scale-y-100 transition-transform duration-500 ease-out rounded-l-xl" />
                
                  {/* Content Container with relative positioning */}
                  <div className="relative z-10 flex flex-col items-center text-center">
                    {/* Icon Container */}
                    <div className="p-3 bg-gradient-to-br from-ctb-green-50/5 to-ctb-green-50/10 rounded-xl group-hover:from-ctb-green-50/10 group-hover:to-ctb-green-50/20 transition-colors duration-500 shadow-sm group-hover:shadow-md relative overflow-hidden mb-4">
                      {/* Icon Background Animation */}
                      <div className="absolute inset-0 bg-gradient-to-tr from-ctb-green-50/0 to-ctb-green-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-700" />
                      
                      {/* Animated dots in icon background */}
                      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700 overflow-hidden">
                        {[...Array(3)].map((_, i) => (
                          <div 
                            key={i} 
                            className="absolute w-0.5 h-0.5 rounded-full bg-ctb-green-50/30"
                            style={{
                              top: `${10 + (i * 30)}%`,
                              left: `${10 + (i * 30)}%`,
                              animation: `float${i} 3s infinite ease-in-out ${i * 0.5}s`
                            }}
                          />
                        ))}
                      </div>
                      
                      {/* The actual icon with enhanced transitions */}
                      {React.cloneElement(finding.icon, { 
                        className: 'w-8 h-8 text-ctb-green-50 group-hover:text-ctb-green-50/90 transition-all duration-500 transform group-hover:scale-110 relative z-10' 
                      })}
                    </div>
                    
                    {/* Title with animated underline */}
                    <div className="overflow-hidden mb-4">
                      <h3 className="text-lg font-bold text-white group-hover:text-white/90 transition-colors duration-500 transform translate-y-0 group-hover:-translate-y-1">{finding.title}</h3>
                      <div className="h-0.5 w-0 bg-gradient-to-r from-white to-white/80 group-hover:w-full transition-all duration-700 ease-out mt-1"></div>
                    </div>

                    {/* Description text */}
                    <p className="text-white/80 leading-relaxed group-hover:text-white transition-colors duration-500">
                      {finding.description}
                    </p>
                  </div>
                  
                  {/* Subtle bottom accent line */}
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-ctb-green-50/80 to-ctb-green-50/40 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-700 ease-out rounded-b-xl opacity-60"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Add global animation keyframes */}
      <style jsx global>{`
        @keyframes float0 {
          0%, 100% { transform: translateY(0) translateX(0); }
          50% { transform: translateY(-5px) translateX(3px); }
        }
        @keyframes float1 {
          0%, 100% { transform: translateY(0) translateX(0); }
          50% { transform: translateY(5px) translateX(-3px); }
        }
        @keyframes float2 {
          0%, 100% { transform: translateY(0) translateX(0); }
          50% { transform: translateY(-7px) translateX(-2px); }
        }
      `}</style>

      {/* Global styles for animations - not needed for this simplified version */}
    </div>
  );
};

export default ReportDetail;

