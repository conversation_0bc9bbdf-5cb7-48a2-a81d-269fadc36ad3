// components/common/AnnouncementBanner.jsx
"use client";
import React, { useState, useEffect } from "react";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";

const AnnouncementBanner = () => {
  const [isVisible, setIsVisible] = useState(true);
  const [isMounted, setIsMounted] = useState(false);
  const bannerRef = React.useRef(null);

  useEffect(() => {
    setIsMounted(true);

    // Set initial banner height to prevent layout shift
    document.documentElement.style.setProperty('--announcement-banner-height', '0px');

    // Check if banner was previously dismissed and if 1 hour has passed
    const dismissedData = localStorage.getItem('announcement-banner-dismissed');
    if (dismissedData) {
      try {
        const dismissedTime = parseInt(dismissedData);
        const currentTime = Date.now();
        const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds

        // If more than 1 hour has passed, remove the localStorage item and show banner
        if (currentTime - dismissedTime > oneHour) {
          localStorage.removeItem('announcement-banner-dismissed');
          setIsVisible(true);
        } else {
          // Still within 1 hour, keep banner hidden
          setIsVisible(false);
        }
      } catch (error) {
        // If there's an error parsing the timestamp, remove the item and show banner
        localStorage.removeItem('announcement-banner-dismissed');
        setIsVisible(true);
      }
    } else {
      // No dismissal record, show banner
      setIsVisible(true);
    }
  }, []);

  // Update CSS custom property for dynamic positioning
  useEffect(() => {
    const updateBannerHeight = () => {
      let bannerHeight = '0px';

      if (isVisible && isMounted && bannerRef.current) {
        // Get the actual height of the banner element
        const actualHeight = bannerRef.current.offsetHeight;
        bannerHeight = `${actualHeight}px`;
      }

      document.documentElement.style.setProperty('--announcement-banner-height', bannerHeight);
    };

    // Update height immediately
    updateBannerHeight();

    // Update height after a small delay to ensure DOM is ready
    setTimeout(updateBannerHeight, 50);

    // Update again after animation completes
    setTimeout(updateBannerHeight, 350);

    // Also update on window resize
    window.addEventListener('resize', updateBannerHeight);

    return () => {
      window.removeEventListener('resize', updateBannerHeight);
    };
  }, [isVisible, isMounted]);



  const handleClose = () => {
    setIsVisible(false);
    // Store current timestamp when banner is dismissed
    localStorage.setItem('announcement-banner-dismissed', Date.now().toString());
    // Immediately update banner height to 0 when closed
    document.documentElement.style.setProperty('--announcement-banner-height', '0px');
  };

  if (!isMounted || !isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        ref={bannerRef}
        initial={{ height: 0, opacity: 0 }}
        animate={{ height: "auto", opacity: 1 }}
        exit={{ height: 0, opacity: 0 }}
        transition={{ duration: 0.3 }}
        className="announcement-banner-container fixed top-0 left-0 right-0 z-[9998] bg-gradient-to-r from-ctb-light-blue to-blue-600 text-white"
      >
        <div className="relative flex items-center justify-center px-4 py-3 sm:px-6">
          <div className="flex items-center justify-center space-x-2 text-center">
            <span className="text-sm sm:text-base font-medium">
              🎉 Limited period offer: 
            </span>
            <Link 
              href="/Claim-Credit"
              className="font-bold underline hover:no-underline transition-all duration-200 hover:text-yellow-200"
            >
              Claim $1000 Pentest Credits Now
            </Link>
          </div>
          
          {/* Close Button */}
          <button
            onClick={handleClose}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-yellow-200 transition-colors duration-200"
            aria-label="Close announcement"
          >
            <svg 
              className="w-5 h-5" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M6 18L18 6M6 6l12 12" 
              />
            </svg>
          </button>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default AnnouncementBanner;
