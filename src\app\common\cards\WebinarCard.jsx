import Link from 'next/link';
import PropTypes from 'prop-types';
import Image from 'next/image';

const WebinarCard = ({ linkUrl,image, title, description, date }) => {
  return (
    <div className="bg-white rounded-lg shadow-md">
      <Link href={linkUrl} target='_blank'>
      {image && <Image src={image} alt={title} className="object-contain" width={400} height={256} />}
      <div className="p-4">
        <h3 className="text-xl font-semibold">{title}</h3>
        <p className="text-gray-500 my-2">{description}</p>
        <p className="text-blue-700">{date}</p>
      </div>
      </Link>
    </div>
  );
};

WebinarCard.propTypes = {
  linkUrl: PropTypes.string,
  image: PropTypes.string,
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  date: PropTypes.string.isRequired,
};

export default WebinarCard;
