import PTaaS from './PTaaS';
 
export const metadata = {
  title: "Capture The Bug | Penetration Testing as a Service (PTaaS)",
  description: "Expert-led penetration testing built for modern development teams. Capture The Bug delivers scalable, audit-ready PTaaS with deep reporting and fast turnaround.",
  keywords: "penetration testing, PTaaS platform, offensive security services, application security testing, cloud security audit, secure code assessment, vulnerability scanning for SaaS, Capture The Bug PTaaS",
  robots: "index, follow",
  icons: {
    icon: '/favicon.ico',
  },
  openGraph: {
    title: "Capture The Bug | Penetration Testing as a Service (PTaaS)",
    type: "website",
    url: "https://capturethebug.xyz/Product/Penetration-Testing", 
    description: "Scalable, on-demand PTaaS for teams who build fast and need expert-led security testing. Ideal for SaaS, APIs, cloud-native, and enterprise systems.",
    images: "https://i.ibb.co/FbqHw6rH/Screenshot-2025-06-19-123512.png",
  },
  twitter: {
    card: 'summary_large_image',
    title: "Capture The Bug | Penetration Testing as a Service (PTaaS)",
    description: "Get fast, flexible penetration testing with Capture The Bug’s PTaaS. Perfect for modern SaaS, startups, and enterprise compliance teams.",
    images: "https://i.ibb.co/FbqHw6rH/Screenshot-2025-06-19-123512.png",
  }
};
 
export default function Page() {
  return <PTaaS />;
}