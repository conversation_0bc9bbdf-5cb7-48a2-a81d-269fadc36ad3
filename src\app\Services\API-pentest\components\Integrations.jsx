"use client";
import React from "react";
import { 
  Shield, 
  Database, 
  Zap, 
  RotateCcw, 
  Lock, 
  Users, 
  AlertTriangle, 
  Key, 
  Brain, 
  Settings 
} from "lucide-react";
import Image from 'next/image';

export default function APISecurityReview() {
  const images = [
    "/images/api1.svg",
    "/images/API2.png",
    "/images/SOC.svg",
    "/images/api4.svg",
    "/images/api5.svg"
  ];

  const sections = [
    {
      title: "Why Are APIs a Top Target for Attackers?",
      description: "APIs are the backbone of modern software-connecting mobile apps, web platforms, cloud services, and third-party integrations. But with that power comes risk.",
      features: [
        {
          title: "Exposed Attack Surface",
          description: "Because APIs expose data and business logic over the internet, they're increasingly exploited for unauthorized access, data leakage, and account takeovers."
        },
        {
          title: "Critical Business Logic",
          description: "Whether you're managing user sessions, processing payments, or integrating with partners, your APIs are handling sensitive logic-and attackers know it."
        }
      ]
    },
    {
      title: "The Top API Security Risks We Test For",
      description: "Our comprehensive testing covers the most critical API vulnerabilities that put your business at risk.",
      features: [
        {
          title: "Broken Authentication & Authorization",
          description: "Testing for weak authentication mechanisms and authorization bypasses that could grant unauthorized access.",
          icon: <Lock className="w-6 h-6 text-ctb-green-50" />
        },
        {
          title: "Insecure Object References (IDOR)",
          description: "Identifying direct object reference vulnerabilities that allow access to unauthorized data or functions.",
          icon: <Database className="w-6 h-6 text-ctb-green-50" />
        },
        {
          title: "Injection Attacks (SQL, JSON, XML, etc.)",
          description: "Comprehensive testing for various injection vulnerabilities across all input vectors.",
          icon: <Zap className="w-6 h-6 text-ctb-green-50" />
        },
        {
          title: "Lack of Rate Limiting & Abuse Protection",
          description: "Assessing protective mechanisms against automated attacks and resource abuse.",
          icon: <RotateCcw className="w-6 h-6 text-ctb-green-50" />
        }
      ]
    },
    {
      title: "External API Pentesting",
      description: "Simulate real-world attacks against your public-facing APIs.",
      features: [
        {
          title: "Beyond OWASP API Top 10",
          description: "External APIs are the most exposed part of your architecture. We perform manual, in-depth testing on REST, GraphQL, and third-party-facing APIs to uncover hidden security flaws before attackers do."
        },
        {
          title: "Real-World Attack Scenarios",
          description: "Our testing simulates privilege escalation via token tampering, mass data scraping via broken rate limits, and chained logic attacks through multi-step endpoints."
        }
      ]
    },
    {
      title: "Internal API Pentesting", 
      description: "Expose and fix the hidden risks behind your firewall.",
      features: [
        {
          title: "Beyond Basic Security",
          description: "Internal APIs often power microservices, CI/CD workflows, and back-office operations-but they're rarely tested with the same rigor as public APIs."
        },
        {
          title: "Comprehensive Internal Testing",
          description: "Our manual internal API penetration testing simulates insider threats, misconfigured services, and chained logic flaws that could lead to privilege escalation or sensitive data leaks."
        }
      ]
    },
    {
      title: " API Vulnerabilities We Commonly Find",
      description: "Our testing uncovers the most critical internal API security gaps that traditional scanning misses.",
      features: [
        {
          title: "Input Validation & Sanitization Flaws",
          description: "Identifying improper input handling that could lead to data corruption or system compromise.",
          icon: <AlertTriangle className="w-6 h-6 text-ctb-green-50" />
        },
        {
          title: "Broken Session or Token Handling",
          description: "Testing authentication mechanisms and session management for internal services.",
          icon: <Key className="w-6 h-6 text-ctb-green-50" />
        },
        {
          title: "Business Logic Abuse Across Endpoints",
          description: "Uncovering logic flaws that could be chained together for unauthorized operations.",
          icon: <Brain className="w-6 h-6 text-ctb-green-50" />
        },
        {
          title: "Security Misconfigurations",
          description: "Finding configuration issues in dev/test environments that could expose production systems.",
          icon: <Settings className="w-6 h-6 text-ctb-green-50" />
        }
      ]
    }
  ];

const renderContent = (section, index, isDark) => (
    <div className="space-y-6">
      <h1 className={`text-xl sm:text-2xl md:text-4xl  font-bold ${isDark ? 'text-white' : 'text-blue-800'} leading-relaxed`}>
        {section.title}
      </h1>
      
      <p className={`text-sm sm:text-base md:text-lg ${isDark ? 'text-blue-100' : 'text-blue-700'} leading-relaxed`}>
        {section.description}
      </p>

      <div className="space-y-6">
        {section.features.map((feature, featureIndex) => (
          <div key={featureIndex} className={`border-l-4 ${isDark ? 'border-blue-300' : 'border-blue-200'} pl-6`}>
            <div className="flex items-center gap-3 mb-2">
              {feature.icon && <div className="flex-shrink-0">{feature.icon}</div>}
              <h3 className={`font-semibold text-lg ${isDark ? 'text-white' : 'text-blue-800'}`}>
                {feature.title}
              </h3>
            </div>
            <p className={`${isDark ? 'text-blue-100' : 'text-blue-700'} leading-relaxed`}>
              {feature.description}
            </p>
          </div>
        ))}
      </div>

     
    </div>
  );

  const renderImage = (index) => {
    // Define different scales for each image
    const imageScales = [
      'scale-[90%]',  
      'scale-[95%]',   
      'scale-[80%]',  
      'scale-[85%]',  
      'scale-[90%]'    
    ];
    
    const scaleClass = imageScales[index % images.length];
    
    return (
      <div className="relative">
        <div className={`relative overflow-hidden ${scaleClass}`}>
          <Image 
            src={images[index % images.length]} 
            alt={`API Security Dashboard ${(index % images.length) + 1}`} 
            className="w-full h-auto object-contain rounded-xl shadow-2xl"
            style={{
              filter: 'drop-shadow(0 25px 50px rgba(0, 0, 0, 0.15))',
            }}
            width={600}
            height={400}
          /> 
        </div>
      </div>
    );
  };


  return (
    <div className="min-h-screen w-full">
      {sections.map((section, index) => {
        const isDark = index % 2 === 1;  
        
        return (
          <div 
            key={index} 
            className={`py-16 md:py-20 ${isDark ? 'bg-blue-900' : 'bg-white'} w-full`}
          >
            <div className="max-w-7xl mx-auto px-4 sm:px-14 ">
              <div className={`grid grid-cols-1 lg:grid-cols-2 gap-20 lg:gap-36 items-center`}>
                {index % 2 === 0 ? (
                  <>
                    <div className="order-2 lg:order-1">
                      {renderImage(index)}
                    </div>
                    <div className="order-1 lg:order-2">
                      {renderContent(section, index, isDark)}
                    </div>
                  </>
                ) : (
                  <>
                    <div className="order-2 lg:order-1">
                      {renderContent(section, index, isDark)}
                    </div>
                    <div className="order-1 lg:order-2">
                      {renderImage(index)}
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
