"use client";

import React from 'react';

export default function OurStory() {
  return (
    <section className="w-full py-16 lg:py-24 bg-white relative overflow-hidden">
      {/* Subtle background decorations */}
      <div className="absolute top-0 left-0 w-64 h-64 bg-blue-50 rounded-full opacity-30 -translate-x-32 -translate-y-32"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-purple-50 rounded-full opacity-20 translate-x-48 translate-y-48"></div>
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        
        {/* Header */}
        <div className="mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-ctb-blue-350 mb-6">
            Our Story
          </h2>
          <p className="text-2xl text-tertiary-blue mb-4">The story behind Capture The Bug</p>
          <div className="w-20 h-1 bg-ctb-light-blue rounded-full"></div>
        </div>

        {/* Main Content */}
        <div className="space-y-8 text-gray-700 leading-relaxed">
          
          {/* Platform Introduction */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 lg:p-10 border-l-4 border-ctb-light-blue">
            <div className="flex items-start gap-4">
              <div className="w-10 h-10 bg-ctb-light-blue rounded-full flex items-center justify-center text-white flex-shrink-0 mt-1">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Purpose-Built for Modern SaaS</h3>
                <p className="text-lg">
                  Capture The Bug is a security testing platform purpose-built for modern SaaS teams. Designed to replace outdated consulting models, our product delivers continuous, on-demand penetration testing through a streamlined, developer-friendly experience.
                </p>
              </div>
            </div>
          </div>

          {/* Mission Statement */}
          <div className="bg-white rounded-2xl p-8 lg:p-10 shadow-lg border border-gray-100">
            <div className="flex items-start gap-4">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Our Mission</h3>
                <p className="text-lg">
                  Built by cybersecurity and product experts, our mission is to make security scalable, efficient, and aligned with how modern software is actually built - fast, iterative, and cloud-native.
                </p>
              </div>
            </div>
          </div>

          {/* Global Presence */}
          <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-8 lg:p-10 border-l-4 border-ctb-light-blue">
            <div className="flex items-start gap-4">
              <div className="w-10 h-10 bg-ctb-light-blue rounded-full flex items-center justify-center text-white flex-shrink-0 mt-1">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Global Reach</h3>
                <p className="text-lg mb-4">
                  With a globally distributed team and a growing footprint across New Zealand, Australia, the U.K., the U.S., and India, we support companies of all sizes - from startups chasing their first compliance audit to enterprises rolling out new products at scale.
                </p>
                <div className="flex flex-wrap gap-3">
                  {['New Zealand', 'Australia', 'United Kingdom', 'United States', 'India'].map((country) => (
                    <span key={country} className="px-4 py-2 bg-gradient-to-r from-blue-100 to-purple-100 text-gray-800 rounded-full text-sm font-medium border border-blue-200">
                      {country}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Platform Approach */}
          <div className="bg-white rounded-2xl p-8 lg:p-10 shadow-lg border border-gray-100">
            <div className="flex items-start gap-4">
              <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                <svg className="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Platform, Not Project</h3>
                <p className="text-lg">
                  Unlike traditional pentest vendors, Capture The Bug is a platform, not a project. We offer test orchestration, real-time results, triage workflows, and compliance-grade reporting - all in one place.
                </p>
              </div>
            </div>
          </div>

         
          {/* Closing Statement */}
<div className="relative bg-tertiary-blue rounded-3xl p-10 lg:p-12 text-white text-center overflow-hidden">
  {/* Decorative elements */}
  <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
  <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>
  
  <div className="relative z-10">
    {/* Quote icon */}
    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
      <svg className="w-8 h-8 text-ctb-light-blue" fill="currentColor" viewBox="0 0 24 24">
        <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
      </svg>
    </div>
    
    <blockquote className="text-2xl lg:text-3xl font-bold mb-6 leading-tight">
      &quot;Security should be continuous, product-integrated, and easy to access.&quot;
    </blockquote>
    
    <div className="flex items-center justify-center space-x-3">
      <div className="w-12 h-0.5 bg-ctb-light-blue"></div>
      <p className="text-xl font-medium">
        That&apos;s why we built <span className="font-bold text-ctb-blue-50">Capture The Bug</span>
      </p>
      <div className="w-12 h-0.5 bg-ctb-light-blue"></div>
    </div>
  </div>
</div>

        </div>
      </div>
    </section>
  );
}