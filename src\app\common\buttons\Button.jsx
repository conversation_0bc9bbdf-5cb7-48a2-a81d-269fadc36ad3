"use client";
import React from 'react';
import PropTypes from 'prop-types';
import Link from 'next/link';
import { twMerge } from 'tailwind-merge';
import { motion } from 'framer-motion';

/**
 * Universal Button component for consistent styling across the site
 * 
 * @param {Object} props - Component props
 * @param {string} [props.variant='primary'] - Button style variant: 'primary' (blue), 'secondary' (outline), 'success' (green)
 * @param {string} [props.size='md'] - Button size: 'sm', 'md', 'lg'
 * @param {string} [props.href] - If provided, button renders as a Link
 * @param {React.ReactNode} [props.leftIcon] - Icon to display before text
 * @param {React.ReactNode} [props.rightIcon] - Icon to display after text
 * @param {boolean} [props.fullWidth=false] - Whether button should stretch beyond its content width
 * @param {boolean} [props.animated=true] - Whether to show hover animations
 * @param {function} [props.onClick] - Click handler
 * @param {string} [props.className] - Additional classes
 * @param {React.ReactNode} props.children - Button content
 */
const Button = ({
  variant = 'primary',
  size = 'md',
  href,
  leftIcon,
  rightIcon,
  fullWidth = false,
  animated = true,
  onClick,
  className,
  children,
  ...props
}) => {
  // Base styles for all buttons
  const baseStyles = "relative font-semibold rounded-lg transition-all duration-300 flex items-center justify-center overflow-hidden group whitespace-nowrap";
  
  // Size variations
  const sizeStyles = {
    sm: "px-4 py-2 text-sm",
    md: "px-5 py-2.5 text-base",
    lg: "px-6 py-3.5 text-lg"
  };
  
  // Variant styles (background, text, borders)
  const variantStyles = {
    primary: "bg-secondary-blue hover:bg-primary-blue text-white shadow-md hover:shadow-lg transform hover:-translate-y-0.5",
    secondary: "bg-transparent border-2 border-secondary-blue text-secondary-blue hover:bg-secondary-blue/5 shadow-sm",
    success: "bg-[#58CC02] hover:bg-[#46a700] text-white shadow-md hover:shadow-lg transform hover:-translate-y-0.5",
    danger: "bg-red-500 hover:bg-red-600 text-white shadow-md hover:shadow-lg transform hover:-translate-y-0.5",
    ghost: "bg-transparent hover:bg-gray-100 text-gray-700"
  };
  
  // Width styles
  const widthStyles = fullWidth ? "w-full" : "inline-flex";
  
  // Animation elements based on variant
  const renderAnimationElements = () => {
    if (!animated) return null;
    
    switch (variant) {
      case 'primary':
      case 'success':
      case 'danger':
        return (
          <span className="absolute inset-0 w-full h-full bg-white/10 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></span>
        );
      case 'secondary':
        return (
          <span className="absolute left-0 top-0 h-full w-0 bg-secondary-blue/10 transform origin-left transition-all duration-300 ease-out group-hover:w-full"></span>
        );
      default:
        return null;
    }
  };
  
  // Combine all styles
  const buttonStyles = twMerge(
    baseStyles,
    sizeStyles[size],
    variantStyles[variant],
    widthStyles,
    className
  );
  
  // Arrow icon animation for right icon - fixed to prevent layout shift
  const arrowAnimation = rightIcon && animated ? "transition-all duration-300 ml-2 transform translate-x-0 group-hover:translate-x-1" : rightIcon ? "ml-2" : "";
  
  // Render as Link if href is provided
  if (href) {
    return (
      <Link
        href={href}
        className={buttonStyles}
        {...props}
      >
        {renderAnimationElements()}
        <span className="relative z-10 flex items-center">
          {leftIcon && <span className="mr-2">{leftIcon}</span>}
          {children}
          {rightIcon && (
            <span className={`inline-flex items-center ${arrowAnimation}`}>
              {rightIcon}
            </span>
          )}
        </span>
      </Link>
    );
  }
  
  // Otherwise render as button
  return (
    <button
      className={buttonStyles}
      onClick={onClick}
      type={props.type || "button"}
      {...props}
    >
      {renderAnimationElements()}
      <span className="relative z-10 flex items-center">
        {leftIcon && <span className="mr-2">{leftIcon}</span>}
        {children}
        {rightIcon && (
          <span className={`inline-flex items-center ${arrowAnimation}`}>
            {rightIcon}
          </span>
        )}
      </span>
    </button>
  );
};

Button.propTypes = {
  variant: PropTypes.oneOf(['primary', 'secondary', 'success', 'danger', 'ghost']),
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  href: PropTypes.string,
  leftIcon: PropTypes.node,
  rightIcon: PropTypes.node,
  fullWidth: PropTypes.bool,
  animated: PropTypes.bool,
  onClick: PropTypes.func,
  className: PropTypes.string,
  children: PropTypes.node.isRequired,
  type: PropTypes.string
};

export default Button; 