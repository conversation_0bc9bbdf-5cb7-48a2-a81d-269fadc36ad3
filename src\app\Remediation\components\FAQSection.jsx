'use client';

import React, { useState } from 'react';
import { Plus, Minus } from 'lucide-react';

const FAQItem = ({ question, answer, isOpen, onToggle, uniqueId }) => (
  <div className="mb-4">
    <button
      onClick={onToggle}
      aria-expanded={isOpen}
      className="flex w-full justify-between items-center text-left bg-gray-100 p-4 rounded-lg transition-all"
    >
      <span className="text-sm md:text-lg font-medium text-gray-900">
        {question}
      </span>
      <span className="ml-4 shrink-0">
        {isOpen ? (
          <Minus className="h-5 w-5 text-[#1e83fb]" />
        ) : (
          <Plus className="h-5 w-5 text-[#1e83fb]" />
        )}
      </span>
    </button>

    <div
      className={`transition-all duration-300 ease-in-out overflow-hidden ${
        isOpen ? 'max-h-[1000px] opacity-100 py-4' : 'max-h-0 opacity-0 py-0'
      } bg-gray-50`}
    >
      <div className="px-4">
        <p className="text-sm md:text-base text-gray-700 leading-relaxed">{answer}</p>
      </div>
    </div>
  </div>
);

const FAQ = () => {
  const [openItem, setOpenItem] = useState(null);

  const handleToggle = (uniqueId) => {
    setOpenItem((prev) => (prev === uniqueId ? null : uniqueId));
  };

  const faqs = [
    {
  id: 'api-unlimited-retesting',
  question: 'Do you offer unlimited retesting?',
  answer:
    'Yes. We offer unlimited retesting on all reported issues. Once you apply a fix, our team will verify it as many times as needed - until the issue is fully resolved and secured. Learn more about our comprehensive <a href="/Services/Web-app" class="text-blue-600 hover:underline font-semibold">web application testing</a> and <a href="/Services/API-pentest" class="text-blue-600 hover:underline font-semibold">API security testing</a> services.',
},
{
  id: 'api-guided-remediation-support',
  question: 'Do you provide guided remediation support?',
  answer:
    'Absolutely. We help your team understand each vulnerability, suggest fix strategies, and offer 1:1 technical support to speed up remediation.',
},
{
  id: 'api-how-soon-retest',
  question: 'How quickly can retesting be done after a fix?',
  answer:
    'In most cases, we begin retesting within 24 hours of your update. Critical issues may be prioritized even faster depending on severity and request.',
},
{
  id: 'api-remediation-detail',
  question: 'Do you provide detailed fix recommendations?',
  answer:
    'Yes. Every report includes clear and actionable remediation steps - whether it’s code-level changes, config updates, or best practice guidance.',
},
{
  id: 'api-retention-of-reports',
  question: 'Can we access old vulnerability reports and retest history?',
  answer:
    'Yes. You’ll have access to a full history of all reported vulnerabilities, their fix status, and retesting results - useful for audits, compliance, and internal tracking.',
},
{
  id: 'api-validation-process',
  question: 'How do you validate if a fix is successful?',
  answer:
    'We re-exploit the original vulnerability under the same conditions. If the issue no longer exists and no bypass is found, the fix is marked as verified.',
},
{
  id: 'api-ticket-integration',
  question: 'Can remediation tasks be synced with our ticketing system?',
  answer:
    'Yes. We support integrations with platforms like Jira, Slack, and others so that your dev team can track remediation directly in their normal workflow.',
},
{
  id: 'api-retest-coverage',
  question: 'Does retesting cover the entire app again or just the fix?',
  answer:
    'Retesting focuses on verifying the specific issue that was reported. However, if related components are affected, we flag and optionally expand the scope.',
},
{
  id: 'api-dev-team-involvement',
  question: 'Can our dev team interact directly with your security team?',
  answer:
    'Yes. We encourage open communication. Your developers can leave comments, ask for clarification, or request guidance through our platform during remediation.',
},
{
  id: 'api-no-fix-yet',
  question: 'What if we can’t fix the issue immediately?',
  answer:
    'We help you assess the risk and suggest temporary mitigations or compensating controls. Your team can then schedule a proper fix when possible.',
}
  ];

  return (
    <section className="w-full py-16 bg-gray-50">
      <div className="max-w-6xl mx-auto px-4">
        <div className="flex flex-col md:flex-row md:gap-20 gap-10">
          <div className="md:w-1/3 w-full flex items-center justify-center">
            <h2 className="text-4xl md:text-6xl font-bold text-[#1e83fb] text-center">
              FAQ
            </h2>
          </div>
          <div className="md:w-2/3 w-full flex flex-col justify-center">
            {faqs.map((faq) => (
              <FAQItem
                key={faq.id}
                uniqueId={faq.id}
                question={faq.question}
                answer={faq.answer}
                isOpen={openItem === faq.id}
                onToggle={() => handleToggle(faq.id)}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
