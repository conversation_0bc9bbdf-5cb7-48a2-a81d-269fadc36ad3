"use client";
import Link from "next/link";
import React from "react";
import Button from "@/app/common/buttons/Button";
import {
  Users,
  RefreshCw,
  Inbox,
  BarChart3,
  Shield,
  Puzzle,
  MessageCircle
} from "lucide-react";

const FeatureCard = ({ icon: Icon, title, subtitle, description }) => (
  <div className="flex flex-col space-y-4 p-6 bg-slate-800/50 rounded-lg border border-slate-700/50 backdrop-blur-sm">
    <div className="text-4xl text-ctb-green-50">
      <Icon size={48} />
    </div>
    <div>
      <h3 className="text-xl font-semibold text-white mb-1">{title}</h3>
      <p className="text-blue-400 text-sm font-medium mb-2">{subtitle}</p>
      <p className="text-gray-300 text-sm leading-relaxed">{description}</p>
    </div>
  </div>
);
 
export default function PentestingFeatures() {
  const features = [
    {
      icon: Users,
      title: "No scanners. Just verified results.",
      subtitle: "No scanners. Just real humans.",
      description: "Every test on our PTaaS platform combines real-world attack simulations with expert insights to uncover deep, logic-based flaws that scanners miss-delivering trustworthy results at platform speed and scale."
    },
    {
      icon: RefreshCw,
      title: "Continuous Pentesting",
      subtitle: "Security that evolves with your code.",
      description: "Re-test vulnerabilities, validate fixes, and assess new changes continuously-not just once a year."
    },
    {
      icon: Inbox,
      title: "Jira, Slack & GitHub Integrations",
      subtitle: "Send findings directly to your team.",
      description: "Auto-sync vulnerabilities with Jira, push alerts to Slack, and integrate with your CI/CD for faster remediation."
    },
    {
      icon: BarChart3,
      title: "Real-Time Findings Dashboard",
      subtitle: "Track risks as they're discovered.",
      description: "Get live visibility into vulnerabilities, remediation status, and test progress-all in a central PTaaS dashboard."
    },
    {
      icon: Shield,
      title: "Compliance-Ready Reporting",
      subtitle: "Support SOC 2, ISO 27001, HIPAA & more.",
      description: "Download audit-aligned reports with mapped vulnerabilities, remediation notes, and timelines that satisfy compliance frameworks."
    },
    {
      icon: Puzzle,
      title: "API & Application Coverage",
      subtitle: "Built for modern stacks-REST, GraphQL, SPAs.",
      description: "We test APIs, web apps, and cloud-native systems with a methodology aligned to OWASP Top 10 and business logic abuse cases."
    }
  ];

  return (
    <div className="relative overflow-hidden"> 
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-slate-900 to-indigo-900 z-0" />
 
      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-full h-full opacity-20 z-0">
        <div className="w-full h-full bg-gradient-to-t from-sky-600/30 to-transparent rounded-full scale-150" />
      </div>
      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1/2 h-1/2 opacity-30 z-0">
      </div>
 
      <div className="relative z-10 px-4 py-12 md:py-20 max-w-7xl mx-auto">
        <div className="text-center mb-16 md:mb-20">
          <h2 className="text-3xl md:text-4xl font-bold text-white">Features</h2>
          <p className="text-blue-400 mt-4 text-md max-w-2xl mx-auto">All the tools you need for faster, smarter pentesting at scale.</p>
        </div>
 
        {/* Mobile: Single column */}
        <div className="block sm:hidden">
          <div className="grid grid-cols-1 gap-6">
            {features.map((feature, index) => (
              <FeatureCard
                key={index}
                icon={feature.icon}
                title={feature.title}
                subtitle={feature.subtitle}
                description={feature.description}
              />
            ))}
          </div>
        </div>
 
        {/* Tablet and Desktop: Two rows layout */}
        <div className="hidden sm:block">
          {/* First row - 3 cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 mb-6 md:mb-8">
            {features.slice(0, 3).map((feature, index) => (
              <FeatureCard
                key={index}
                icon={feature.icon}
                title={feature.title}
                subtitle={feature.subtitle}
                description={feature.description}
              />
            ))}
          </div>
          
          {/* Second row - 3 cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {features.slice(3, 6).map((feature, index) => (
              <FeatureCard
                key={index + 3}
                icon={feature.icon}
                title={feature.title}
                subtitle={feature.subtitle}
                description={feature.description}
              />
            ))}
          </div>
        </div>

        <div className="text-center mt-16 mb-10">
          <Button
            href="/Request-Demo"
            variant="primary"
            size="lg"
            className="text-lg px-12"
          >
            Request a demo →
          </Button>
        </div>
      </div>
    </div>
  );
}