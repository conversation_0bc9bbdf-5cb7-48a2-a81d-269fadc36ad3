"use client";
import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  ChevronUpIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  Bars3Icon,
  XMarkIcon,
} from "@heroicons/react/20/solid";
import Button from "../buttons/Button";
import Intercom from '@intercom/messenger-js-sdk';
import { useActiveNavigation } from './useActiveNavigation';

export default function Header() {
  Intercom({
    app_id: 'a07mn7gg',
  });

  const [activeMenu, setActiveMenu] = useState("");
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [expandedMobileMenu, setExpandedMobileMenu] = useState(null);
  const leaveTimeout = useRef(null);
  const [activeSubMenu, setActiveSubMenu] = useState("");
  const [activeSubSubMenu, setActiveSubSubMenu] = useState("");
  const [bannerHeight, setBannerHeight] = useState(0);

  // Use the active navigation hook
  const { isNavItemActive } = useActiveNavigation();

  // Brand colors
  const brandColors = {
    primary: "primary-blue",
    secondary: "secondary-blue",
    tertiary: "tertiary-blue",
    light: "#F8FAFF",
    accent: "#0B45DB"
  };

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setMobileMenuOpen(false);
        setExpandedMobileMenu(null);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Monitor banner height changes
  useEffect(() => {
    const updateBannerHeight = () => {
      const computedHeight = getComputedStyle(document.documentElement)
        .getPropertyValue('--announcement-banner-height')
        .trim();

      if (computedHeight && computedHeight !== '0px') {
        setBannerHeight(parseInt(computedHeight) || 0);
      } else {
        setBannerHeight(0);
      }
    };

    // Initial check
    updateBannerHeight();

    // Create a MutationObserver to watch for style changes
    const observer = new MutationObserver(updateBannerHeight);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style']
    });

    // Also listen for custom events from the banner
    const handleBannerChange = () => updateBannerHeight();
    window.addEventListener('banner-height-changed', handleBannerChange);

    return () => {
      observer.disconnect();
      window.removeEventListener('banner-height-changed', handleBannerChange);
    };
  }, []);


  useEffect(() => {
    if (mobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [mobileMenuOpen]);

  const menuItems = {
    Product: {
      title: "Product",
      url: "/Product/Penetration-Testing",
      noDropdown: true,
    },
    Solutions: {
      title: "Solutions", 
      categories: [
        {
          title: "Company Size",
          bgColor: "bg-blue-500",
          links: [
            {
              name: "Start-Up",
              url: "/Company-size/Start-Up"
            },
            {
              name: "Growing Team ",
              url: "/Company-size/Growing-Team"
            },
            {
              name: "Enterprise",
              url: "/Company-size/Enterprise"
            },
          ],
        },
        {
          title: "Services",
          bgColor: "bg-blue-500",
          links: [
            {
              name: "Web Application",
              url: "/Services/Web-app"
            },
            {
              name: "Mobile Application",
              url: "/Services/Mobile-app"
            },
            {
              name: "Network Infrastructure",
              url: "/Services/Network-pentest"
            },
            {
              name: "API Testing",
              url: "/Services/API-pentest"
            },
          ],
        },
        {
          title: "Industries",
          bgColor: "bg-blue-500",
          links: [ 
            {
              name: "Automotive & Transportation",
              url: "/Industries/Automotive&Transportation"
            },
            {
              name: "Banking & Financial Services",
              url: "/Industries/Banking&FinancialServices"
            },
            {
              name: "E-commerce & Retail",
              url: "/Industries/Ecommerce&Retail"
            },
            {
              name: "Education & EdTech",
              url: "/Industries/Education&EdTech"
            },
            {
              name: "AI Security & Infrastructure",
              url: "/Industries/AISecurity&Infrastructure"
            },
            {
              name: "Health & Safety Tech",
              url: "/Industries/Health&SafetyTech"
            },
            {
              name: "Legal & Compliance",
              url: "/Industries/Legal&Compliance"
            },
            {
              name: "SaaS & Cloud Platforms",
              url: "/Industries/SaaS&CloudPlatforms"
            },
            {
              name: "Telecom & Media",
              url: "/Industries/Telecom&Media"
            },
          ],
        },
      ],

      ebook: {
        title: "SMARTER SECURITY TESTING FOR MODERN DIGITAL BUSINESSES.",
        description: "Do you release features and update codes all year but only pentest once or twice? You're not alone-and it's a risk.",
        url: "/Download-Report",
        cta: "Download the eBook ",
        image: "/images/plan_cover.png"
      }
    },

    Blogs: {
      title: "Blogs",
      url: "/Blogs",
      noDropdown: true,
    },

    Customer: {
      title: "Customer",
      url: "/Customers",
      noDropdown: true,
    },
    Pricing: {
      title: "Pricing",
      url: "/Pricing",
      noDropdown: true,
    },
    Company: {
      title: "Company",
      categories: [
        {
          title: "OVERVIEW",
          bgColor: "bg-blue-500",
          links: [
            {
              name: "About",
              description: " ",
              url: "/Company/About-Us"
            },
            {
              name: "Press",
              description: " ",
              url: "/Company/Press"
            },
            {
              name: "Tech Podcast",
              description: "",
              url: "https://businessdesk.co.nz/article/technology/the-business-of-tech-podcast-startups-the-budget-and-bounties-for-bugs",
              external: true,
            },
            {
              name: "Contact Us",
              description: " ",
              url: "/Company/Contact-Us"
            },
              {
              name: "Partner With Us",
              description: " ",
              url: "/Company/Partner-With-Us"
            },
          ],
        },
      ],
    },
  };

  const handleMouseEnter = (key) => {
    if (leaveTimeout.current) {
      clearTimeout(leaveTimeout.current);
      leaveTimeout.current = null;
    }
    setActiveMenu(key);
    setActiveSubMenu("");
  };

  const handleMouseLeave = () => {
    leaveTimeout.current = setTimeout(() => {
      setActiveMenu("");
      setActiveSubMenu("");
      setActiveSubSubMenu("");
    }, 250);
  };

  const handleSubMenuEnter = (key) => {
    setActiveSubMenu(key);
  };

  const handleSubMenuLeave = () => {
    setActiveSubMenu("");
    setActiveSubSubMenu("");
  };

  const handleSubSubMenuEnter = (key) => {
    setActiveSubSubMenu(key);
  };

  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
    setExpandedMobileMenu(null);
  };

  return (
    <div
      className="w-full bg-white fixed left-0 right-0 shadow-md transition-all duration-300 ease-in-out"
      style={{
        zIndex: 9997,
        top: `var(--announcement-banner-height, ${bannerHeight}px)`
      }}
    >
      <nav className="max-w-screen-2xl mx-auto flex items-center justify-between px-3 sm:px-4 pt-3 sm:pt-4 md:px-6 lg:px-8">
        <div className="flex-shrink-0 my-1 sm:my-2 md:ml-4 lg:ml-8 xl:ml-16">
          <Link href="/">
            <Image
              src={"/images/dark_logo.png"}
              alt="Logo"
              width={134}
              height={30}
              className="w-16 xs:w-18 sm:w-20 md:w-24 lg:w-30 xl:w-32 h-auto transition-transform duration-300 hover:scale-105"
              priority
            />
          </Link>
        </div>


        <div className="lg:hidden">
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="p-2 rounded-md hover:bg-gray-100 transition-colors"
            aria-label="Toggle mobile menu"
          >
            {mobileMenuOpen ? (
              <XMarkIcon className="h-5 w-5 sm:h-6 sm:w-6 text-gray-700" />
            ) : (
              <Bars3Icon className="h-5 w-5 sm:h-6 sm:w-6 text-gray-700" />
            )}
          </button>
        </div>


        <ul className="hidden lg:flex flex-1 justify-center space-x-2 xl:space-x-6 items-stretch">
          {Object.entries(menuItems).map(([key, value]) => (
            <li
              key={key}
              className={`relative text-sm xl:text-sm font-medium cursor-pointer px-2 xl:px-1 py-6 border-b-4 transition-all duration-300 ${
                isNavItemActive(key) 
                  ? 'border-blue-500 text-blue-600' 
                  : 'border-transparent hover:border-blue-500'
              }`}
              style={{
                marginBottom: '-4px',
                zIndex: 1
              }}
              onMouseEnter={() => handleMouseEnter(key)}
              onMouseLeave={handleMouseLeave}
            >
              {value.noDropdown ? (
                <Link 
                  href={value.url} 
                  className={`transition-all duration-300 ${
                    isNavItemActive(key) 
                      ? 'text-blue-600' 
                      : 'hover:text-blue-600'
                  }`}
                >
                  <span>{value.title}</span>
                </Link>
              ) : (
                <span
                  className={`flex items-center space-x-1 transition-all duration-300 ${
                    isNavItemActive(key) 
                      ? 'text-blue-600' 
                      : 'hover:text-blue-600'
                  }`}
                  onClick={() => setActiveMenu(activeMenu === key ? "" : key)}
                >
                  <span>{value.title}</span>
                  {activeMenu === key ? (
                    <ChevronUpIcon className="h-4 w-4 text-blue-500 transition-transform duration-300 transform" />
                  ) : (
                    <ChevronDownIcon className={`h-4 w-4 transition-transform duration-300 transform ${
                      isNavItemActive(key) ? 'text-blue-500' : ''
                    }`} />
                  )}
                </span>
              )}
              {activeMenu === key && value.categories && (
                <div
                  className="absolute top-full mt-6 z-50"
                  style={{
                    left: '100%',
                    transform: 'translateX(-36%)',
                    minWidth:
                      key === 'Solutions' && value.ebook ? '1200px' :
                        key === 'Company' ? '280px' :
                          value.categories.length === 1 ? '450px' : '900px',
                    maxWidth: '',
                  }}
                  onMouseEnter={() => handleMouseEnter(key)}
                  onMouseLeave={handleMouseLeave}
                >

                  <div className="absolute inset-0 bg-white rounded-lg shadow-lg"
                    style={{
                      width: 'calc(100% + 40px)',
                      left: '-20px',
                      top: '-20px',
                      bottom: '-20px',
                      zIndex: -1,
                      boxShadow: '0 4px 24px rgba(0, 0, 0, 0.08)'
                    }}
                  ></div>
                  <div
                    className="bg-gradient-to-br from-[#0052CC]/5 to-[#0052CC]/10 rounded-lg relative"
                    style={{
                      width: '100%',
                    }}
                  >
                    <div className="p-4 xl:p-6 ">
                      <div className={`grid ${key === 'Solutions' && value.ebook ? 'grid-cols-4' : value.categories.length === 1 ? 'grid-cols-1' : 'grid-cols-2'} gap-0`}>
                        {value.categories.map((category, idx) => (
                          <div key={idx} className={
                            key === 'Solutions' && value.ebook ?
                              (idx === 0 ? "pr-3 xl:pr-4" : 
                               idx === 1 ? "px-2 xl:px-3" :
                               idx === 2 ? "px-2 xl:px-3   " : "") :
                              value.categories.length === 2 && idx === 0 ? "pr-4 xl:pr-6" :
                                value.categories.length === 2 && idx === 1 ? "pl-4 xl:pl-6" : ""
                          }>
                            <div className="mb-5 xl:mb-6">
                              <h3 className="text-base font-bold text-tertiary-blue uppercase tracking-wider">
                                  {category.title}
                                </h3>
                            </div>

                            <div className="space-y-4">
                              {category.links.map((link, linkIndex) => (
                                <div key={linkIndex} className="group">
                                  <a
                                    href={link.url}
                                    className="block py-1.5 px-2 hover:bg-[#0052CC]/10 hover:shadow-sm rounded-md transition-all duration-300"
                                    {...(link.external && { target: "_blank", rel: "noopener noreferrer" })}
                                  >
                                    <div className="flex items-center justify-between">
                                      <h4 className="text-sm xl:text-base font-medium text-primary-blue group-hover:text-secondary-blue transition-all duration-300">
                                          {link.name}
                                        </h4>
                                      <ChevronRightIcon className="h-4 w-4 text-primary-blue opacity-0 group-hover:opacity-100 group-hover:text-secondary-blue transform translate-x-0 group-hover:translate-x-1 transition-all duration-300" />
                                    </div>
                                        {link.description && (
                                      <p className="text-xs xl:text-sm text-gray-500 mt-1 group-hover:text-gray-700 transition-all duration-300">
                                            {link.description}
                                          </p>
                                        )}
                                  </a>

                                  {link.subMenu && (
                                    <div className="ml-4 xl:ml-6 mt-1 space-y-1.5">
                                      {link.subMenu.map((subLink, subLinkIndex) => (
                                        <a
                                          key={subLinkIndex}
                                          href={subLink.url}
                                          className="block pl-3 py-1.5 text-xs xl:text-sm text-gray-600 hover:text-primary-blue transition-all duration-300 relative before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:w-1 before:h-1 before:rounded-full before:bg-gray-400 before:transition-all before:duration-300 hover:before:bg-primary-blue hover:before:w-1.5"
                                        >
                                          {subLink.name}
                                        </a>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}

                        {key === 'Solutions' && value.ebook && (
                          <div className="pl-3 xl:pl-4 border-l border-[#0052CC]/10">
                            <div className="bg-white/80 backdrop-blur-sm p-4 xl:p-6 rounded-lg border border-[#0052CC]/10 hover:shadow-sm transition-all duration-300">
                              <div className="mb-2 rounded-md overflow-hidden relative " style={{ height: '160px' }}>
                               
                                 <Image
                                                    src="/images/download-report-cover.jpeg"
                                                    alt="State of Pentesting Report 2025"
                                                    fill
                                  className="w-full h-full object-contain transition-transform duration-500 hover:scale-105"
                                                  />
                                <div className="absolute inset-0 bg-gradient-to-t from-transparent to-blue-500/5 pointer-events-none"></div>
                              </div>
                              <h3 className="text-base xl:text-lg font-semibold text-tertiary-blue mb-2 leading-relaxed">
                                {value.ebook.title}
                              </h3>
                              <p className="text-xs xl:text-sm text-gray-600 mb-3 xl:mb-4 leading-relaxed">
                                {value.ebook.description}
                              </p>
                              <a
                                href={value.ebook.url}
                                className="inline-flex items-center text-primary-blue font-medium hover:text-tertiary-blue transition-all duration-300 text-xs xl:text-sm"
                              >
                                {value.ebook.cta}
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                </svg>
                              </a>
                            </div>
                          </div>
                        )}

                        {key === 'Solutions' && value.ebook && (
                          <>
                            <div className="absolute left-1/4 top-0 bottom-0 w-[1px] bg-gradient-to-b from-transparent via-[#0052CC]/20 to-transparent"></div>
                            <div className="absolute left-2/4 top-0 bottom-0 w-[1px] bg-gradient-to-b from-transparent via-[#0052CC]/20 to-transparent"></div>
                            <div className="absolute left-3/4 top-0 bottom-0 w-[1px] bg-gradient-to-b from-transparent via-[#0052CC]/20 to-transparent"></div>
                          </>
                        )}
                        {value.categories.length === 2 && !(key === 'Solutions' && value.ebook) && (
                          <div className="absolute left-1/2 top-0 bottom-0 w-[1px] bg-gradient-to-b from-transparent via-[#0052CC]/20 to-transparent transform -translate-x-1/2"></div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </li>
          ))}
        </ul>

        <div className="hidden lg:flex items-center space-x-3 xl:space-x-5 md:mr-2 lg:mr-4 xl:mr-8 2xl:mr-16">
          <Link
            href="https://app.capturethebug.xyz/login"
            className="text-primary-dark hover:text-primary-blue transition-all duration-300 text-sm xl:text-base whitespace-nowrap relative before:absolute before:bottom-0 before:left-0 before:right-0 before:h-[1px] before:bg-primary-blue before:origin-left before:scale-x-0 hover:before:scale-x-100 before:transition-transform before:duration-300"
          >
            Log in
          </Link>
          <Button
            href="/Request-Demo"
            variant="primary"
            size="md"
            className="text-sm xl:text-base whitespace-nowrap"
          >
            Request a demo
          </Button>
        </div>
      </nav>
      <hr className="relative z-0 mt-1 border-t border-[#E9EFFD]" />

      {mobileMenuOpen && (
        <div className="lg:hidden fixed inset-0 z-40 bg-black bg-opacity-50 backdrop-blur-sm transition-all duration-300" onClick={closeMobileMenu} />
      )}

      {mobileMenuOpen && (
        <div className="lg:hidden bg-white shadow-xl z-50 fixed inset-x-0 top-0 h-full overflow-y-auto transition-all duration-300">
          <div className="flex items-center justify-between px-4 py-4 border-b border-[#E9EFFD]">
            <Link href="/" onClick={closeMobileMenu}>
              <Image
                src={"/images/dark_logo.png"}
                alt="Logo"
                width={144}
                height={36}
                className="w-20 sm:w-24 h-auto transition-transform duration-300 hover:scale-105"
              />
            </Link>
            <button
              onClick={closeMobileMenu}
              className="p-2 rounded-md hover:bg-gray-100 transition-all duration-300"
              aria-label="Close mobile menu"
            >
              <XMarkIcon className="h-5 w-5 sm:h-6 sm:w-6 text-gray-700" />
            </button>
          </div>

          <div className="px-4 py-4 sm:py-6 bg-gradient-to-b from-[#0052CC]/5 to-white">
            <ul className="space-y-1 sm:space-y-2">
              {Object.entries(menuItems).map(([key, value]) => (
                <li key={key} className="border-b border-[#E9EFFD] pb-2">
                  {value.noDropdown ? (
                    <Link
                      href={value.url}
                      onClick={closeMobileMenu}
                      className={`block py-3 text-base sm:text-lg font-medium transition-all duration-300 ${
                        isNavItemActive(key)
                          ? 'text-blue-600 bg-blue-50 px-3 rounded-md'
                          : 'text-tertiary-blue hover:text-primary-blue'
                      }`}
                    >
                      {value.title}
                    </Link>
                  ) : (
                    <>
                      <button
                        onClick={() =>
                          expandedMobileMenu === key
                            ? setExpandedMobileMenu(null)
                            : setExpandedMobileMenu(key)
                        }
                        className={`w-full text-left flex justify-between items-center py-3 text-base sm:text-lg font-medium transition-all duration-300 ${
                          isNavItemActive(key)
                            ? 'text-blue-600 bg-blue-50 px-3 rounded-md'
                            : 'text-tertiary-blue hover:text-primary-blue'
                        }`}
                      >
                        <span>{value.title}</span>
                        {expandedMobileMenu === key ? (
                          <ChevronUpIcon className="h-5 w-5 text-primary-blue transition-transform duration-300" />
                        ) : (
                          <ChevronDownIcon className={`h-5 w-5 transition-transform duration-300 ${
                            isNavItemActive(key) ? 'text-primary-blue' : 'text-gray-500'
                          }`} />
                        )}
                      </button>
                      {expandedMobileMenu === key && value.categories && (
                        <div className="pl-3 sm:pl-4 pb-3 sm:pb-4 space-y-3 sm:space-y-4">
                          {value.categories.map((category, idx) => (
                            <div key={idx} className="space-y-2 sm:space-y-3">
                              <div className="mb-2">
                                <p className="text-base font-bold text-tertiary-blue uppercase tracking-wider">
                                  {category.title}
                                </p>
                              </div>
                              <div className="space-y-1.5 sm:space-y-2 pl-3 sm:pl-4">
                                {category.links && category.links.map((link, linkIndex) => (
                                  <div key={linkIndex} className="space-y-1.5 sm:space-y-2">
                                    <Link
                                      href={link.url}
                                      onClick={closeMobileMenu}
                                      className="block py-2 text-sm sm:text-base text-primary-blue hover:text-secondary-blue transition-all duration-300 flex justify-between items-center hover:bg-[#0052CC]/10 hover:shadow-sm rounded-md px-2 group relative overflow-hidden"
                                      {...(link.external && { target: "_blank", rel: "noopener noreferrer" })}
                                    >
                                      <span className="relative z-10">{link.name}</span>
                                      <ChevronRightIcon className="h-4 w-4 text-gray-300 transition-all duration-300 transform group-hover:text-primary-blue group-hover:translate-x-1 relative z-10" />
                                      <span className="absolute inset-0 w-0 h-full bg-[#0052CC]/5 transform origin-left transition-all duration-300 ease-out group-hover:w-full"></span>
                                    </Link>
                                    {link.subMenu && (
                                      <div className="pl-3 sm:pl-4 space-y-1">
                                        {link.subMenu.map((subLink, subLinkIndex) => (
                                          <Link
                                            key={subLinkIndex}
                                            href={subLink.url}
                                            onClick={closeMobileMenu}
                                            className="block py-1 text-xs sm:text-sm text-gray-600 hover:text-primary-blue transition-all duration-300 group relative pl-3"
                                          >
                                            <span className="relative z-10">{subLink.name}</span>
                                            <span className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-1 rounded-full bg-gray-400 group-hover:bg-primary-blue group-hover:w-1.5 transition-all duration-300"></span>
                                          </Link>
                                        ))}
                                      </div>
                                    )}
                                  </div>
                                ))}
                              </div>
                            </div>
                          ))}
                           
                          {key === 'Solutions' && value.ebook && (
                            <div className="mt-4 sm:mt-6 pt-4 sm:pt-6 border-t border-[#0052CC]/20">
                              <div className="bg-gradient-to-br from-[#0052CC]/5 to-[#0052CC]/10 p-4 sm:p-5 rounded-lg border border-[#0052CC]/10">
                                <div className="mb-3 rounded-md overflow-hidden relative h-20 sm:h-24">
                                  <Image
                                    src="/images/download-report-cover.jpeg"
                                    alt="State of Pentesting Report 2025"
                                    fill
                                    className="w-full h-full object-contain transition-transform duration-500 hover:scale-105"
                                  />
                                  <div className="absolute inset-0 bg-gradient-to-t from-transparent to-blue-500/5 pointer-events-none"></div>
                                </div>
                                <h3 className="text-sm sm:text-base font-semibold text-tertiary-blue mb-2 leading-relaxed">
                                  {value.ebook.title}
                                </h3>
                                <p className="text-xs sm:text-sm text-gray-600 mb-2 leading-relaxed">
                                  {value.ebook.description}
                                </p>
                                <Link
                                  href={value.ebook.url}
                                  onClick={closeMobileMenu}
                                  className="inline-flex items-center text-primary-blue font-medium hover:text-secondary-blue transition-all duration-300 text-xs sm:text-sm group"
                                >
                                  {value.ebook.cta}
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 sm:h-3.5 sm:w-3.5 ml-1 transition-transform duration-300 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                  </svg>
                                </Link>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </>
                  )}
                </li>
              ))}
            </ul>

            <div className="pt-4 sm:pt-6 mt-4 sm:mt-6 border-t border-[#0052CC]/10 space-y-3 sm:space-y-4">
              <Button
                href="https://app.capturethebug.xyz/login"
                variant="secondary"
                fullWidth={true}
                className="text-sm sm:text-base"
                onClick={closeMobileMenu}
              >
                Log in
              </Button>
              <Button
                href="/Request-Demo"
                variant="primary"
                fullWidth={true}
                className="text-sm sm:text-base"
                onClick={closeMobileMenu}
              >
                Request a demo
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}