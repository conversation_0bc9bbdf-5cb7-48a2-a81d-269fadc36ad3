"use client";
import { useState } from "react";
import { 
  Zap, 
  Shield, 
  Eye, 
  CheckCircle, 
  Users, 
  Database,
  ArrowRight
} from "lucide-react";
import { motion } from "framer-motion";
import Button from "../../../common/buttons/Button";
import Image from 'next/image';

export default function TabsComponent() {
  const [activeTab, setActiveTab] = useState(0);

  const images = [
    "/images/reports.svg",
    "/images/Integration.svg",
    "/images/Remediation.svg", 
  ];

  const tabs = [
    {
      id: 0,
      title: "Enterprise-ready reporting",
      heading: "Deliver trust with audit-grade reports your stakeholders expect",
      description: "Get structured, standards-aligned vulnerability reports designed for enterprise procurement, audits, and security reviews.",
      features: [
        {
          title: "Mapped to compliance frameworks",
          description: "Capture The Bug reports are mapped to ISO 27001, SOC 2, GDPR, CIS, HIPAA, and PCI DSS controls-making them easy to plug into your compliance workflows and security questionnaires. Show exactly how issues impact controls and how remediation closes the gap.",
          icon: CheckCircle
        },
        {
          title: "Audit-friendly evidence",
          description: "Each finding includes detailed technical evidence, proof-of-exploit, affected assets, and fix guidance-enabling faster remediation and reducing friction during due diligence, renewals, or assessments.",
          icon: Database
        }
      ],
      buttonText: "Get started"
    },
    {
      id: 1,
      title: "Engineering-led remediation",
      heading: "Move from finding to fixing-on your terms, in your tools",
      description: "Modern dev teams need more than reports. We deliver issues as actionable workflows built for velocity.",
      features: [
        {
          title: "Integrated with your SDLC",
          description: "Create, assign, and track fixes directly from GitHub, GitLab, or Jira. Findings are grouped by service and enriched with reproducible steps so engineers don't waste time reproducing the issue.",
          icon: Users
        },
        {
          title: "Dev-first fix recommendations",
          description: "Each issue comes with context-aware fix advice developers can trust-no jargon, no guesswork. Cut remediation time, reduce security debt, and empower your team to own security.",
          icon: Zap
        }
      ],
      buttonText: "Get started"
    },
    {
      id: 2,
      title: "Built for enterprise scale",
      heading: "Security maturity starts with the right foundation",
      description: "Scale your pentesting program with the flexibility and support your business demands.",
      features: [
        {
          title: "Custom security workflows",
          description: "From SSO/SAML, audit trails, and role-based access to custom test scopes and SLAs, Capture The Bug adapts to your environment and grows with your team.",
          icon: Shield
        },
        {
          title: "Dedicated support & governance",
          description: "Get a dedicated customer success team, quarterly testing plans, and guidance aligned to your risk profile and regulatory requirements. Built for teams who need clarity, speed, and confidence.",
          icon: Eye
        }
      ],
      buttonText: "Get started"
    }
  ];

  const currentTab = tabs[activeTab];

  const renderContent = (tab) => (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-8"
    >
      <div>
        <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
          {tab.heading.split(' ').map((word, index) => (
            <span key={index} className={word === 'automation' || word === 'platform' || word === 'management' || word === 'reviews' ? 'text-blue-600' : ''}>
              {word}{' '}
            </span>
          ))}
        </h1>
        <p className="text-base md:text-lg text-gray-600 leading-relaxed">
          {tab.description}
        </p>
      </div>

      {/* Features */}
      <div className="space-y-6">
        {tab.features.map((feature, index) => {
          const IconComponent = feature.icon;
          return (
            <motion.div 
              key={index} 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 * (index + 1) }}
              className="flex gap-4"
            >
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <IconComponent className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600  leading-relaxed">
                  {feature.description}
                </p>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* CTA Button */}
      <div className="pt-4">
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Button
            href="/Request-Demo"
            variant="primary"
            size="lg"
            rightIcon={<ArrowRight className="w-4 h-4" />}
          >
            {tab.buttonText}
          </Button>
        </motion.div>
      </div>
    </motion.div>
  );

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="max-w-7xl mx-auto p-4 lg:p-8"
    >
      {/* Tab Navigation - Hidden on mobile, visible on tablet+ */}
      <div className="hidden md:flex justify-center mb-12">
        <div className="flex flex-wrap gap-2 bg-gray-100 p-1 rounded-full w-fit">
          {tabs.map((tab, index) => (
            <Button
              key={tab.id}
              onClick={() => setActiveTab(index)}
              variant={activeTab === index ? "primary" : "ghost"}
              size="md"
              className={`rounded-full whitespace-nowrap ${
                activeTab !== index && "text-gray-600 hover:text-gray-900"
              }`}
            >
              {tab.title}
            </Button>
          ))}
        </div>
      </div>

      {/* Mobile View - All 3 tabs displayed vertically */}
      <div className="md:hidden space-y-12">
        {tabs.slice(0, 3).map((tab, index) => (
          <motion.div 
            key={tab.id} 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 * index }}
            className="space-y-8"
          >
            {/* Image */}
            <div className="relative">
              <Image 
                src={images[index]} 
                alt={`Tab ${index + 1} visual`} 
                className="w-full h-auto rounded-2xl border-gray-400 border-5 shadow-md"
                width={400}
                height={300}
              />
            </div>
            {/* Content */}
            <div>
              {renderContent(tab)}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Desktop/Tablet View - Tab-based content */}
      <div className="hidden md:block">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Left Side - Image */}
          <motion.div 
            key={activeTab}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="relative order-1 lg:order-1"
          >
            <Image 
              src={images[activeTab]} 
              alt={`${currentTab.title} visual`} 
              className="w-full h-auto rounded-2xl"
              width={400}
              height={300}
            />
          </motion.div>

          {/* Right Side - Content */}
          <div className="order-2 lg:order-2">
            {renderContent(currentTab)}
          </div>
        </div>
      </div>
    </motion.div>
  );
}