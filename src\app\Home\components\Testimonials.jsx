"use client";

import React, { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';


const testimonialsData = [
  {
    id: 1,
    quote: "Capture The Bug has efficiently and affordably helped us meet our cybersecurity goals. Their tailored solutions and proactive approach have fortified our defenses, providing peace of mind. The real-time bug reports and their dedicated assistance ensure we are vigilant against cyber threats.",
    author: "<PERSON>",
    title: "Chief Operating Officer",
    company: "PARTLY",
    logoUrl: "/images/partly_logo.png",
    logoAlt: "PARTLY Logo",
    rating: 5,
    highlightedPhrase: "fortified our defenses",
    keyMetric: {
      value: "24/7",
      label: "Real-time vigilance"
    }
  },
  {
    id: 2,
    quote: "As a leading Kubernetes company, we understand the importance of securing our data and systems. We engage Capture The Bug's pentesting as a service platform for black box penetration testing. Their ethical hackers provided a thorough security assessment, with clear and concise reporting that included actionable recommendations. We highly recommend their platform for any organization looking to conduct comprehensive penetration testing.",
    author: "<PERSON>",
    title: "Sr. Director of Engineering",
    company: "Rafay Systems",
    logoUrl: "/images/rafay_logo.png",
    logoAlt: "Rafay Systems Logo",
    rating: 5,
    highlightedPhrase: "thorough security assessment",
    keyMetric: {
      value: "100%",
      label: "Comprehensive coverage"
    }
  },
  {
    id: 3,
    quote: "The team at Capture The Bug have been amazing and super easy to work with. In reality, security testing is ongoing, and needs to be effective yet cost efficient. I love the CTB platform format over traditional pen testing, not sure I could go back!",
    author: "Lorraine Guerin",
    title: "CPO",
    company: "Yabble",
    logoUrl: "/images/yabble_logo.png",
    logoAlt: "Yabble Logo",
    rating: 5,
    highlightedPhrase: "super easy to work with",
    keyMetric: {
      value: "Cost",
      label: "Efficient security"
    }
  },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.07,
      delayChildren: 0.2
    }
  }
};

const cardVariants = {
  hidden: (direction) => ({
    x: direction > 0 ? 200 : -200,
    opacity: 0,
    scale: 0.8,
  }),
  visible: {
    x: 0,
    opacity: 1,
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 60,
      damping: 20,
      duration: 0.8
    }
  },
  exit: (direction) => ({
    x: direction < 0 ? 200 : -200,
    opacity: 0,
    scale: 0.8,
    transition: {
      type: "spring",
      stiffness: 60,
      damping: 20,
      duration: 0.6
    }
  })
};

const quoteVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      delay: 0.3,
      duration: 0.6
    }
  }
};

const authorVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      delay: 0.5,
      duration: 0.6
    }
  }
};

const metricVariants = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      delay: 0.7,
      type: "spring",
      stiffness: 100,
      damping: 15
    }
  }
};

const logoVariants = {
  hidden: { opacity: 0, y: 30, scale: 0.8 },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      delay: 0.4,
      type: "spring",
      stiffness: 100,
      damping: 15
    }
  }
};

const dotVariants = {
  inactive: { scale: 0.7, opacity: 0.5 },
  active: { 
    scale: 1, 
    opacity: 1,
    transition: { duration: 0.3 } 
  }
};

export default function Testimonials() {
  const [[currentIndex, direction], setCurrentIndex] = useState([0, 0]);
  const [autoplay, setAutoplay] = useState(true);
  
  const paginate = useCallback((newDirection) => {
    let newIndex = currentIndex + newDirection;
    if (newIndex < 0) {
      newIndex = testimonialsData.length - 1;
    } else if (newIndex >= testimonialsData.length) {
      newIndex = 0;
    }
    setCurrentIndex([newIndex, newDirection]);
  }, [currentIndex]);
  
  // Handle automatic slideshow
  useEffect(() => {
    let interval;
    if (autoplay) {
      interval = setInterval(() => {
        paginate(1);
      }, 8000);
    }
    
    return () => clearInterval(interval);
  }, [autoplay, paginate]);

  const goToSlide = (slideIndex) => {
    const newDirection = slideIndex > currentIndex ? 1 : -1;
    setCurrentIndex([slideIndex, newDirection]);
  };

  const activeTestimonial = testimonialsData[currentIndex];
  
  // Create stars array based on rating
  const stars = Array.from({ length: 5 }, (_, i) => i < activeTestimonial.rating);

  // Custom highlight function for quotes
  const highlightQuote = (quote, phrase) => {
    if (!phrase) return quote;
    
    const parts = quote.split(new RegExp(`(${phrase})`, 'gi'));
    return parts.map((part, i) => 
      part.toLowerCase() === phrase.toLowerCase() ? 
        <span key={i} className="text-ctb-green-50 font-medium">{part}</span> : 
        part
    );
  };

  return (
    <section className="w-full py-20 sm:py-24 md:py-28 bg-gradient-to-b from-tertiary-blue to-secondary-blue relative overflow-hidden">
      {/* Premium background elements */}
      <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] bg-repeat opacity-[0.03]"></div>
      <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary-blue/20 to-transparent"></div>
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary-blue/20 to-transparent"></div>
      
      {/* Decorative elements */}
      <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-blue/10 rounded-full blur-3xl"></div>
      <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-primary-blue/10 rounded-full blur-3xl"></div>
      
      <div className="max-w-screen-2xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-1.5 mb-4 rounded-full bg-white/10 text-ctb-green-50 text-sm font-medium tracking-wide">TRUSTED BY INDUSTRY LEADERS</span>
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-[500] text-white mb-6">
            What our clients are saying
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-blue/60 via-ctb-green-50/60 to-primary-blue/60 mx-auto rounded-full"></div>
        </div>
        
        {/* Main Testimonial Carousel */}
        <div className="md:px-4 lg:px-8 xl:px-16 relative min-h-[450px] sm:min-h-[400px] md:min-h-[380px]">
          <AnimatePresence initial={false} custom={direction} mode="wait">
            <motion.div
              key={currentIndex}
              custom={direction}
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="w-full"
            >
              <motion.div 
                variants={cardVariants}
                custom={direction}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="relative grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12"
                onHoverStart={() => setAutoplay(false)}
                onHoverEnd={() => setAutoplay(true)}
              >
                {/* Quote Section */}
                <div className="lg:col-span-2 bg-gradient-to-br from-white/10 to-white/[0.02] backdrop-blur-sm p-8 sm:p-10 rounded-2xl border border-white/10 shadow-xl relative overflow-hidden group">
                  {/* Quote background elements */}
                  <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary-blue/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-1000"></div>
                  <div className="absolute -right-20 -top-20 w-40 h-40 bg-ctb-green-50/5 rounded-full blur-2xl"></div>
                  
                  {/* Large quote mark */}
                  <div className="absolute -top-1 -left-1 text-white/10 text-9xl font-serif">&ldquo;</div>
                  
                  {/* Star rating */}
                  <div className="flex mb-6 items-center relative z-10">
                    {stars.map((filled, i) => (
                      <svg key={i} className={`w-5 h-5 ${filled ? 'text-ctb-green-50' : 'text-white/30'} mr-1`} fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                  
                  {/* Quote text */}
                  <motion.blockquote 
                    variants={quoteVariants}
                    className="text-xl sm:text-2xl font-[400] leading-relaxed text-white mb-8 relative z-10"
                  >
                    {highlightQuote(activeTestimonial.quote, activeTestimonial.highlightedPhrase)}
                  </motion.blockquote>
                  
                  {/* Author information */}
                  <motion.div 
                    variants={authorVariants}
                    className="flex items-center"
                  >
                    <div className="flex-shrink-0 mr-4">
                      {activeTestimonial.avatarUrl ? (
                        <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-white/30">
                          <Image
                            src={activeTestimonial.avatarUrl}
                            alt={activeTestimonial.author}
                            width={48}
                            height={48}
                            className="object-cover"
                          />
                        </div>
                      ) : (
                        <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center text-white text-xl font-semibold">
                          {activeTestimonial.author.charAt(0)}
                        </div>
                      )}
                    </div>
                    <div>
                      <div className="text-white font-semibold text-lg">{activeTestimonial.author}</div>
                      <div className="text-white/70 text-sm">{activeTestimonial.title}, <span className="text-ctb-green-50">{activeTestimonial.company}</span></div>
                    </div>
                  </motion.div>
                </div>
                
                {/* Company & Metric Section */}
                <div className="lg:col-span-1 flex flex-col">
                  {/* Logo Card */}
                  <motion.div 
                    variants={logoVariants}
                    className="bg-white/5 backdrop-blur-sm p-6 rounded-2xl border border-white/10 shadow-lg flex items-center justify-center mb-6 h-48"
                  >
                    <div className="relative w-full h-full flex items-center justify-center">
                      <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-xl"></div>
                      <div className="relative z-10 w-40 h-40 flex items-center justify-center">
                        {activeTestimonial.logoUrl && (
                          <div className="bg-white rounded-xl p-4 shadow-sm flex items-center justify-center w-32 h-20">
                            <Image
                              src={activeTestimonial.logoUrl}
                              alt={activeTestimonial.logoAlt}
                              width={120}
                              height={60}
                              className="object-contain max-w-full max-h-full"
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                  
                  {/* Key Metric Card */}
                  <motion.div 
                    variants={metricVariants}
                    className="bg-gradient-to-br from-ctb-green-50/20 to-primary-blue/20 backdrop-blur-sm p-6 rounded-2xl border border-white/10 shadow-lg flex-1 flex flex-col items-center justify-center text-center"
                  >
                    <div className="text-ctb-green-50 text-4xl sm:text-5xl font-bold mb-2">
                      {activeTestimonial.keyMetric.value}
                    </div>
                    <div className="text-white text-sm sm:text-base">
                      {activeTestimonial.keyMetric.label}
                    </div>
                  </motion.div>
                </div>
              </motion.div>
            </motion.div>
          </AnimatePresence>
          
          {/* Premium Navigation Controls */}
          <div className="mt-12 flex items-center justify-center space-x-3">
            <button
              onClick={() => paginate(-1)}
              aria-label="Previous testimonial"
              className="w-12 h-12 flex items-center justify-center border border-white/20 rounded-full bg-white/5 hover:bg-white/10 transition-colors duration-300 group"
            >
              <svg className="w-5 h-5 text-white transition-transform duration-300 transform group-hover:-translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            
            <div className="flex items-center space-x-3 mx-4">
              {testimonialsData.map((_, index) => (
                <motion.button
                  key={index}
                  onClick={() => goToSlide(index)}
                  aria-label={`Go to testimonial ${index + 1}`}
                  className="w-3 h-3 rounded-full bg-white/30 focus:outline-none"
                  variants={dotVariants}
                  animate={currentIndex === index ? "active" : "inactive"}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                  style={{
                    background: currentIndex === index ? 
                      "linear-gradient(to right, #58CC02, #0835A7)" : 
                      "rgba(255, 255, 255, 0.3)"
                  }}
                />
              ))}
            </div>
            
            <button
              onClick={() => paginate(1)}
              aria-label="Next testimonial"
              className="w-12 h-12 flex items-center justify-center border border-white/20 rounded-full bg-white/5 hover:bg-white/10 transition-colors duration-300 group"
            >
              <svg className="w-5 h-5 text-white transition-transform duration-300 transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
