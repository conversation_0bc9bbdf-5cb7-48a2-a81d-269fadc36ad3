"use client";
import React from 'react';
import Link from 'next/link';
import { ArrowRight, ExternalLink, Shield, Code, Smartphone, Globe, Building, Users, DollarSign } from 'lucide-react';

/**
 * Strategic Internal Linking Component
 * Provides contextual, keyword-rich internal links for better SEO and user experience
 */

// Define strategic link categories with SEO-optimized anchor text
export const linkCategories = {
  services: {
    'web-app': {
      url: '/Services/Web-app',
      anchor: 'web application penetration testing',
      keywords: ['web app security', 'OWASP testing', 'application security'],
      icon: <Code className="w-4 h-4" />
    },
    'mobile-app': {
      url: '/Services/Mobile-app',
      anchor: 'mobile application security testing',
      keywords: ['mobile app security', 'iOS testing', 'Android security'],
      icon: <Smartphone className="w-4 h-4" />
    },
    'network-pentest': {
      url: '/Services/Network-pentest',
      anchor: 'network penetration testing',
      keywords: ['network security', 'infrastructure testing', 'network assessment'],
      icon: <Globe className="w-4 h-4" />
    },
    'api-pentest': {
      url: '/Services/API-pentest',
      anchor: 'API penetration testing',
      keywords: ['API security', 'REST API testing', 'GraphQL security'],
      icon: <Code className="w-4 h-4" />
    }
  },
  
  solutions: {
    'startup': {
      url: '/Company-size/Start-Up',
      anchor: 'startup cybersecurity solutions',
      keywords: ['startup security', 'early-stage security', 'affordable pentesting']
    },
    'growing-team': {
      url: '/Company-size/Growing-Team',
      anchor: 'growing team security solutions',
      keywords: ['scaling security', 'team security', 'growth-stage security']
    },
    'enterprise': {
      url: '/Company-size/Enterprise',
      anchor: 'enterprise cybersecurity solutions',
      keywords: ['enterprise security', 'large-scale security', 'enterprise pentesting']
    }
  },

  product: {
    'ptaas': {
      url: '/Product/Penetration-Testing',
      anchor: 'Penetration Testing as a Service (PTaaS)',
      keywords: ['PTaaS platform', 'continuous security', 'automated pentesting']
    },
    'pricing': {
      url: '/Pricing',
      anchor: 'transparent penetration testing pricing',
      keywords: ['pentesting cost', 'security pricing', 'affordable security']
    }
  },

  resources: {
    'blog': {
      url: '/Blogs',
      anchor: 'cybersecurity insights and penetration testing guidance',
      keywords: ['security blog', 'pentesting articles', 'cybersecurity news']
    },
    'case-studies': {
      url: '/Case-Studies',
      anchor: 'penetration testing case studies',
      keywords: ['security success stories', 'pentesting results', 'client testimonials']
    },
    'customers': {
      url: '/Customers',
      anchor: 'customer success stories',
      keywords: ['client testimonials', 'security testimonials', 'customer reviews']
    }
  },

  locations: {
    'nz': {
      url: '/Locations/nz',
      anchor: 'New Zealand penetration testing services',
      keywords: ['NZ cybersecurity', 'Auckland security', 'Wellington pentesting']
    },
    'au': {
      url: '/Locations/au',
      anchor: 'Australia penetration testing services',
      keywords: ['Australian cybersecurity', 'Sydney security', 'Melbourne pentesting']
    },
    'us': {
      url: '/Locations/us',
      anchor: 'United States penetration testing services',
      keywords: ['US cybersecurity', 'American security', 'USA pentesting']
    }
  }
};

// Strategic link suggestions based on page context
export const getContextualLinks = (pageType, currentPath) => {
  const suggestions = [];

  switch (pageType) {
    case 'blog':
      suggestions.push(
        linkCategories.product.ptaas,
        linkCategories.services['web-app'],
        linkCategories.resources['case-studies'],
        linkCategories.product.pricing
      );
      break;
      
    case 'service':
      if (currentPath.includes('Web-app')) {
        suggestions.push(
          linkCategories.services['api-pentest'],
          linkCategories.services['mobile-app'],
          linkCategories.product.ptaas,
          linkCategories.solutions.startup
        );
      } else if (currentPath.includes('API-pentest')) {
        suggestions.push(
          linkCategories.services['web-app'],
          linkCategories.services['network-pentest'],
          linkCategories.product.ptaas,
          linkCategories.solutions['growing-team']
        );
      }
      break;
      
    case 'company-size':
      suggestions.push(
        linkCategories.product.ptaas,
        linkCategories.product.pricing,
        linkCategories.resources.customers,
        linkCategories.services['web-app']
      );
      break;
      
    case 'location':
      suggestions.push(
        linkCategories.services['web-app'],
        linkCategories.product.ptaas,
        linkCategories.resources['case-studies'],
        linkCategories.solutions.enterprise
      );
      break;
      
    default:
      suggestions.push(
        linkCategories.product.ptaas,
        linkCategories.services['web-app'],
        linkCategories.product.pricing,
        linkCategories.resources.blog
      );
  }

  return suggestions.slice(0, 4); // Limit to 4 suggestions
};

// Component for inline contextual links
export const InlineLink = ({ 
  href, 
  children, 
  className = "", 
  external = false,
  variant = "default" 
}) => {
  const baseClasses = "transition-all duration-300 font-medium";
  
  const variants = {
    default: "text-blue-600 hover:text-blue-800 underline hover:no-underline",
    subtle: "text-gray-700 hover:text-blue-600 border-b border-gray-300 hover:border-blue-600",
    button: "inline-flex items-center gap-1 text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 px-3 py-1 rounded-md text-sm",
    cta: "inline-flex items-center gap-2 text-white bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg font-semibold"
  };

  const linkClasses = `${baseClasses} ${variants[variant]} ${className}`;

  if (external) {
    return (
      <a 
        href={href} 
        className={linkClasses}
        target="_blank" 
        rel="noopener noreferrer"
      >
        {children}
        {variant === 'button' || variant === 'cta' ? (
          <ExternalLink className="w-3 h-3" />
        ) : null}
      </a>
    );
  }

  return (
    <Link href={href} className={linkClasses}>
      {children}
      {variant === 'button' || variant === 'cta' ? (
        <ArrowRight className="w-3 h-3" />
      ) : null}
    </Link>
  );
};

// Component for related links section
export const RelatedLinksSection = ({ 
  title = "Related Services", 
  links, 
  className = "",
  variant = "grid" 
}) => {
  if (!links || links.length === 0) return null;

  return (
    <div className={`bg-gray-50 rounded-lg p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
        <Shield className="w-5 h-5 text-blue-600" />
        {title}
      </h3>
      
      {variant === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {links.map((link, index) => (
            <InlineLink 
              key={index}
              href={link.url}
              variant="button"
              className="justify-start"
            >
              {link.icon && <span className="text-blue-500">{link.icon}</span>}
              {link.anchor}
            </InlineLink>
          ))}
        </div>
      ) : (
        <div className="flex flex-wrap gap-2">
          {links.map((link, index) => (
            <InlineLink 
              key={index}
              href={link.url}
              variant="subtle"
              className="text-sm"
            >
              {link.anchor}
            </InlineLink>
          ))}
        </div>
      )}
    </div>
  );
};

// Component for contextual CTA with internal links
export const ContextualCTA = ({ 
  title, 
  description, 
  primaryLink, 
  secondaryLinks = [],
  className = "" 
}) => {
  return (
    <div className={`bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 ${className}`}>
      <div className="text-center">
        <h3 className="text-xl font-bold text-gray-900 mb-2">{title}</h3>
        <p className="text-gray-600 mb-4">{description}</p>
        
        {primaryLink && (
          <div className="mb-4">
            <InlineLink 
              href={primaryLink.url}
              variant="cta"
              className="inline-flex"
            >
              {primaryLink.text}
            </InlineLink>
          </div>
        )}
        
        {secondaryLinks.length > 0 && (
          <div className="flex flex-wrap justify-center gap-3">
            {secondaryLinks.map((link, index) => (
              <InlineLink 
                key={index}
                href={link.url}
                variant="button"
                className="text-sm"
              >
                {link.text}
              </InlineLink>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

const InternalLinkingStrategy = {
  InlineLink,
  RelatedLinksSection,
  ContextualCTA,
  linkCategories,
  getContextualLinks
};

export default InternalLinkingStrategy;
