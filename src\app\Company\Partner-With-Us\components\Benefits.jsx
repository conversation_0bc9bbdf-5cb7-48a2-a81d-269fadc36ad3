import React from 'react';

export default function PartnerProgram() {
  return (
    <section className="w-full min-h-screen bg-gray-50 flex items-center justify-center py-20 px-4 lg:px-24">
      <div className="w-full max-w-7xl">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-3xl lg:text-5xl font-bold text-[#010D2C] mb-6">
            Why You Should Partner With Us
          </h1>
          <p className="text-lg text-gray-600 max-w-4xl mx-auto">
            We&apos;re looking for partners who want to deliver modern, real-world penetration testing and <a href="/Remediation" className="text-[#010D2C] hover:underline font-semibold">remediation support</a> - without building it all from scratch. Whether you&apos;re a small cybersecurity consultancy, vCISO, or managed service provider, here&apos;s how Capture The Bug helps you grow.
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {/* Offer Pentesting Without Hiring */}
          <div className="bg-white rounded-lg p-8 shadow-sm text-center hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-pointer group">
            <div className="mb-6 flex justify-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors duration-300">
                <svg className="w-8 h-8 text-green-600 group-hover:text-green-700 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd"/>
                  <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"/>
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-semibold text-[#010D2C] mb-4 group-hover:text-[#062575] transition-colors duration-300"> Offer Pentesting Without Hiring a Team</h3>
            <p className="text-gray-600">
              Skip the hiring and training. Use our platform and expert testing team to deliver high-quality pentests to your clients. See our <a href="/Pricing" className="text-[#010D2C] hover:underline font-semibold">flexible pricing options</a> for partners.
            </p>
          </div>

          {/* Expand Security Services */}
          <div className="bg-white rounded-lg p-8 shadow-sm text-center hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-pointer group">
            <div className="mb-6 flex justify-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors duration-300">
                <svg className="w-8 h-8 text-green-600 group-hover:text-green-700 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clipRule="evenodd"/>
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-semibold text-[#010D2C] mb-4 group-hover:text-[#062575] transition-colors duration-300"> Expand Your Security Services</h3>
            <p className="text-gray-600">
              Add Vulnerability Assessment and Penetration Testing, Bug Bounty & VDP Management, and <a href="/Remediation" className="text-[#010D2C] hover:underline font-semibold">remediation workflows</a> to your existing security stack - without the operational overhead. Explore our comprehensive <a href="/Services/Network-pentest" className="text-[#010D2C] hover:underline font-semibold">network security testing</a> services.
            </p>
          </div>

          {/* Stay at Center of Client Relationship */}
          <div className="bg-white rounded-lg p-8 shadow-sm text-center hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-pointer group">
            <div className="mb-6 flex justify-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors duration-300">
                <svg className="w-8 h-8 text-green-600 group-hover:text-green-700 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"/>
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-semibold text-[#010D2C] mb-4 group-hover:text-[#062575] transition-colors duration-300"> Stay at the Center of the Client Relationship</h3>
            <p className="text-gray-600">
              We support you behind the scenes. You own the client, we handle the testing and platform - ensuring a seamless experience end-to-end.
            </p>
          </div>
        </div>

        {/* Bottom Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Built for Compliance */}
          <div className="bg-white rounded-lg p-8 shadow-sm text-center hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-pointer group">
            <div className="mb-6 flex justify-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors duration-300">
                <svg className="w-8 h-8 text-green-600 group-hover:text-green-700 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-semibold text-[#010D2C] mb-4 group-hover:text-[#062575] transition-colors duration-300"> Built for Compliance-Driven Environments</h3>
            <p className="text-gray-600">
              Deliver testing that maps to SOC 2, ISO 27001, PCI DSS, HIPAA, and more - all from a single, easy-to-use PTaaS dashboard.
            </p>
          </div>

          {/* Designed for vCISOs */}
          <div className="bg-white rounded-lg p-8 shadow-sm text-center hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-pointer group">
            <div className="mb-6 flex justify-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors duration-300">
                <svg className="w-8 h-8 text-green-600 group-hover:text-green-700 transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd"/>
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-semibold text-[#010D2C] mb-4 group-hover:text-[#062575] transition-colors duration-300"> Designed for vCISOs, MSPs & Consultancies</h3>
            <p className="text-gray-600">
              Whether you advise, manage, or secure - our platform lets you plug in pentesting expertise where your team ends and our platform begins. Ready to get started? <a href="/Company/Contact-Us" className="text-[#010D2C] hover:underline font-semibold">Contact us</a> to discuss partnership opportunities.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}