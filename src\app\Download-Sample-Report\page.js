import DownloadSample from './downloadsample';
 
export const metadata = {
  title: "Capture The Bug | Download Sample Pentest Report",
  description: "Preview Capture The Bug’s expert-led pentest reporting. Download a sample of our 2025 Offensive Security Report to see insights, format, and value.",
keywords: "sample pentest report, offensive security report sample, PTaaS report download, cybersecurity reporting example, Capture The Bug sample, 2025 pentest insights, penetration testing documentation",  robots: "index, follow",
  icons: {
    icon: '/favicon.ico',
  },
  openGraph: {
    title: "Capture The Bug | Download Sample Pentest Report",
    type: "website",
    url: "https://capturethebug.xyz/Download-Sample-Report",
    description: "Download a sample of the 2025 Offensive Security Report. See how Capture The Bug delivers clear, actionable PTaaS reporting.",
    images: "https://postimg.cc/BPsB4YhP",
  },
  twitter: {
    card: 'summary_large_image',
    title: "Capture The Bug | Sample Pentest Report",
    description: "See what a professional PTaaS report looks like. Download a sample report and preview Capture The Bug’s 2025 pentesting insights.",
    images: "https://postimg.cc/BPsB4YhP",
  }
};


export default function Page() {
  return <DownloadSample />;
}