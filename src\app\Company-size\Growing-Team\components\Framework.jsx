"use client";
import { useState } from "react";
import { 
  Zap, 
  Shield, 
  Eye, 
  CheckCircle, 
  Users, 
  Database,
  ArrowRight,
  Clock
} from "lucide-react";
import Button from "../../../common/buttons/Button";
import { motion } from "framer-motion";
import Image from 'next/image';

export default function TabsComponent() {
  const [activeTab, setActiveTab] = useState(0);

  const images = [
    "/images/SOC.svg",
    "/images/continuous.svg", 
    "/images/growing3.svg",
    "/images/Remediation.svg", 
  ];

  const tabs = [
    {
      id: 0,
      title: "Continuous security",
      heading: "Efficiency through continuous security",
      description: "Turn pentesting from a once-a-year headache into a real-time advantage. Automate manual pentest processes with developer-first workflows, real-time findings, and compliance-ready reporting-all from a single dashboard.",
      features: [
        {
          title: "Real-Time Pentest Reports",
          description: "See findings as they're discovered-so your devs can triage issues instantly, reduce exposure windows, and accelerate secure delivery. All findings are severity-tagged and mapped to CVSS and compliance controls like SOC 2, ISO 27001, GDPR, CIS, and HIPAA.",
          icon: Eye
        },
        {
          title: "Seamless Dev Workflows",
          description: "We integrate natively with tools like Slack, Jira, GitHub, and more-so you can route findings to the right teams, assign remediation owners, and track fixes from one centralized dashboard.",
          icon: Users
        }
      ],
      buttonText: "Get started"
    },
    {
      id: 1,
      title: "Proactive security testing",
      heading: "Proactive security you don't have to chase",
      description: "Ship faster and sleep better with real-time risk coverage that scales with your product. CTB turns every release into an opportunity to reduce your threat surface-not expand it.",
      features: [
        {
          title: "Always-on risk monitoring",
          description: "Capture The Bug's platform lets you launch pentests continuously, not annually-so security isn't delayed behind red tape. Review live results, track risk by business priority, and stay a step ahead.",
          icon: Shield
        },
        {
          title: "Smart remediation insights",
          description: "Get a clear list of exploitable vulnerabilities, mapped to affected features and dev teams. Easily assign, track, and resolve issues-before they're noticed by attackers or auditors.",
          icon: Zap
        }
      ],
      buttonText: "Start testing"
    },
    {
      id: 2,
      title: "Audit-ready reporting",
      heading: "Comprehensive reports. Compliance-mapped. CISO-approved.",
      description: "Security teams shouldn't have to choose between real insights and audit requirements. Capture The Bug gives you both-deep manual findings mapped to global frameworks like ISO 27001, SOC 2, and OWASP.",
      features: [
        {
          title: "Pentest reports your auditors will love",
          description: "Each finding comes with severity scoring, replication steps, remediation guidance, and direct mapping to relevant compliance controls. Easily exportable for auditors, boards, and partners.",
          icon: CheckCircle
        },
        {
          title: "Built to align with how you're measured",
          description: "Whether you're prepping for a SOC 2 audit, ISO 27001 certification, or investor due diligence, CTB's reports are tailored to help you prove real security posture, not just tick boxes.",
          icon: Database
        }
      ],
      buttonText: "View reports"
    },
    {
      id: 3,
      title: "Remediation Built-In",
      heading: "Developer-friendly remediation that scales",
      description: "Modern teams need more than just pentest reports-they need real-time, actionable vulnerability fixes. Capture The Bug delivers security findings with step-by-step remediation guidance, mapped to compliance frameworks like ISO 27001 and SOC 2, so you can resolve issues fast and stay audit-ready.",
      features: [
        {
          title: "Security fixes devs can ship",
          description: "Each finding includes clear replication steps, exploit context, and code-safe fixes. No generic advice. No ticket ping-pong.",
          icon: Clock
        },
        {
          title: "Works where your devs work",
          description: "CTB integrates with Slack, Jira, and GitHub to keep remediation aligned with your sprint velocity-without breaking your CI/CD.",
          icon: Users
        }
      ],
      buttonText: "Fix vulnerabilities"
    }
  ];

  const currentTab = tabs[activeTab];

  const renderContent = (tab) => (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
          {tab.heading.split(' ').map((word, index) => (
            <span key={index} className={word === 'automation' || word === 'platform' || word === 'management' || word === 'reviews' ? 'text-blue-600' : ''}>
              {word}{' '}
            </span>
          ))}
        </h1>
        <p className="text-base md:text-lg text-gray-600 leading-relaxed">
          {tab.description}
        </p>
      </div>

      {/* Features */}
      <div className="space-y-6">
        {tab.features.map((feature, index) => {
          const IconComponent = feature.icon;
          return (
            <div key={index} className="flex gap-4">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <IconComponent className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            </div>
          );
        })}
      </div>
 
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto p-4 lg:p-14">
      {/* Tab Navigation - Hidden on mobile, visible on tablet+ */}
      <div className="hidden md:flex justify-center mb-12">
        <div className="flex flex-wrap gap-2 bg-gray-100 p-1 rounded-full w-fit">
          {tabs.map((tab, index) => (
            <Button
              key={tab.id}
              onClick={() => setActiveTab(index)}
              variant={activeTab === index ? "primary" : "ghost"}
              size="md"
              className={`rounded-full whitespace-nowrap ${
                activeTab !== index && "text-gray-600 hover:text-gray-900"
              }`}
            >
              {tab.title}
            </Button>
          ))}
        </div>
      </div>

      {/* Mobile View - All 3 tabs displayed vertically */}
      <div className="md:hidden space-y-12">
        {tabs.slice(0, 3).map((tab, index) => (
          <div key={tab.id} className="space-y-8">
            {/* Image */}
            <div className="relative">
              <Image 
                src={images[index]} 
                alt={`${tab.title} illustration`}
                className="w-full h-auto rounded-2xl"
                width={400}
                height={300}
              />
            </div>
            {/* Content */}
            <div>
              {renderContent(tab)}
            </div>
          </div>
        ))}
      </div>

      {/* Desktop/Tablet View - Tab-based content */}
      <div className="hidden md:block">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Left Side - Image */}
          <div className="relative order-1 lg:order-1">
            <Image 
              src={images[activeTab]} 
              alt={`${currentTab.title} illustration`}
              className="w-full h-auto rounded-2xl"
              width={400}
              height={300}
            />
          </div>

          {/* Right Side - Content */}
          <div className="order-2 lg:order-2">
            {renderContent(currentTab)}
          </div>
        </div>
      </div>
    </div>
  );
}