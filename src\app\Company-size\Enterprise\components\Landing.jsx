"use client";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { ArrowRight } from "lucide-react";
import { motion } from "framer-motion";
import Button from "../../../common/buttons/Button";

export default function LandingSection() {
  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="bg-[#F8F5F7] "
    >
      {/* Main 50/50 Split Section */}
      <div className="container mx-auto px-4 md:px-24">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center min-h-[600px] py-16 sm:py-24 lg:py-26">
          
          {/* Left Content - 50% */}
          <motion.div 
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="flex flex-col gap-6 max-w-xl"
          >
            <motion.h1 
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-3xl md:text-4xl lg:text-5xl font-bold text-ctb-blue-150 leading-tight"
            >
              Elevate Security at <span className="text-primary-blue">Enterprise Scale</span>
            </motion.h1>
            <motion.p 
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-[#7B6B9C] text-base md:text-lg font-semibold leading-relaxed"
            >
              Capture The Bug&apos;s PTaaS platform delivers scalable, enterprise-grade penetration testing with unmatched precision and control.
            </motion.p>
            <motion.p 
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.35 }}
              className="text-[#7B6B9C] text-base md:text-lg leading-relaxed"
            >
              Whether you&apos;re operating across global teams, managing compliance for multiple frameworks, or securing thousands of endpoints-CTB gives you the tools to continuously identify, remediate, and report vulnerabilities at scale. Our platform scales with your enterprise needs while maintaining the precision and control you demand.
            </motion.p>
            <motion.div 
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="flex justify-start"
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  href="/Request-Demo"
                  variant="success"
                  size="lg"
                  rightIcon={
                    <motion.div
                      animate={{ x: [0, 4, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <ArrowRight className="h-5 w-5" />
                    </motion.div>
                  }
                >
                  Request a demo
                </Button>
              </motion.div>
            </motion.div>
          </motion.div>
          
          {/* Right Image - 50% */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="flex justify-center lg:justify-end"
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="w-full max-w-[600px]"
            >
              <Image
                src="/images/enterprise-dash.svg"
                alt="Enterprise Security Dashboard"
                width={1120}  
                height={980} 
                className="w-full h-auto rounded-3xl"  
                priority
                quality={90} 
              />
            </motion.div>
          </motion.div>
        </div>
        <div className="w-full bg-[#F8F5F7]   pb-8   sm:pb-12  md:pb-14">
        <div className="container mx-auto px-4 md:px-12 flex flex-col items-center text-center">
          <p className="text-[#7B6B9C] text-sm sm:text-base md:text-lg leading-relaxed mb-8">
            Trusted by leading companies worldwide
          </p>
          <div className="flex justify-center items-center gap-4 sm:gap-8 md:gap-12 lg:gap-16 flex-wrap px-8 -mt-6 sm:px-0">
            <div className="w-[140px] aspect-[2] rounded-full overflow-hidden">
              <Image src="/images/Zebpay_logo.png" alt="Zebpay logo" className="w-full h-full object-contain" width={140} height={70} />
            </div>
            <div className="w-[140px] aspect-[2] rounded-full overflow-hidden">
              <Image src="/images/lawvu.jpg" alt="lawvu logo" className="w-full h-full object-contain" width={140} height={70} />
            </div> 
            <div className="w-[140px] aspect-[2] rounded-full overflow-hidden">
              <Image src="/images/EROAD_Logo.webp" alt="eroad logo" className="w-full h-full object-contain" width={140} height={70} />
            </div>
            <div className="w-[140px]   rounded-3xl overflow-hidden">
              <Image src="/images/paysauce_logo.png" alt="paysauce logo" className="w-full h-full mt-6 object-contain" width={140} height={70} />
            </div>
            <div className="w-[140px] aspect-[2] rounded-full overflow-hidden">
              <Image src="/images/blackpearl_logo.png" alt="blackpearl logo" className="w-full h-full object-contain" width={140} height={70} />
            </div>
          </div>
        </div>
      </div>
      </div> 
    </motion.div>
  );
}