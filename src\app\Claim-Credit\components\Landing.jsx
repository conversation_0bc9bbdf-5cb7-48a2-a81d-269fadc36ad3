"use client";
import React, { useState } from "react";
import Image from "next/image";

const Landing = () => {
  const [formData, setFormData] = useState({
    businessEmail: "",
    firstName: "",
    lastName: "",
    jobTitle: "",
    company: "",
    agreeToTerms: false,
  });

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    // Add your form submission logic here
  };

  return (
    <div className="md:mt-20 mt-10 min-h-[calc(100vh-64px)] bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container px-4 sm:px-8 md:px-12 lg:pl-24 lg:pr-4 py-8 md:py-12 lg:py-20">
        <div className="grid lg:grid-cols-2 gap-6 md:gap-12 lg:gap-32 items-start max-w-[1800px] mx-auto lg:ml-auto">
          
          {/* Left Column - Content */}
          <div className="space-y-8 lg:space-y-10 lg:px-8 lg:sticky lg:top-24 max-w-[800px] mx-auto lg:mx-0">
            <div className="space-y-6">
              <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-4xl font-[600]" style={{ color: '#062575' }}>
                Download the State of<br />
                Pentesting Report 2025.
              </h1>
              <p className="text-base md:text-md text-black">
                What you don&apos;t know can put you at risk. Most security teams trust their defenses, until pentesting reveals the gaps. The 2025 State of Pentesting Report uncovers the vulnerabilities and shows you how to build a more effective security program.
              </p>
              <div className="pt-2">
                <h2 className="text-2xl font-bold mb-4" style={{ color: '#062575' }}>Inside the report, you&apos;ll learn:</h2>
                <ul className="space-y-4">
                  <li className="flex items-start space-x-3">
                    <span className="mt-1" style={{ color: '#60a5fa' }}>
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" /></svg>
                    </span>
                    <span className="text-md text-black">Why even as 98% of organizations are integrating genAI into their products, security investments aren&apos;t keeping pace.</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <span className="mt-1" style={{ color: '#60a5fa' }}>
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" /></svg>
                    </span>
                    <span className="text-md text-black">How pentesting data challenges assumptions and exposes critical, exploitable vulnerabilities that automated scans miss.</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <span className="mt-1" style={{ color: '#60a5fa' }}>
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" /></svg>
                    </span>
                    <span className="text-md text-black">The biggest concerns of security leaders, how teams are prioritizing threats, and how your security measures up.</span>
                  </li>
                </ul>
                <p className="mt-6 text-md font-bold" style={{ color: '#027bfc' }}>Get the expert insights you need to protect your organization.</p>
              </div>
            </div>
          </div>

          {/* Right Column - Form */}
          <div className="w-full max-w-full md:max-w-[900px] lg:max-w-[700px] mx-auto lg:mx-0 lg:ml-32">
            <div className="bg-white rounded-2xl shadow-xl p-4 sm:p-6 lg:p-8">
              {/* Report Cover Image */}
              <div className="mb-6 text-center">
                <div className="relative w-48 h-64 mx-auto">
                  <Image
                    src="/images/rep-cov.png"
                    alt="State of Pentesting Report 2025"
                    fill
                    className="object-contain"
                  />
                </div>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4 md:space-y-6">
                {/* Business Email */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Business Email *
                  </label>
                  <input
                    type="email"
                    name="businessEmail"
                    value={formData.businessEmail}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                {/* Name Fields */}
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      First Name *
                    </label>
                    <input
                      type="text"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all"
                      placeholder="First Name"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all"
                      placeholder="Last Name"
                      required
                    />
                  </div>
                </div>

                {/* Job Title */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Job Title *
                  </label>
                  <input
                    type="text"
                    name="jobTitle"
                    value={formData.jobTitle}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all"
                    placeholder="Job Title"
                    required
                  />
                </div>

                {/* Company */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Company *
                  </label>
                  <input
                    type="text"
                    name="company"
                    value={formData.company}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all"
                    placeholder="Company Name"
                    required
                  />
                </div>

                {/* Privacy Notice */}
                <div className="text-xs text-slate-500">
                  <p>
                    Cobalt needs the contact information you provide to us to contact you about our products and services. 
                    You may unsubscribe from these communications at anytime. For information on how to unsubscribe, as well as our privacy practices and commitment to 
                    protecting your privacy, check out our{" "}
                    <a href="https://capturethebug.xyz/Useful-Links/Privacy-Policy" className="text-[#027bfc] hover:underline">Privacy Policy</a>.
                  </p>
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  className="w-full bg-[#062575] hover:bg-[#027bfc] text-white font-semibold py-4 px-6 rounded-lg transition-colors"
                >
                  DOWNLOAD THE REPORT
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Landing;
