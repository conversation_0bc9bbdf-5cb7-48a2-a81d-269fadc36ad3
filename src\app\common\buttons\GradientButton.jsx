import PropTypes from 'prop-types';
import clsx from 'clsx';
import Link from 'next/link';

const GradientButton = ({ 
  children, 
  onClick, 
  href, 
  icon, 
  className,
  size = 'md',
  variant = 'primary'
}) => {
  const baseClasses = clsx(
    "inline-flex items-center justify-center gap-2 rounded-lg transition-all duration-300 transform hover:-translate-y-0.5 font-semibold relative overflow-hidden group",
    {
      // Size variants
      'px-3 py-1.5 text-sm': size === 'sm',
      'px-5 py-2.5 text-base': size === 'md',
      'px-7 py-3.5 text-lg': size === 'lg',
      
      // Style variants
      'bg-secondary-blue hover:bg-primary-blue text-white shadow-md hover:shadow-lg': variant === 'primary',
      'bg-white hover:bg-white/95 text-primary-blue border border-primary-blue/20 shadow-sm hover:shadow-md': variant === 'secondary',
      'bg-transparent border-2 border-secondary-blue text-secondary-blue': variant === 'outline',
    },
    className
  );

  // Effects for different variants
  const renderEffect = () => {
    if (variant === 'primary') {
      return <span className="absolute inset-0 w-full h-full bg-white/10 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></span>;
    } else if (variant === 'secondary') {
      return <span className="absolute inset-0 w-full h-full bg-primary-blue/5 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 ease-out"></span>;
    } else if (variant === 'outline') {
      return <span className="absolute inset-0 w-full h-full bg-secondary-blue/10 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-500 ease-out"></span>;
    }
    return null;
  };

  // If href is provided, render as Link
  if (href) {
    return (
      <Link href={href} className={baseClasses}>
        {renderEffect()}
        <span className="relative z-10">{children}</span>
        {icon && <span className="relative z-10 transition-transform group-hover:translate-x-1 duration-300">{icon}</span>}
      </Link>
    );
  }

  // Otherwise render as button
  return (
    <button 
      className={baseClasses}
      onClick={onClick}
      aria-label="gradient button"
    >
      {renderEffect()}
      <span className="relative z-10">{children}</span>
      {icon && <span className="relative z-10 transition-transform group-hover:translate-x-1 duration-300">{icon}</span>}
    </button>
  );
};

GradientButton.propTypes = {
  children: PropTypes.node.isRequired,
  onClick: PropTypes.func,
  href: PropTypes.string,
  icon: PropTypes.node,
  className: PropTypes.string,
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  variant: PropTypes.oneOf(['primary', 'secondary', 'outline']),
};

export default GradientButton; 