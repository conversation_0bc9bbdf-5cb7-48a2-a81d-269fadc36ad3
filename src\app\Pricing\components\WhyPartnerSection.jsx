"use client";

import React from 'react';
import Button from "@/app/common/buttons/Button";
import Link from 'next/link';

const benefits = [
  {
    id: "predictable-cost",
    title: "Predictable annual cost",
    icon: (
      <svg className="w-8 h-8 text-ctb-green-50" fill="currentColor" viewBox="0 0 24 24">
        <path d="M7,15H9C9,16.08 10.37,17 12,17C13.63,17 15,16.08 15,15C15,13.9 13.96,13.5 11.76,12.97C9.64,12.44 7,11.78 7,9C7,7.21 8.47,5.69 10.5,5.18V3H13.5V5.18C15.53,5.69 17,7.21 17,9H15C15,7.92 13.63,7 12,7C10.37,7 9,7.92 9,9C9,10.1 10.04,10.5 12.24,11.03C14.36,11.56 17,12.22 17,15C17,16.79 15.53,18.31 13.5,18.82V21H10.5V18.82C8.47,18.31 7,16.79 7,15Z"/>
      </svg>
    )
  },
  {
    id: "continuous-coverage",
    title: "Continuous coverage",
    icon: (
      <svg className="w-8 h-8 text-ctb-green-50" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"/>
      </svg>
    )
  },
  {
    id: "expert-team",
    title: "Expert pentester team",
    icon: (
      <svg className="w-8 h-8 text-ctb-green-50" fill="currentColor" viewBox="0 0 24 24">
        <path d="M16,4C16.88,4 17.67,4.5 18,5.26L19,7H20A2,2 0 0,1 22,9V20A2,2 0 0,1 20,22H4A2,2 0 0,1 2,20V9A2,2 0 0,1 4,7H5L6,5.26C6.33,4.5 7.12,4 8,4H16M16.5,6H7.5L7,7H17L16.5,6M12,8A3,3 0 0,0 9,11A3,3 0 0,0 12,14A3,3 0 0,0 15,11A3,3 0 0,0 12,8M12,15.5C10,15.5 6,16.5 6,18.5V20H18V18.5C18,16.5 14,15.5 12,15.5Z"/>
      </svg>
    )
  },
  {
    id: "realtime-dashboard",
    title: "Real-time dashboard",
    icon: (
      <svg className="w-8 h-8 text-ctb-green-50" fill="currentColor" viewBox="0 0 24 24">
        <path d="M3,3H21A2,2 0 0,1 23,5V19A2,2 0 0,1 21,21H3A2,2 0 0,1 1,19V5A2,2 0 0,1 3,3M3,5V19H21V5H3M5,7H19V9H5V7M5,11H19V13H5V11M5,15H19V17H5V15Z"/>
      </svg>
    )
  },
  {
    id: "fast-retest",
    title: "Fast retest cycles",
    icon: (
      <svg className="w-8 h-8 text-ctb-green-50" fill="currentColor" viewBox="0 0 24 24">
        <path d="M13,2.03C17.73,2.5 21.5,6.25 21.95,11C22.5,16.5 18.5,21.38 13,21.93V19.93C16.64,19.5 19.5,16.61 19.96,12.97C20.5,8.58 17.39,4.59 13,4.05V2.03M11,2.06V4.05C9.6,4.26 8.26,4.84 7.17,5.79L5.67,4.26C7.19,2.95 9.05,2.27 11,2.06M4.26,5.67L5.79,7.17C4.84,8.26 4.26,9.6 4.05,11H2.06C2.27,9.05 2.95,7.19 4.26,5.67M2.05,13H4.05C4.26,14.4 4.84,15.74 5.79,16.83L4.26,18.33C2.95,16.81 2.27,14.95 2.05,13M5.67,19.74L7.17,18.21C8.26,19.16 9.6,19.74 11,19.95V21.94C9.05,21.73 7.19,21.05 5.67,19.74M12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9Z"/>
      </svg>
    )
  },
  {
    id: "transparent-pricing",
    title: "Transparent day-based pricing",
    icon: (
      <svg className="w-8 h-8 text-ctb-green-50" fill="currentColor" viewBox="0 0 24 24">
        <path d="M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V5H19V19M17,12V7H7V17H17V12M15,10V15H9V10H15Z"/>
      </svg>
    )
  }
];

export default function WhyPartnerSection() {
  return (
    <section className="w-full bg-tertiary-blue py-16 sm:py-20 md:py-24">
      <div className="max-w-screen-2xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
        {/* Header Section */}
        <div className="mb-12 sm:mb-16 md:mb-20">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-[500] text-center mb-4 leading-tight text-white">
            Why Partner with Capture The Bug?
          </h2>
        </div>

        {/* Benefits Grid */}
        <div className="md:px-4 lg:px-8 xl:px-16">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-6 items-stretch w-full">
            {benefits.map((benefit) => (
              <div
                key={benefit.id}
                className="group relative flex flex-col justify-center items-center bg-white/10 backdrop-blur-sm rounded-2xl border border-ctb-green-50/20 shadow-[0_10px_35px_-15px_rgba(0,0,0,0.2)] hover:shadow-[0_25px_65px_-12px_rgba(0,0,0,0.3)] transition-all duration-500 ease-in-out p-6 md:p-8 min-h-[160px] overflow-hidden transform hover:-translate-y-1 hover:border-ctb-green-50/40"
              >
                {/* Premium Background Effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-ctb-green-50/[0.03] via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                
                {/* Top right corner decorative element */}
                <div className="absolute -top-12 -right-12 w-20 h-20 rounded-full bg-gradient-to-br from-ctb-green-50/10 to-ctb-green-50/5 group-hover:from-ctb-green-50/20 group-hover:to-ctb-green-50/10 transition-colors duration-700" />
                
                {/* Animated glow effect */}
                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[120%] h-[120%] rounded-full bg-ctb-green-50/5 blur-3xl opacity-0 group-hover:opacity-30 scale-0 group-hover:scale-100 transition-all duration-700" />
                
                {/* Left border accent */}
                <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-ctb-green-50/20 via-ctb-green-50/40 to-ctb-green-50/20 transform origin-bottom scale-y-0 group-hover:scale-y-100 transition-transform duration-500 ease-out rounded-l-xl" />
              
                {/* Content Container with relative positioning */}
                <div className="relative z-10 flex flex-col items-center text-center">
                  {/* Icon Container */}
                  <div className="p-3 bg-gradient-to-br from-ctb-green-50/5 to-ctb-green-50/10 rounded-xl group-hover:from-ctb-green-50/10 group-hover:to-ctb-green-50/20 transition-colors duration-500 shadow-sm group-hover:shadow-md relative overflow-hidden mb-4">
                    {/* Icon Background Animation */}
                    <div className="absolute inset-0 bg-gradient-to-tr from-ctb-green-50/0 to-ctb-green-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-700" />
                    
                    {/* Animated dots in icon background */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700 overflow-hidden">
                      {[...Array(3)].map((_, i) => (
                        <div 
                          key={i} 
                          className="absolute w-0.5 h-0.5 rounded-full bg-ctb-green-50/30"
                          style={{
                            top: `${10 + (i * 30)}%`,
                            left: `${10 + (i * 30)}%`,
                            animation: `float${i} 3s infinite ease-in-out ${i * 0.5}s`
                          }}
                        />
                      ))}
                    </div>
                    
                    {/* The actual icon with enhanced transitions */}
                    {React.cloneElement(benefit.icon, { 
                      className: 'w-8 h-8 text-ctb-green-50 group-hover:text-ctb-green-50/90 transition-all duration-500 transform group-hover:scale-110 relative z-10' 
                    })}
                  </div>
                  
                  {/* Title with animated underline */}
                  <div className="overflow-hidden">
                    <h3 className="text-lg font-bold text-white group-hover:text-white/90 transition-colors duration-500 transform translate-y-0 group-hover:-translate-y-1">{benefit.title}</h3>
                    <div className="h-0.5 w-0 bg-gradient-to-r from-white to-white/80 group-hover:w-full transition-all duration-700 ease-out mt-1"></div>
                  </div>
                </div>
                
                {/* Subtle bottom accent line */}
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-ctb-green-50/80 to-ctb-green-50/40 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-700 ease-out rounded-b-xl opacity-60"></div>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Button */}
        <div className="mt-16 sm:mt-20 text-center">
          <Link 
            href="/Company/Contact-Us" 
            className="inline-flex items-center justify-center bg-ctb-green-50 hover:bg-ctb-green-50/90 text-tertiary-blue font-semibold px-10 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5 group relative overflow-hidden"
          >
            {/* Button background glow effect */}
            <span className="absolute inset-0 w-full h-full bg-tertiary-blue/10 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></span>
            
            <span className="relative z-10">Contact Sales</span>
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-5 w-5 ml-3 transform group-hover:translate-x-1 transition-transform duration-300" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </Link>
        </div>
      </div>
      
      {/* Add global animation keyframes */}
      <style jsx global>{`
        @keyframes float0 {
          0%, 100% { transform: translateY(0) translateX(0); }
          50% { transform: translateY(-5px) translateX(3px); }
        }
        @keyframes float1 {
          0%, 100% { transform: translateY(0) translateX(0); }
          50% { transform: translateY(5px) translateX(-3px); }
        }
        @keyframes float2 {
          0%, 100% { transform: translateY(0) translateX(0); }
          50% { transform: translateY(-7px) translateX(-2px); }
        }
      `}</style>
    </section>
  );
}
