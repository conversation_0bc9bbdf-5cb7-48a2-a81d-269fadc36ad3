"use client";
import React, { useState, useMemo } from "react";
// import Confetti from "react-confetti";

const data = [
  {
    target: "Web Application",
    userRole: "1 user role",
    pages: "40 pages",
    hours: 32,
    greyBox: 4900,
    blackBox: 3675,
  },
  {
    target: "Web Application",
    userRole: "1 user role",
    pages: "50 pages",
    hours: 48,
    greyBox: 7350,
    blackBox: 5500,
  },
  {
    target: "Web Application",
    userRole: "2 user roles",
    pages: "60 pages",
    hours: 72,
    greyBox: 11025,
    blackBox: 8300,
  },
  {
    target: "Web Application",
    userRole: "3 user roles",
    pages: "70 pages",
    hours: 88,
    greyBox: 13475,
    blackBox: 10500,
  },
  {
    target: "Web Application",
    userRole: "4 user roles",
    pages: "80 pages",
    hours: 104,
    greyBox: 15925,
    blackBox: 12000,
  },
  {
    target: "Web Application",
    userRole: "5 user roles",
    pages: "100 pages",
    hours: 120,
    greyBox: 18375,
    blackBox: 13900,
  },
  {
    target: "Web Application",
    userRole: "6 user roles",
    pages: "120 pages",
    hours: 136,
    greyBox: 20825,
    blackBox: 15900,
  },
  {
    target: "Web Application",
    userRole: "7 user roles",
    pages: "140 pages",
    hours: 152,
    greyBox: 23275,
    blackBox: 17500,
  },
  {
    target: "API",
    userRole: "1 user role",
    endpoints: "75 endpoints",
    hours: 32,
    greyBox: 4900,
    blackBox: 3675,
  },
  {
    target: "API",
    userRole: "1 user role",
    endpoints: "90 endpoints",
    hours: 48,
    greyBox: 7350,
    blackBox: 5500,
  },
  {
    target: "API",
    userRole: "2 user roles",
    endpoints: "120 endpoints",
    hours: 72,
    greyBox: 11025,
    blackBox: 8300,
  },
  {
    target: "API",
    userRole: "3 user roles",
    endpoints: "120 endpoints",
    hours: 88,
    greyBox: 13475,
    blackBox: 10500,
  },
  {
    target: "API",
    userRole: "4 user roles",
    endpoints: "135 endpoints",
    hours: 104,
    greyBox: 15925,
    blackBox: 12000,
  },
  {
    target: "API",
    userRole: "5 user roles",
    endpoints: "155 endpoints",
    hours: 120,
    greyBox: 18375,
    blackBox: 13900,
  },
  {
    target: "API",
    userRole: "6 user roles",
    endpoints: "175 endpoints",
    hours: 136,
    greyBox: 20825,
    blackBox: 15900,
  },
  {
    target: "API",
    userRole: "7 user roles",
    endpoints: "190 endpoints",
    hours: 152,
    greyBox: 23275,
    blackBox: 17500,
  },
  {
    target: "Mobile Application",
    userRole: "2 OS",
    screens: "25 screens",
    hours: 32,
    greyBox: 4900,
    blackBox: 3675,
  },
  {
    target: "Mobile Application",
    userRole: "2 OS",
    screens: "35 screens",
    hours: 48,
    greyBox: 7350,
    blackBox: 5500,
  },
  {
    target: "Mobile Application",
    userRole: "2 OS, 2 user roles",
    screens: "65 screens",
    hours: 72,
    greyBox: 11025,
    blackBox: 8300,
  },
  {
    target: "Mobile Application",
    userRole: "2 OS, 3 user roles",
    screens: "75 screens",
    hours: 88,
    greyBox: 13475,
    blackBox: 10500,
  },
  {
    target: "Mobile Application",
    userRole: "2 OS, 4 user roles",
    screens: "80 screens",
    hours: 104,
    greyBox: 15925,
    blackBox: 12000,
  },
  {
    target: "Mobile Application",
    userRole: "3 OS, 5 user roles",
    screens: "100 screens",
    hours: 120,
    greyBox: 18375,
    blackBox: 13900,
  },
  {
    target: "Mobile Application",
    userRole: "3 OS, 6 user roles",
    screens: "130 screens",
    hours: 136,
    greyBox: 20825,
    blackBox: 15900,
  },
  {
    target: "Mobile Application",
    userRole: "3 OS, 7 user roles",
    screens: "140 screens",
    hours: 152,
    greyBox: 23275,
    blackBox: 17500,
  },
  {
    target: "External Network",
    userRole: "50 IPs",
    hours: 32,
    greyBox: 4900,
    blackBox: 3675,
  },
  {
    target: "External Network",
    userRole: "100 IPs",
    hours: 48,
    greyBox: 7350,
    blackBox: 5500,
  },
  {
    target: "External Network",
    userRole: "125 IPs",
    hours: 72,
    greyBox: 11025,
    blackBox: 8300,
  },
  {
    target: "External Network",
    userRole: "150 IPs",
    hours: 88,
    greyBox: 13475,
    blackBox: 10500,
  },
  {
    target: "External Network",
    userRole: "200 IPs",
    hours: 104,
    greyBox: 15925,
    blackBox: 12000,
  },
  {
    target: "External Network",
    userRole: "225 IPs",
    hours: 120,
    greyBox: 18375,
    blackBox: 13900,
  },
  {
    target: "External Network",
    userRole: "250 IPs",
    hours: 136,
    greyBox: 20825,
    blackBox: 15900,
  },
  {
    target: "External Network",
    userRole: "275 IPs",
    hours: 152,
    greyBox: 23275,
    blackBox: 17500,
  },
  {
    target: "Internal Network",
    userRole: "50 IPs",
    hours: 32,
    greyBox: 4900,
    blackBox: 3675,
  },
  {
    target: "Internal Network",
    userRole: "100 IPs",
    hours: 48,
    greyBox: 7350,
    blackBox: 5500,
  },
  {
    target: "Internal Network",
    userRole: "125 IPs",
    hours: 72,
    greyBox: 11025,
    blackBox: 8300,
  },
  {
    target: "Internal Network",
    userRole: "150 IPs",
    hours: 88,
    greyBox: 13475,
    blackBox: 10500,
  },
  {
    target: "Internal Network",
    userRole: "200 IPs",
    hours: 104,
    greyBox: 15925,
    blackBox: 12000,
  },
  {
    target: "Internal Network",
    userRole: "225 IPs",
    hours: 120,
    greyBox: 18375,
    blackBox: 13900,
  },
  {
    target: "Internal Network",
    userRole: "250 IPs",
    hours: 136,
    greyBox: 20825,
    blackBox: 15900,
  },
  {
    target: "Internal Network",
    userRole: "275 IPs",
    hours: 152,
    greyBox: 23275,
    blackBox: 17500,
  },
  {
    target: "Cloud Configuration",
    userRole: "1 account, 50 services",
    hours: 32,
    greyBox: 4900,
    blackBox: 3675,
  },
  {
    target: "Cloud Configuration",
    userRole: "1 account, 60 services",
    hours: 48,
    greyBox: 7350,
    blackBox: 5500,
  },
  {
    target: "Cloud Configuration",
    userRole: "3 accounts, 80 services",
    hours: 72,
    greyBox: 11025,
    blackBox: 8300,
  },
  {
    target: "Cloud Configuration",
    userRole: "3 accounts, 90 services",
    hours: 88,
    greyBox: 13475,
    blackBox: 10500,
  },
  {
    target: "Cloud Configuration",
    userRole: "4 accounts, 110 services",
    hours: 104,
    greyBox: 15925,
    blackBox: 12000,
  },
  {
    target: "Cloud Configuration",
    userRole: "5 accounts, 120 services",
    hours: 120,
    greyBox: 18375,
    blackBox: 13900,
  },
  {
    target: "Cloud Configuration",
    userRole: "6 accounts, 180 services",
    hours: 136,
    greyBox: 20825,
    blackBox: 15900,
  },
  {
    target: "Cloud Configuration",
    userRole: "7 accounts, 190 services",
    hours: 152,
    greyBox: 23275,
    blackBox: 17500,
  },
];

const targetDescriptions = {
  "Web Application":
    "Ensure the security of your web applications by identifying and addressing vulnerabilities that could expose sensitive data or disrupt services.",
  API: "Protect your APIs from threats by evaluating security measures and uncovering potential weaknesses that could lead to data breaches.",
  "Mobile Application":
    "Mobile app penetration testing examines the security of your iOS or Android applications, uncovering potential weaknesses in the mobile ecosystem",
  "External Network":
    "Safeguard your network perimeter by detecting vulnerabilities that could be exploited by external attackers.",
  "Internal Network":
    "Examine your internal network for security gaps that could be exploited by insiders or malicious software.",
  "Cloud Configuration":
    "Secure your cloud infrastructure by identifying misconfigurations and vulnerabilities that could expose your resources to unauthorized access. ",
};

const StyledDropdown = ({ label, value, onChange, options, placeholder }) => (
  <div className="relative group">
    <label className="block mb-2 font-medium text-gray-700 pl-2">{label}:</label>
    <div className="relative flex items-center pl-2">
      <select
        value={value}
        onChange={onChange}
        className={`w-full p-3 pr-10 border rounded-lg appearance-none transition-all duration-200 ease-in-out
          ${value ? 'border-blue-500 bg-white' : 'border-gray-300 bg-gray-50'}
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
          hover:border-blue-400`}
      >
        <option value="">{placeholder}</option>
        {options.map((option) => (
          <option key={option} value={option}>
            {option}
          </option>
        ))}
      </select>
      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
        <div className="h-6 border-r border-gray-300 mr-2"></div>
        <svg
          className="fill-current text-gray-500 h-4 w-4 transition-transform duration-200 group-hover:scale-110"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
        >
          <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
        </svg>
      </div>
    </div>
    {value && (
      <div className="w-1 h-full bg-blue-500 absolute -left-1 top-0 bottom-0 rounded-full"></div>
    )}
  </div>
);

const DynamicCalculator = () => {
  const [testingType, setTestingType] = useState("");
  const [target, setTarget] = useState("");
  const [userRole, setUserRole] = useState("");
  const [scope, setScope] = useState("");
  const [previousPlanPrice, setPreviousPlanPrice] = useState("");

  const targets = useMemo(
    () => [...new Set(data.map((item) => item.target))],
    []
  );

  const userRoles = useMemo(() => {
    if (!target) return [];
    return [
      ...new Set(
        data
          .filter((item) => item.target === target)
          .map((item) => item.userRole)
      ),
    ];
  }, [target]);

  const scopes = useMemo(() => {
    if (!target || !userRole) return [];
    return [
      ...new Set(
        data
          .filter(
            (item) => item.target === target && item.userRole === userRole
          )
          .map((item) => item.pages || item.endpoints || item.screens)
      ),
    ].filter(Boolean);
  }, [target, userRole]);

  const result = useMemo(() => {
    if (!testingType || !target || !userRole) return null;

    let item;
    if (["Web Application", "API", "Mobile Application"].includes(target)) {
      if (!scope) return null;
      item = data.find(
        (item) =>
          item.target === target &&
          item.userRole === userRole &&
          (item.pages === scope ||
            item.endpoints === scope ||
            item.screens === scope)
      );
    } else {
      item = data.find(
        (item) => item.target === target && item.userRole === userRole
      );
    }

    if (!item) return null;
    const cost = testingType === "Grey Box" ? item.greyBox : item.blackBox;
    const discount =
      previousPlanPrice && previousPlanPrice !== ""
        ? parseFloat(previousPlanPrice) - cost
        : 0;
    const percentage =
      discount && discount !== 0
        ? ((discount / parseFloat(previousPlanPrice)) * 100).toFixed(2)
        : 0;
    return { hours: item.hours, cost, discount, percentage };
  }, [testingType, target, userRole, scope, previousPlanPrice]);

  const getUserRoleLabel = () => {
    switch (target) {
      case "Web Application":
      case "API":
        return "User Roles";
      case "Mobile Application":
        return "OS and User Roles";
      case "External Network":
      case "Internal Network":
        return "IPs";
      case "Cloud Configuration":
        return "Accounts and Services";
      default:
        return "User Role / Scope";
    }
  };

  const getScopeLabel = () => {
    switch (target) {
      case "Web Application":
        return "Pages";
      case "API":
        return "Endpoints";
      case "Mobile Application":
        return "Screens";
      default:
        return "Scope";
    }
  };

  const TargetDescription = ({ target, testingType, userRole, scope, previousPlanPrice, result }) => (
    <div className="mb-6 md:mt-0 mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
      <div className="font-bold text-xl text-gray-800 mb-2">{target}</div>
      <div className="text-gray-600 mb-4">{targetDescriptions[target]}</div>
      
      <div className="space-y-3">
        {testingType && (
          <div className="flex items-center text-sm text-gray-700">
            <span className="font-semibold w-40">Penetration Testing Type:</span>
            <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
              {testingType}
            </span>
          </div>
        )}
        
        {userRole && (
          <div className="flex items-center text-sm text-gray-700">
            <span className="font-semibold w-40">User Role / Scope:</span>
            <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm">
              {userRole}
            </span>
          </div>
        )}
        
        {scope && (
          <div className="flex items-center text-sm text-gray-700">
            <span className="font-semibold w-40">Scope:</span>
            <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm">
              {scope}
            </span>
          </div>
        )}
        
        {previousPlanPrice && (
          <div className="flex items-center text-sm text-gray-700">
            <span className="font-semibold w-40">Previous Vendor Quote:</span>
            <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm">
              USD ${previousPlanPrice}
            </span>
          </div>
        )}
      </div>
    </div>
  );
  

  return (
    <div className="w-full max-w-6xl mx-auto p-6 mt-10 mb-10">
      <div className="flex flex-col md:flex-row gap-8">
        <div className="w-full md:w-1/3 space-y-6 pr-6 border-r border-gray-200">
          <div>
            <h2 className="text-3xl font-bold mb-3 text-left text-blue-900">
              Pricing Calculator
            </h2>
            <div className="text-sm text-gray-600 leading-relaxed">
              Select the type of penetration test you need, followed by the asset you want assessed. This will provide you with an estimated cost. For a detailed quote, please click &apos;Schedule a Call,&apos; and one of our team members will contact you promptly.
            </div>
          </div>

          <div className="space-y-6">
            <StyledDropdown
              label="Type Of Penetration Testing"
              value={testingType}
              onChange={(e) => {
                setTestingType(e.target.value);
                setUserRole("");
                setScope("");
                setPreviousPlanPrice("");
              }}
              options={["Grey Box", "Black Box"]}
              placeholder="Choose Type"
            />

            <StyledDropdown
              label="Target Penetration Testing"
              value={target}
              onChange={(e) => {
                setTarget(e.target.value);
                setUserRole("");
                setScope("");
                setPreviousPlanPrice("");
              }}
              options={targets}
              placeholder="Choose Target"
            />

            {target && (
              <StyledDropdown
                label={getUserRoleLabel()}
                value={userRole}
                onChange={(e) => {
                  setUserRole(e.target.value);
                  setScope("");
                  setPreviousPlanPrice("");
                }}
                options={userRoles}
                placeholder={`Select ${getUserRoleLabel()}`}
              />
            )}
          </div>
        </div>

        <div className="w-full md:w-2/3 space-y-6">
          {target && (
            <TargetDescription
              target={target}
              testingType={testingType}
              userRole={userRole}
              scope={scope}
              previousPlanPrice={previousPlanPrice}
              result={result}
            />
          )}

          {userRole &&
            ["Web Application", "API", "Mobile Application"].includes(target) &&
            scopes.length > 0 && (
              <StyledDropdown
                label={getScopeLabel()}
                value={scope}
                onChange={(e) => {
                  setScope(e.target.value);
                  setPreviousPlanPrice("");
                }}
                options={scopes}
                placeholder={`Select ${getScopeLabel()}`}
              />
            )}

          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <label className="block mb-2 font-medium text-gray-700">
              Other Vendor Quote:
            </label>
            <input
              type="number"
              value={previousPlanPrice}
              onChange={(e) => setPreviousPlanPrice(e.target.value)}
              className="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              placeholder="Enter quote"
            />
          </div>

          <div className="p-6 rounded-lg border border-gray-200 shadow-sm">
            <div className="grid grid-cols-2 gap-6">
              <div className="p-4 bg-gray-50 rounded-lg">
                <p className="font-medium text-gray-600 mb-1">Estimated Cost</p>
                <p className="text-3xl font-bold text-blue-900">
                  USD ${result ? result.cost.toLocaleString() : 0}
                </p>
              </div>

              {result && result.discount !== null && (
                <>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <p className="font-medium text-gray-600 mb-1">Savings with Us</p>
                    <p
                      className={`text-3xl font-bold ${
                        result.discount < 0 ? "text-red-600" : "text-green-600"
                      }`}
                    >
                      USD ${result.discount.toLocaleString()}
                    </p>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <p className="font-medium text-gray-600 mb-1">Total Savings %</p>
                    <p
                      className={`text-3xl font-bold ${
                        result.discount < 0 ? "text-red-600" : "text-green-600"
                      }`}
                    >
                      {result.percentage}%
                    </p>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="mt-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
        <p className="text-sm text-gray-600">
          <strong className="text-gray-800">Disclaimer:</strong>
          The prices provided by this calculator are for general guidance only and should not be considered a final quote. Capture The Bug offers agile, on-demand pentesting services tailored to your unique needs. For an accurate quote, we recommend{" "}
          <a href="/Request-Demo" className="text-blue-600 hover:text-blue-800 font-medium underline">
            scheduling a scoping call
          </a>{" "}
          where we can understand your requirements in detail, evaluate the depth and complexity of the assessment, and ensure the pentest is designed to deliver maximum value. Our approach prioritizes real-world insights over automated scans, ensuring a mature and comprehensive security service.
        </p>
      </div>
    </div>
  );
};

export default DynamicCalculator;
