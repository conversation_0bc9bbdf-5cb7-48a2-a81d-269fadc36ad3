'use client';

import React, { useState } from 'react';
import { Plus, Minus } from 'lucide-react';

const FAQItem = ({ question, answer, isOpen, onToggle, uniqueId }) => (
  <div className="mb-4">
    <button
      onClick={onToggle}
      aria-expanded={isOpen}
      className="flex w-full justify-between items-center text-left bg-gray-100 p-4 rounded-lg transition-all"
    >
      <span className="text-sm md:text-lg font-medium text-gray-900">
        {question}
      </span>
      <span className="ml-4 shrink-0">
        {isOpen ? (
          <Minus className="h-5 w-5 text-[#1e83fb]" />
        ) : (
          <Plus className="h-5 w-5 text-[#1e83fb]" />
        )}
      </span>
    </button>

    <div
      className={`transition-all duration-300 ease-in-out overflow-hidden ${
        isOpen ? 'max-h-[1000px] opacity-100 py-4' : 'max-h-0 opacity-0 py-0'
      } bg-gray-50`}
    >
      <div className="px-4">
        <p className="text-sm md:text-base text-gray-700 leading-relaxed">{answer}</p>
      </div>
    </div>
  </div>
);

const FAQ = () => {
  const [openItem, setOpenItem] = useState(null);

  const handleToggle = (uniqueId) => {
    setOpenItem((prev) => (prev === uniqueId ? null : uniqueId));
  };

 const faqs = [
  {
    id: 'mobile-app-pt-definition',
    question: 'What is Mobile App Penetration Testing?',
    answer:
      'Mobile App Penetration Testing involves comprehensive security assessment of iOS and Android applications, analyzing both the app itself and its backend APIs to identify vulnerabilities that could be exploited by attackers.',
  },
  {
    id: 'ios-vs-android-testing',
    question: 'Do you test both iOS and Android applications?',
    answer:
      'Yes, we provide comprehensive testing for both iOS and Android applications. Our testing covers platform-specific vulnerabilities, business logic flaws, and backend API security for both platforms.',
  },
  {
    id: 'mobile-ptaas-difference',
    question: "How does Capture The Bug's PTaaS approach benefit Mobile App testing?",
    answer:
      "Our PTaaS solution enables continuous mobile app security testing throughout your development lifecycle. Test new builds, push findings to your dev tools, and get real-time collaboration with our security experts.",
  },
  {
    id: 'mobile-vulnerability-types',
    question: 'What kind of vulnerabilities can be identified in mobile app tests?',
    answer:
      'We identify insecure data storage, weak cryptography, authentication bypasses, injection flaws, insecure communication, business logic vulnerabilities, and platform-specific security issues following OWASP Mobile Top 10.',
  },
  {
    id: 'testing-methodology',
    question: 'Do you use automated tools or manual techniques for mobile testing?',
    answer:
      'Our mobile app assessments combine automated static and dynamic analysis tools with extensive manual testing techniques, including reverse engineering, runtime manipulation, and real-world attack scenarios.',
  },
  {
    id: 'mobile-standards-followed',
    question: 'Which standards do you follow for Mobile App Penetration Testing?',
    answer:
      'We follow OWASP Mobile Security Testing Guide (MSTG), OWASP Mobile Top 10, and platform-specific security guidelines from Apple and Google to ensure comprehensive testing coverage.',
  },
  {
    id: 'api-backend-testing',
    question: 'Do you test the backend APIs along with the mobile app?',
    answer:
      'Absolutely. We test both the mobile front-end and backend APIs as a complete system, identifying issues like auth bypass, insecure data flow, token manipulation, and server-side vulnerabilities.',
  },
  {
    id: 'app-preparation',
    question: 'How should we prepare our mobile app for penetration testing?',
    answer:
      'You can provide APK/IPA files, test credentials, API documentation, and any specific testing scenarios. Our platform supports easy file upload and our team will guide you through the preparation process.',
  },
  {
    id: 'compliance-mobile',
    question: 'Does mobile app testing help with compliance requirements?',
    answer:
      'Yes, our mobile app testing supports compliance with standards like SOC 2, ISO 27001, HIPAA, and PCI DSS by identifying security gaps and providing audit-ready reports with remediation guidance.',
  },
  {
    id: 'developer-collaboration',
    question: 'Can our developers collaborate directly with your testers?',
    answer:
      'Yes, our platform enables direct communication between your development team and our security experts. Ask questions, validate fixes, request re-tests, and get remediation guidance in real-time.',
  },
];


  return (
    <section className="w-full py-16 bg-white">
      <div className="max-w-6xl mx-auto px-4">
        <div className="flex flex-col md:flex-row md:gap-20 gap-10">
          {/* Left: Section Heading */}
          <div className="md:w-1/3 w-full flex items-center justify-center">
            <h2 className="text-4xl md:text-6xl font-bold text-[#1e83fb] text-center">
              FAQ
            </h2>
          </div>

          {/* Right: FAQ Items */}
          <div className="md:w-2/3 w-full flex flex-col justify-center">
            {faqs.map((faq) => (
              <FAQItem
                key={faq.id}
                uniqueId={faq.id}
                question={faq.question}
                answer={faq.answer}
                isOpen={openItem === faq.id}
                onToggle={() => handleToggle(faq.id)}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;