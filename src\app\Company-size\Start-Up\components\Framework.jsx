import React from "react";
import { Shield, Zap, TrendingUp } from "lucide-react";

export default function CTBSimpleSection() {
  return (
    <div className="bg-[#F8F5F7] pt-8 pb-14 px-4 sm:px-6">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-6 sm:p-8 relative overflow-hidden">
          {/* Decorative gradients */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-blue-100 to-transparent rounded-full -translate-y-16 translate-x-16"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-purple-100 to-transparent rounded-full translate-y-12 -translate-x-12"></div>

          <div className="relative z-10">
            {/* Header Text */}
            <div className="text-center mb-8">
              <h2 className="text-3xl sm:text-4xl font-bold text-blue-600 mb-4">
                Move faster,
                <span className="ml-2">ship safer</span>
              </h2>
              <p className="text-base sm:text-lg text-slate-600 max-w-2xl mx-auto leading-relaxed">
                Startups choose Capture The Bug to unlock enterprise deals, streamline security reviews, and prove trust-faster.
                Whether you&apos;re raising, selling, or scaling, we&apos;ll help you hit your next milestone with security you can show.
              </p>
            </div>

            {/* Icons Section */}
            <div className="flex flex-col sm:flex-row justify-center items-center gap-6 sm:gap-8 mt-8">
              <div className="flex flex-col items-center group">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors">
                  <Zap className="h-8 w-8 text-green-600" />
                </div>
                <span className="text-sm text-slate-600 mt-2 font-medium">Speed</span>
              </div>

              {/* Divider */}
              <div className="hidden sm:block w-px h-16 bg-slate-200"></div>
              <div className="block sm:hidden w-16 h-px bg-slate-200"></div>

              <div className="flex flex-col items-center group">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors">
                  <Shield className="h-8 w-8 text-green-600" />
                </div>
                <span className="text-sm text-slate-600 mt-2 font-medium">Security</span>
              </div>

              {/* Divider */}
              <div className="hidden sm:block w-px h-16 bg-slate-200"></div>
              <div className="block sm:hidden w-16 h-px bg-slate-200"></div>

              <div className="flex flex-col items-center group">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors">
                  <TrendingUp className="h-8 w-8 text-green-600" />
                </div>
                <span className="text-sm text-slate-600 mt-2 font-medium">Growth</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
