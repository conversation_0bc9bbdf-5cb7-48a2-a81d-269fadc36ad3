import Image from 'next/image';
import React from 'react';

const Teams = () => {
  return (
    <div className="mx-auto md:p-20 p-10 bg-gray-50">
    <div className="space-y-12">
      <div className="flex flex-col md:flex-row gap-8">
        <div className="md:w-1/2 space-y-4">
          <h2 className="text-sm font-semibold text-gray-600 uppercase">Expert Pentester Teams
          </h2>
          <h1 className="text-3xl font-bold text-blue-700">Get the Specialized Team Your Apps Deserve</h1>
          <p className="text-gray-600">
          Unlike other providers that use a one-size-fits-all approach, we leverage AI to handpick skilled and motivated pentesters tailored to your specific needs. Our curated teams ensure high-quality results, providing targeted expertise for your unique environment and assets.
          </p>
        </div>
        <div className="md:w-1/2 flex items-center justify-center h-64 md:h-auto">
        <Image src="/images/android-app-2-finalized.jpg" width={500} height={500} alt="" />
        </div>
      </div>
      
      <div className="flex flex-col md:flex-row gap-8">
        <div className="md:w-1/2 flex items-center justify-center h-64 md:h-auto">
        <Image src="/images/android-app-3.png" width={500} height={500} alt="" />
        </div>
        <div className="md:w-1/2 space-y-4">
          <h2 className="text-sm font-semibold text-gray-600 uppercase">Real-Time Penetration Test Dashboard
          </h2>
          <h1 className="text-3xl font-bold text-blue-700">Track Results as They Happen</h1>
          <p className="text-gray-600">
          Stay informed throughout the entire testing process. Our real-time dashboard lets you monitor prioritized findings, action items, and pentester progress 24/7. No more waiting in the dark-download your final report as soon as it&apos;s ready and stay ahead with continuous updates. All of this is accessible directly from our user-friendly platform, ensuring you&apos;re always in the loop.
          </p>
        </div>
      </div>
    </div>
  </div>
  );
};

export default Teams;