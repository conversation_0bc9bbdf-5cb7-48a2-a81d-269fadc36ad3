"use client"
import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

export default function VulnerabilityTrendsChart() {
  const [hoveredPoint, setHoveredPoint] = useState(null);
  const [viewMode, setViewMode] = useState('monthly');
  const [animateLine, setAnimateLine] = useState(false);
  const chartRef = useRef(null);
  const [isHovering, setIsHovering] = useState(false);

  // Trigger periodic animations
  useEffect(() => {
    const animationInterval = setInterval(() => {
      setAnimateLine(true);
      setTimeout(() => setAnimateLine(false), 1500);
    }, 8000);
    
    return () => clearInterval(animationInterval);
  }, []);

  const weeklyData = [
    { x: 0, y: 5, date: 'Mon' },
    { x: 1, y: 8, date: 'Tue' },
    { x: 2, y: 12, date: 'Wed' },
    { x: 3, y: 10, date: 'Thu' },
    { x: 4, y: 15, date: 'Fri' },
    { x: 5, y: 12, date: 'Sat' },
    { x: 6, y: 8, date: 'Sun' }
  ];
  
  const monthlyData = [
    { x: 0, y: 8, date: 'Jan' },
    { x: 1, y: 12, date: 'Feb' },
    { x: 2, y: 20, date: 'Mar' },
    { x: 3, y: 25, date: 'Apr' },
    { x: 4, y: 22, date: 'May' },
    { x: 5, y: 28, date: 'Jun' }
  ];

  const currentData = viewMode === 'weekly' ? weeklyData : monthlyData;
  const maxY = Math.max(...currentData.map(d => d.y)) + 5;
  const chartWidth = 280;
  const chartHeight = 100;
  
  // Function to handle mode switch with animation
  const handleModeSwitch = (mode) => {
    if (mode === viewMode) return;
    setHoveredPoint(null);
    setAnimateLine(true);
    setViewMode(mode);
    setTimeout(() => setAnimateLine(false), 1500);
  };
  
  // Convert data points to SVG path
  const generateSmoothPath = (data) => {
    if (data.length < 2) return '';
    
    const points = data.map(d => ({
      x: (d.x / (data.length - 1)) * chartWidth,
      y: chartHeight - (d.y / maxY) * chartHeight
    }));
    
    let path = `M ${points[0].x},${points[0].y}`;
    
    for (let i = 0; i < points.length - 1; i++) {
      const x1 = points[i].x;
      const y1 = points[i].y;
      const x2 = points[i + 1].x;
      const y2 = points[i + 1].y;
      
      // Calculate control points for smooth curve
      const cpx1 = x1 + (x2 - x1) / 3;
      const cpy1 = y1;
      const cpx2 = x1 + 2 * (x2 - x1) / 3;
      const cpy2 = y2;
      
      path += ` C ${cpx1},${cpy1} ${cpx2},${cpy2} ${x2},${y2}`;
    }
    
    return path;
  };
  
  const pathString = generateSmoothPath(currentData);

  return (
    <div className="w-full h-full flex flex-col items-center justify-center relative">
      {/* Toggle switch */}
      <div className="bg-gray-100 rounded-full p-0.5 flex items-center shadow-sm mb-2 text-xs">
        <motion.button
          onClick={() => handleModeSwitch('weekly')}
          className={`px-3 py-1 rounded-full transition-all relative ${
            viewMode === 'weekly' 
              ? 'bg-primary-blue text-white shadow-sm' 
              : 'text-gray-500 hover:text-gray-700'
          }`}
          whileHover={{ scale: viewMode !== 'weekly' ? 1.05 : 1 }}
          whileTap={{ scale: 0.95 }}
        >
          Weekly
          {viewMode === 'weekly' && (
            <motion.div
              className="absolute bottom-0 left-0 right-0 h-0.5 bg-white rounded-full"
              layoutId="activeIndicator"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            />
          )}
        </motion.button>
        <motion.button
          onClick={() => handleModeSwitch('monthly')}
          className={`px-3 py-1 rounded-full transition-all relative ${
            viewMode === 'monthly' 
              ? 'bg-primary-blue text-white shadow-sm' 
              : 'text-gray-500 hover:text-gray-700'
          }`}
          whileHover={{ scale: viewMode !== 'monthly' ? 1.05 : 1 }}
          whileTap={{ scale: 0.95 }}
        >
          Monthly
          {viewMode === 'monthly' && (
            <motion.div
              className="absolute bottom-0 left-0 right-0 h-0.5 bg-white rounded-full"
              layoutId="activeIndicator"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            />
          )}
        </motion.button>
      </div>

      {/* Chart container */}
      <motion.div 
        className="relative bg-white rounded-lg overflow-hidden w-full"
        ref={chartRef}
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => {
          setIsHovering(false);
          setHoveredPoint(null);
        }}
        whileHover={{ scale: 1.02 }}
        transition={{ type: "spring", stiffness: 400, damping: 10 }}
      >
        {/* Chart */}
        <svg width={chartWidth} height={chartHeight} className="mx-auto">
          {/* Light horizontal grid lines */}
          {[0, 1, 2].map((i) => (
            <motion.line
              key={i}
              x1="0"
              y1={i * (chartHeight / 2)}
              x2={chartWidth}
              y2={i * (chartHeight / 2)}
              stroke="#f0f0f0"
              strokeWidth="1"
              initial={{ opacity: 0, x2: 0 }}
              animate={{ opacity: 1, x2: chartWidth }}
              transition={{ duration: 0.8, delay: i * 0.1 }}
            />
          ))}
          
          {/* Chart line with animated gradient */}
          <defs>
            <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <motion.stop 
                offset="0%" 
                stopColor="#0835A7" 
                stopOpacity="1"
                animate={animateLine ? {
                  stopOpacity: [1, 0.5, 1],
                } : {}}
                transition={{ duration: 1.5, ease: "easeInOut" }}
              />
              <motion.stop 
                offset="50%" 
                stopColor="#001B71" 
                stopOpacity="1"
                animate={animateLine ? {
                  stopOpacity: [1, 0.8, 1],
                } : {}}
                transition={{ duration: 1.5, ease: "easeInOut" }}
              />
              <motion.stop 
                offset="100%" 
                stopColor="#0835A7" 
                stopOpacity="1"
                animate={animateLine ? {
                  stopOpacity: [1, 0.5, 1],
                } : {}}
                transition={{ duration: 1.5, ease: "easeInOut" }}
              />
            </linearGradient>
          </defs>
          
          {/* Path background glow for animation */}
          {animateLine && (
            <motion.path
              d={pathString}
              fill="none"
              stroke="#0835A7"
              strokeWidth="6"
              strokeLinecap="round"
              opacity="0.15"
              strokeDasharray="283"
              strokeDashoffset="283"
              animate={{ strokeDashoffset: 0 }}
              transition={{ duration: 1.5, ease: "easeOut" }}
              className="blur-[3px]"
            />
          )}
          
          {/* Main path */}
          <motion.path
            d={pathString}
            fill="none"
            stroke={animateLine ? "url(#lineGradient)" : "#0835A7"}
            strokeWidth="2.5"
            strokeLinecap="round"
            initial={{ pathLength: 0 }}
            animate={{ 
              pathLength: 1, 
              strokeWidth: animateLine ? [2.5, 3.5, 2.5] : 2.5 
            }}
            transition={{ 
              pathLength: { duration: 1.5, ease: "easeInOut" },
              strokeWidth: { duration: 1.5, ease: "easeInOut" }
            }}
          />
          
          {/* Area under the curve for hover effect */}
          <motion.path
            d={`${pathString} L ${chartWidth},${chartHeight} L 0,${chartHeight} Z`}
            fill="url(#lineGradient)"
            opacity={isHovering ? 0.05 : 0}
            transition={{ duration: 0.3 }}
          />
          
          {/* Data points */}
          {currentData.map((d, i) => {
            const x = (d.x / (currentData.length - 1)) * chartWidth;
            const y = chartHeight - (d.y / maxY) * chartHeight;
            const isActive = hoveredPoint === i;
            
            return (
              <g key={i}>
                {/* Highlight point on hover */}
                {isActive && (
                  <motion.circle
                    cx={x}
                    cy={y}
                    r="8"
                    fill="#0835A7"
                    opacity="0.2"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0 }}
                    transition={{ duration: 0.2 }}
                  />
                )}
                
                {/* Pulsing animation for active point */}
                {isActive && (
                  <motion.circle
                    cx={x}
                    cy={y}
                    r="12"
                    stroke="#0835A7"
                    strokeWidth="1"
                    fill="none"
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ 
                      scale: [0.8, 1.2, 0.8],
                      opacity: [0, 0.3, 0]
                    }}
                    transition={{ 
                      duration: 1.5,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                )}
                
                {/* Data point circle */}
                <motion.circle
                  cx={x}
                  cy={y}
                  r={isActive ? 6 : 4}
                  fill={isActive ? "#001B71" : "#0835A7"}
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{ 
                    scale: 1, 
                    opacity: 1,
                    y: isActive ? y - 2 : y
                  }}
                  transition={{ 
                    delay: 0.8 + (i * 0.05), 
                    y: { duration: 0.2, type: "spring" }
                  }}
                  onMouseEnter={() => setHoveredPoint(i)}
                  onMouseLeave={() => hoveredPoint === i && setHoveredPoint(null)}
                  className="cursor-pointer"
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                />

                {/* Value indicator above point */}
                <AnimatePresence>
                  {isActive && (
                    <motion.text
                      x={x}
                      y={y - 10}
                      fontSize="10"
                      fontWeight="bold"
                      fill="#001B71"
                      textAnchor="middle"
                      dominantBaseline="middle"
                      initial={{ opacity: 0, y: y - 5 }}
                      animate={{ opacity: 1, y: y - 10 }}
                      exit={{ opacity: 0, y: y - 5 }}
                      transition={{ duration: 0.2 }}
                    >
                      {d.y}
                    </motion.text>
                  )}
                </AnimatePresence>
              </g>
            );
          })}
        </svg>
        
        {/* X-Axis labels */}
        <div className="flex justify-between px-1 text-[10px] text-gray-400 mt-1">
          {currentData.map((d, i) => (
            <motion.div 
              key={i}
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1 + (i * 0.05) }}
              className={`${hoveredPoint === i ? 'font-medium text-gray-600' : ''}`}
            >
              {d.date}
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
} 