"use client";

import { useEffect } from 'react';
import { 
  getCookieConsent, 
  initializeAnalytics, 
  initializeMarketingTools,
  disableMicrosoftClarity,
  disableAllTracking
} from '../../utils/cookieConsent.js';

const CookieInitializer = () => {
  useEffect(() => {
    // Check if we're in production environment
    const isProduction = 
      typeof window !== 'undefined' && 
      !window.location.hostname.includes('localhost') && 
      !window.location.hostname.includes('127.0.0.1');
    
    // Always disable all tracking in development environments
    disableAllTracking();
    
    // Skip all analytics in development/localhost environment
    if (!isProduction) {
      console.log('Analytics and tracking disabled in development environment');
      return;
    }
    
    const consentType = getCookieConsent();
    
    // Initialize tools based on existing consent when the app loads
    if (consentType === 'all') {
      initializeAnalytics();
      initializeMarketingTools();
      
      // Handle scripts with data-consent-required attribute
      const analyticsScripts = document.querySelectorAll('[data-consent-required="analytics"]');
      analyticsScripts.forEach(script => {
        if (script.getAttribute('src')) {
          // External script
          const newScript = document.createElement('script');
          [...script.attributes].forEach(attr => {
            if (attr.name !== 'data-consent-required') {
              newScript.setAttribute(attr.name, attr.value);
            }
          });
          document.head.appendChild(newScript);
        } else {
          // Inline script
          eval(script.innerHTML);
        }
      });
      
      // Also add the GTM noscript iframe if consent is given
      const noscriptContainer = document.createElement('noscript');
      const iframe = document.createElement('iframe');
      iframe.src = "https://www.googletagmanager.com/ns.html?id=GTM-MMMXC8Z";
      iframe.height = "0";
      iframe.width = "0";
      iframe.style.display = "none";
      iframe.style.visibility = "hidden";
      noscriptContainer.appendChild(iframe);
      document.body.prepend(noscriptContainer);
    }
    
    // Setup dataLayer for GTM to respect consent settings even if GTM is already loaded
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      'event': 'cookie_consent_update',
      'cookie_consent': consentType
    });
    
  }, []);

  return null; // This component doesn't render anything
};

export default CookieInitializer;