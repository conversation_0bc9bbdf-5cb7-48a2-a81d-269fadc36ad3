"use client";

import Button from "@/app/common/buttons/Button";
import Image from "next/image";
import Link from "next/link";
import {
  Lock,
  Package,
  Unlock,
  Repeat,
  Smartphone,
  Shield,
  TestTube,
} from "lucide-react";


export default function Section() {
  return (
    <div className="relative min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 overflow-hidden">
       
      <div className="relative z-10 container mx-auto md:px-28 pt-16 md:pt-4 pb-16">
        
        <div className="flex flex-col lg:flex-row items-center gap-12 lg:gap-16 xl:gap-20 min-h-[80vh]">
           
          <div className="flex-1 text-center lg:text-left max-w-2xl space-y-8">
             
            <div className="space-y-4">
              <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold leading-tight">
                Why Mobile Apps Are a 
              </h1>
              <span className="text-2xl sm:text-3xl md:text-4xl font-bold   text-blue-700 bg-clip-text leading-tight block sm:inline">
                Top Target
              </span>
            </div> 
            <p className="text-slate-600 text-sm sm:text-base md:text-lg  leading-relaxed  ">
              Mobile apps are rich with attack surface-<span className="font-semibold text-slate-800 bg-yellow-100 px-2 py-1 rounded-md">insecure data storage, exposed APIs, broken auth, and misused permissions</span>, 
are just the start. With 5G connectivity and billions of global users, attackers increasingly target mobile apps for financial fraud, account takeover, and API abuse.   <br/>          
You ship on iOS and Android. We test both-natively, manually, and thoroughly. 
</p> 
          </div> 
          <div className="flex-1 w-full max-w-3xl relative">
            <div className="md:px-16 -mt-5">
              <Image
                src="/images/mobileapp.png"
                alt="Internal Network Pentesting Dashboard"
                width={1440} 
                height={100} 
                className="md:w-full w-[600px] scale-100 rounded-xl"
                priority
                quality={80} 
              />
            </div>
          </div>
        </div>
      </div>

      <div className="relative z-10 container mx-auto px-4 sm:px-6 md:px-24 pt-8 sm:pt-12 md:pt-16 lg:pt-24 xl:pt-0 sm:pb-20">
        
        <div className="flex flex-col lg:flex-row items-center gap-8 sm:gap-10 md:gap-12 lg:gap-16 xl:gap-20 min-h-[80vh]">
          
          {/* Left side image */}
          <div className="flex-1 w-full max-w-3xl relative">
            <div className="px-4 sm:px-8 md:px-12 lg:px-8 xl:px-16">
              <Image
                src="/images/SOC.svg"
                alt="Common api"
                width={1440} 
                height={100} 
                className="md:w-full w-[600px] h-auto md:scale-100 scale-90 rounded-xl"
                priority
                quality={80} 
              />
            </div>
          </div>

          {/* Right content */}
          <div className="flex-1 text-center lg:text-left max-w-2xl space-y-6 mb-10 sm:mb-0 sm:space-y-8">
            
            <div>
              <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold leading-tight">
                Common Mobile Vulnerabilities
              </h1>
              <span className="text-2xl sm:text-3xl md:text-4xl font-bold  text-blue-700 bg-clip-text leading-tight block sm:inline">
                iOS & Android Threats We Test
              </span> 
            </div>

            <ul className="space-y-3 ">
              {[
                {
                  icon: Lock,
                  text: "Insecure Authentication & Session Management",
                },
                {
                  icon: Package,
                  text: "Insecure Data Storage (Shared Prefs, Keychain, SQLite)",
                },
                {
                  icon: Unlock,
                  text: "Reverse Engineering & Code Tampering",
                },
                {
                  icon: Repeat,
                  text: "Insecure API Integration (Auth headers, token leakage)",
                },
                {
                  icon: Smartphone,
                  text: "Insecure Inter-Process Communication (Intent abuse, Broadcast leak)",
                },
                {
                  icon: Shield,
                  text: "Weak Encryption & Certificate Pinning Issues",
                },
                {
                  icon: TestTube,
                  text: "OWASP Mobile Top 10 Coverage",
                },
              ].map(({ icon: Icon, text }, idx) => (
                <li
                  key={idx}
                  className="flex items-center gap-3 sm:gap-4 p-3 sm:p-4 rounded-xl bg-white/80 hover:bg-blue-50 transition-colors cursor-default shadow-sm"
                >
                  <Icon className="w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 flex-shrink-0 text-blue-600  text-sm sm:text-base md:text-sm lg:text-sm" />
                  <span className="text-slate-600 text-sm sm:text-base md:text-lg leading-relaxed">{text}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
