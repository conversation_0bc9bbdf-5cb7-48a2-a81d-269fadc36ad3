"use client;"
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { ArrowRight } from "lucide-react";
import { motion } from "framer-motion";
import Button from "../../../common/buttons/Button";

export default function LandingSection() {
  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="bg-[#F8F5F7]  "
    >
      {/* Main 50/50 Split Section */}
      <div className="container mx-auto px-4 md:px-24">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center min-h-[600px] py-16 sm:py-24 lg:py-26">
          
          {/* Left Content - 50% */}
          <motion.div 
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="flex flex-col gap-6 max-w-xl"
          >
            <motion.h1 
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-3xl md:text-4xl lg:text-5xl font-bold text-ctb-blue-150 leading-tight"
            >
             Penetration Testing That  <span className="text-primary-blue ">Scales With You </span>
            </motion.h1>

            <motion.p 
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-[#7B6B9C] text-base md:text-lg leading-relaxed"
            >
Modern PTaaS for teams who ship fast, secure faster, and stay audit-ready.            </motion.p>

            <motion.p 
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.35 }}
              className="text-[#7B6B9C] text-base md:text-lg leading-relaxed"
            >
              Our PTaaS platform gives you continuous access to security experts, real-time findings, and compliance-ready reports that help you win SOC 2, ISO 27001, HIPAA, and customer trust-faster.
            </motion.p>

            <motion.div 
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="flex justify-start"
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  href="/Request-Demo"
                  variant="success"
                  size="lg"
                  rightIcon={
                    <motion.div
                      animate={{ x: [0, 4, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <ArrowRight className="h-5 w-5" />
                    </motion.div>
                  }
                >
                  Request a demo
                </Button>
              </motion.div>
            </motion.div>
          </motion.div>

          {/* Right Image - 50% */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="flex justify-center   lg:justify-end"
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="w-full max-w-[600px]   "
            >
              <Image
                src="/images/product-dash2.svg"
                alt="Large Display"
                width={1120}  
                height={980} 
                className="w-full h-auto rounded-3xl  bg-transparent  "  
                priority
                quality={90} 
              />
            </motion.div>
          </motion.div>

        </div>
      </div>
 
    </motion.div>
  );
}