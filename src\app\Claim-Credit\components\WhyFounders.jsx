// WhyFounders.jsx

"use client";
import React from 'react';
import { ShieldCheckIcon } from '@heroicons/react/24/solid';

const WhyFounders = () => {
  const points = [
    {
      title: "$1,000 pentest credit",
      description: "Eligible early-stage teams can offset their first test through our Founder Program."
    },
    {
      title: "Manual, real-time pentesting",
      description: "Impact-focused pentesting - no noisy automation."
    },
    {
      title: "SOC 2, ISO 27001, HIPAA-ready reports",
      description: "Audit-friendly outputs that help you close deals and win trust."
    },
    {
      title: "Retesting & fix guidance included",
      description: "Recheck key issues and get prioritized support without new contracts."
    },
    {
      title: "Flexible test scheduling",
      description: "Run tests on your terms - before launches, after pushes, or pre-audit."
    },
    {
      title: "Built to move fast",
      description: "Simple onboarding, no red tape, and results in days - not weeks."
    }
  ];
  const closingStatement = "Startups from SaaS, fintech, healthtech, and AI choose Capture The Bug to build trust early and scale securely.";

  return (
    <section className="w-full py-20 bg-slate-900 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-tertiary-blue"></div>
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-1 rounded-full bg-ctb-blue-350 text-ctb-blue-0 text-sm font-medium mb-4">
            Trusted by Founders
          </span>
          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6">
            Why Founders Choose Us
          </h2>
        </div>
        
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {points.map((point, index) => (
            <div 
              key={index} 
              className="group relative bg-slate-800/50 backdrop-blur-sm rounded-2xl p-6 border border-slate-700/50 hover:border-ctb-blue-350 transition-all duration-300"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/0 to-purple-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative flex flex-col items-center text-center">
                <ShieldCheckIcon className="h-8 w-8 text-ctb-light-blue mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">{point.title}</h3>
                <p className="text-slate-300">{point.description}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <div className="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-ctb-blue-350 to-tertiary-blue rounded-full border border-ctb-light-blue backdrop-blur-sm">
            <div className="flex items-center space-x-2">
              <span className="w-2 h-2 rounded-full bg-blue-900 animate-pulse"></span>
              <p className="text-lg text-slate-200 font-medium">
                {closingStatement}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyFounders;
