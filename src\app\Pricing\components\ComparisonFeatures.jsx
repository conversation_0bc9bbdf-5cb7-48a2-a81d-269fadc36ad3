"use client";

import React from 'react';

const comparisonFeatures = [
  {
    feature: "Initial 3-Week Comprehensive Pentest",
    starter: true,
    growth: true,
    enterprise: true
  },
  {
    feature: "Monthly Pentest Days",
    starter: "1 / mo",
    growth: "2 / mo", 
    enterprise: "4+ / mo"
  },
  {
    feature: "Real-time CTB Dashboard",
    starter: true,
    growth: true,
    enterprise: true
  },
  {
    feature: "Dedicated Pentester Chat",
    starter: true,
    growth: true,
    enterprise: true
  },
  {
    feature: "User Seats",
    starter: "5",
    growth: "5",
    enterprise: "5"
  },
  {
    feature: "Quarterly Executive Snapshot",
    starter: true,
    growth: true,
    enterprise: true
  },
  {
    feature: "Retest Window (Week 5)",
    starter: true,
    growth: true,
    enterprise: true
  },
  {
    feature: "Quarterly Threat-Model Workshop",
    starter: false,
    growth: false,
    enterprise: true
  },
  {
    feature: "CI/CD Integration Session",
    starter: false,
    growth: false,
    enterprise: true
  }
];

const CheckIcon = () => (
  <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
  </svg>
);

const MinusIcon = () => (
  <span className="text-gray-400 text-xl">-</span>
);

export default function FeatureComparison() {
  return (
    <section className="w-full py-16 sm:py-20 md:py-24 bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-semibold text-[#06258d] mb-6">
            Compare Plan Features
          </h2>
          <p className="text-lg sm:text-xl text-gray-900 max-w-3xl mx-auto leading-relaxed">
            Choose the perfect plan for your security needs. All plans include our core penetration testing services with varying levels of ongoing support.
          </p>
        </div>

        {/* Desktop Table */}
        <div className="hidden lg:block">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
            <table className="w-full">
              {/* Table Header */}
              <thead>
                <tr className="bg-blue-50">
                  <th className="px-6 py-4 text-left text-lg font-semibold text-[#06258d] align-middle">
                    Features
                  </th>
                  <th className="px-6 py-4 text-center text-lg font-semibold text-[#027bfd] align-middle">
                    Starter
                  </th>
                  <th className="px-6 py-4 text-center text-lg font-semibold text-[#06258d] align-middle">
                    Growth 
                  </th>
                  <th className="px-6 py-4 text-center text-lg font-semibold text-[#027bfd] align-middle">
                    Enterprise
                  </th>
                </tr>
              </thead>
              
              {/* Table Body */}
              <tbody className="divide-y divide-gray-200">
                {comparisonFeatures.map((item, index) => (
                  <tr key={index} className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 text-gray-900 font-medium align-middle">
                      {item.feature}
                    </td>
                    <td className="px-6 py-4 text-center align-middle">
                      <div className="flex justify-center items-center">
                        {typeof item.starter === 'boolean' ? (
                          item.starter ? <CheckIcon /> : <MinusIcon />
                        ) : (
                          <span className="text-gray-700 font-medium">{item.starter}</span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 text-center bg-blue-25 align-middle">
                      <div className="flex justify-center items-center">
                        {typeof item.growth === 'boolean' ? (
                          item.growth ? <CheckIcon /> : <MinusIcon />
                        ) : (
                          <span className="text-gray-700 font-medium">{item.growth}</span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 text-center align-middle">
                      <div className="flex justify-center items-center">
                        {typeof item.enterprise === 'boolean' ? (
                          item.enterprise ? <CheckIcon /> : <MinusIcon />
                        ) : (
                          <span className="text-gray-700 font-medium">{item.enterprise}</span>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Mobile Cards */}
        <div className="lg:hidden space-y-6">
          {comparisonFeatures.map((item, index) => (
            <div key={index} className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
              <div className="px-6 py-4 bg-blue-50 border-b">
                <h3 className="font-semibold text-gray-900">{item.feature}</h3>
              </div>
              <div className="divide-y divide-gray-200">
                <div className="px-6 py-3 flex justify-between items-center">
                  <span className="text-gray-700">Starter</span>
                  <div className="flex items-center">
                    {typeof item.starter === 'boolean' ? (
                      item.starter ? <CheckIcon /> : <MinusIcon />
                    ) : (
                      <span className="text-gray-700 font-medium">{item.starter}</span>
                    )}
                  </div>
                </div>
                <div className="px-6 py-3 flex justify-between items-center bg-blue-25">
                  <span className="text-gray-700 font-medium">Growth</span>
                  <div className="flex items-center">
                    {typeof item.growth === 'boolean' ? (
                      item.growth ? <CheckIcon /> : <MinusIcon />
                    ) : (
                      <span className="text-gray-700 font-medium">{item.growth}</span>
                    )}
                  </div>
                </div>
                <div className="px-6 py-3 flex justify-between items-center">
                  <span className="text-gray-700">Enterprise</span>
                  <div className="flex items-center">
                    {typeof item.enterprise === 'boolean' ? (
                      item.enterprise ? <CheckIcon /> : <MinusIcon />
                    ) : (
                      <span className="text-gray-700 font-medium">{item.enterprise}</span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
