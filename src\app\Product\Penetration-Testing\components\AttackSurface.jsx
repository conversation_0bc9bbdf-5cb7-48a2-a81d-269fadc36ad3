import React from "react";
import Button from "@/app/common/buttons/Button";

const BuiltForScale = () => {
  const features = [
    {
      title: "Guided remediation",
      description: " We go beyond reports. Our team works with yours to resolve issues quickly and confidently. ",
      linkText: "Explore Remediation →",
      href: "/Services/Web-app#guided-remediation",
      icon: (
        <svg className="w-10 h-10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M20 10C20 14.4183 16.4183 18 12 18C7.58172 18 4 14.4183 4 10C4 5.58172 7.58172 2 12 2C16.4183 2 20 5.58172 20 10Z" stroke="currentColor" strokeWidth="2"/>
          <path d="M12 18V22M12 22H15M12 22H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M9 10C9 9.44772 9.44772 9 10 9H10.01C10.5623 9 11.01 9.44772 11.01 10C11.01 10.5523 10.5623 11 10.01 11H10C9.44772 11 9 10.5523 9 10Z" fill="currentColor"/>
          <path d="M13 10C13 9.44772 13.4477 9 14 9H14.01C14.5623 9 15.01 9.44772 15.01 10C15.01 10.5523 14.5623 11 14.01 11H14C13.4477 11 13 10.5523 13 10Z" fill="currentColor"/>
        </svg>
      )
    },
    {
      title: "Pentest in days ",
      description: " Kick off your pentest in under two weeks-no back-and-forth, just fast, ready-to-go security testing. ",
      linkText: "Launch a pentest fast →",
      href: "/Request-Demo",
      icon: (
        <svg className="w-10 h-10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2L3 7V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M12 8V12M12 16H12.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      )
    },
    {
      title: "Automation meets expertise ",
      description: "Automation takes care of the routine. Our security engineers step in for anything that needs a human touch. ",
      linkText: "Explore our methodology → ",
      href: "/How-it-works",
      icon: (
        <svg className="w-10 h-10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      )
    },
    {
      title: "Compliance made easy ",
      description: " From SOC 2 to ISO 27001, we map every control to your audit needs-automatically. ",
      linkText: "Stay audit-ready →",
      href: "/Request-Demo",
      icon: (
        <svg className="w-10 h-10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M17 20H7M17 20C18.1046 20 19 19.1046 19 18V9C19 7.89543 18.1046 7 17 7M17 20V7M7 20C5.89543 20 5 19.1046 5 18V9C5 7.89543 5.89543 7 7 7M7 20V7M7 7V5C7 3.89543 7.89543 3 9 3H15C16.1046 3 17 3.89543 17 5V7M12 11H12.01M12 15H12.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
        </svg>
      )
    }
  ];

  return (
    <div className="py-20 md:py-24 lg:py-32 px-4 sm:px-6 md:px-8 lg:px-12 xl:px-28 bg-tertiary-blue">
      <div className="max-w-8xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16 lg:mb-20">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-[600] leading-tight mb-6">
            <span className="text-white">Engineered for Growth.  </span>
            <span className="text-primary-blue bg-white rounded-md px-3 py-1"> Trusted at Scale.</span>
          </h2>
          <p className="text-white text-lg md:text-xl leading-relaxed max-w-3xl mx-auto">
           From startups to global enterprises, Capture The Bug scales with your security needs - combining powerful technology, real-time visibility, and expert support to keep you secure at every stage. 
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
          {features.map((feature, index) => (
            <div 
              key={index} 
              className="bg-white rounded-xl p-8 h-full flex flex-col transition-all duration-300 ease-in-out border-2 border-transparent hover:-translate-y-2 hover:border-secondary-blue hover:shadow-xl cursor-pointer group relative overflow-hidden"
            >
              {/* Background decoration */}
              <div className="absolute -bottom-20 -right-20 w-48 h-48 bg-gradient-to-tl from-primary-blue/5 to-secondary-blue/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              
              {/* Icon */}
              <div className="mb-6 relative z-10">
                <div className="p-3 rounded-full w-fit bg-gradient-to-br from-primary-blue/10 to-secondary-blue/5 text-secondary-blue group-hover:text-primary-blue transition-colors duration-300">
                  {feature.icon}
                </div>
              </div>
              
              <h3 className="text-xl lg:text-2xl font-semibold text-tertiary-blue mb-4 group-hover:text-primary-blue transition-colors duration-300 relative z-10">
                {feature.title}
              </h3>
              
              <p className="text-tertiary-blue/80 text-base leading-relaxed mb-6 flex-grow relative z-10">
                {feature.description}
              </p>
              
              <div className="mt-auto relative z-10">
                <a 
                  href={feature.href}
                  className="inline-block"
                >
                  <Button
                    variant="secondary"
                    size="sm"
                    rightIcon={
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    }
                  >
                    {feature.linkText}
                  </Button>
                </a>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BuiltForScale;